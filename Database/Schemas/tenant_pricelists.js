const Schema = mongoose.Schema

const TenantPricelistSchema = new Schema({
    price_list_id: {
        type: String,
        required: true,
    },
    name: {
        type: String,
        required: true,
    },
    tenant_id: {
        type: Number,
        ref: 'tenants'
    },
    is_active: {
        type: Boolean,
        required: true
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('tenant_pricelists', TenantPricelistSchema);