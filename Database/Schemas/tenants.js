const { ADD_TENANT_SCREENS, SMS_PROVIDER_INFO } = require('../../Configs/constants')
const ActionSchema = require('./portal_actions_schema');
const Schema = mongoose.Schema;

const UnifonicSMSSchema = new Schema({
    username: {
        type: String,
        required: true,
    },
    password: {
        type: String,
        required: true,
    },
    sender_id: {
        type: String,
        required: true,
    }
})

const CustomSenderIdSchema = new Schema({
    type: {
        type: String,
        default: SMS_PROVIDER_INFO.UNIFONIC
    },
    unifonic_credentials: {
        type: UnifonicSMSSchema
    }
})

const AdvanceLimitSchema = new Schema({
    key: {
        type: String,
        required: true,
    },
    allowance: {
        type: Number,
        required: true
    }
}, { _id: false });

const ServiceSchema = new Schema({
    key: {
        type: String,
        required: true,
    },
    permission: {
        type: ActionSchema,
        required: true
    }
}, { _id: false });

const TenantSchema = new Schema({
    name: {
        type: String,
        required: true
    },
    legal_name: {
        type: String,
        required: true
    },
    street_name: {
        type: String,
        required: true
    },
    country: {
        type: Schema.Types.ObjectId,
        ref: "countries",
        required: true
    },
    timezone: {
        type: String,
    },
    country_code: {
        type: String,
    },
    region: {
        type: Schema.Types.ObjectId,
        ref: "regions",
        required: true
    },
    city: {
        type: Schema.Types.ObjectId,
        ref: "cities",
        required: true
    },
    primary_contact_id: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    linked_tenant_id: [{ //TODO: linked tenants schema changes
        type: Number, // array of tenant ids
        ref: "tenants"
    }],
    subscription_start_date: {
        type: Date,
    },
    subscription_end_date: {
        type: Date,
    },
    services: {
        type: [ServiceSchema],
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true,
    },
    is_active: {
        type: Boolean,
        required: true,
    },
    advance_limit: {
        type: [AdvanceLimitSchema],
        required: true
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    last_branch_id: {
        type: Number, // when a tenant is added, we will make a default branch of the tenant, which will have B01 branch id. this will store the last create branch number
        required: true,
    },
    // sms_credentials: {
    //     type: Schema.Types.ObjectId,
    //     ref: "sms_credentials"
    // },
    enable_sms_services: {
        type: Boolean,
        required: true,
        default: false
    },
    has_custom_sender_id: {
        type: Boolean,
        required: true,
        default: false
    },
    custom_sms_details: {
        type: CustomSenderIdSchema
    },
    customer_counter: {
        type: Number,
        required: true,
        default: 10000
    },
    master_price_id: {
        prefix: {
            type: String,
            default: "PL",
        },
        current_counter: {
            type: Number,
            default: 0,
        },
    },
    deal_counter: {
        type: Number,
        required: true,
        default: 10000
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

TenantSchema.plugin(autoIncrement.plugin, {
    model: 'tenants',
    startAt: 1000,
    incrementBy: 1
});

TenantSchema.index({
    name: 1,
})

module.exports = mongoose.model('tenants', TenantSchema);
