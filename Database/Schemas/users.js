const Schema = mongoose.Schema;

//TODO: need to create (unique_mobile_number => country_code + mobile_number) && email as unique indexes
const UserSchema = new Schema({
    first_name: {
        type: String,
        required: true,
    },
    last_name: {
        type: String,
        required: true,
    },
    email: {
        type: String,
        required: true,
        index: true
    },
    mobile_number: {
        type: Number,
        required: true,
        index: true
    },
    country_code: {
        type: String,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    is_active: {
        type: Boolean,
        required: true
    },
    cognito_username: {
        type: String,
        required: true,
        index: true
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users'
    },
    forgot_password_otp: {
        type: String
    },
    otp_verification_time: {
        type: Date,
    },
    otp_verification_time_limit: {  // in minutes
        type: Number
    },
    profile_pic: {
        type: String
    }
}, {
    timestamps: {
    createdAt: "created_at",
    updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('users', UserSchema);