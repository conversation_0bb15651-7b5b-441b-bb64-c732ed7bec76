const Schema = mongoose.Schema;

const { VALUES } = require('../../Configs/constants');

const DefaultActionsSchema = new Schema({
    view: {
        type: Boolean,
        default: false,
    },
    edit: {
        type: Boolean,
        default: false,
    },
    delete: {
        type: Boolean,
        default: false
    },
    create: {
        type: Boolean,
        default: false,
    }
})

const ModuleSchema = new Schema({
    name: {  // it will be reflected on permission embedded document for the role's collection documents
        // e.g.  { ..., permission: { dashboard: { view: true, edit: true, ... } } }
        type: String,
        required: true
    },
    portal_type: {
        type: String,
        required: true,
        enum: Object.values(VALUES.portals)
    },
    default_actions: {
        type: DefaultActionsSchema,
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("modules", ModuleSchema);