const Schema = mongoose.Schema;

const TenantBranchSchema = new Schema({
    branch_id: {
        type: String,
        required: true,
    },
    tenant_id: {
        type: Number,
        ref: 'tenants'
    },
    name: {
        type: String,
        required: true,
    },
    is_default: {
        type: Boolean,
        required: true,
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    warehouse_counter: {
        type: Number,
        required: true,
        default: 0
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

TenantBranchSchema.index({
    tenant_id: 1,
})

module.exports = mongoose.model('tenant_branches', TenantBranchSchema);