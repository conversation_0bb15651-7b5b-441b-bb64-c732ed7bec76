const mongoose = require("mongoose");

const Schema = mongoose.Schema;

//if no settings found for tenant_id & user_role_id combination, fallback to default values
const UserRolesSettingSchema = new Schema({
    _id: { // tenantId_userRoleId (1119_634015e7a2a6e700126c5638)
        type: String,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    user_role_id: {
        type: mongoose.Types.ObjectId,
        required: true
    },
    default_master_price_id: {
        type: Schema.Types.ObjectId
    },
    out_of_stock: {
        visible: {
            type: Boolean,
            required: true,
            default: true
        },
        searchable: {
            type: Boolean,
            required: true,
            default: false
        },
    },
    price_change: {
        type: Boolean,
        required: true,
        default: true
    },
    preferred_language: {
        type: String,
        trim: true,
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

// ==================== DATA SYNC OPTIMIZED INDEXES ====================

/**
 * @description Index for data sync - UPDATED records with cursor pagination
 * Query: { tenant_id: tenantId, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
 */
UserRolesSettingSchema.index({
    tenant_id: 1,
    updated_at: 1,
    _id: 1
}, {
    name: "user_role_settings_updated_sync_idx",
});

module.exports = mongoose.model('user_role_settings', UserRolesSettingSchema);
