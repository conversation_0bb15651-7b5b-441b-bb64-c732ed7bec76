const Schema = mongoose.Schema;
const GpsCoordinates = new Schema({
    longitude: {
        type: Number,
        required: true,
        default: 0
    },
    latitude: {
        type: Number,
        required: true,
        default: 0
    }
}, {_id: false});


const ShippingAddressSchema = new Schema({
    shipping_address: {
        type: String
    },
    shipping_country_id: {
        type : Schema.Types.ObjectId,
        ref: 'countries',
    },
    shipping_city_id: {
        type : Schema.Types.ObjectId,
        ref: 'cities',
    },
    shipping_region_id: {
        type : Schema.Types.ObjectId,
        ref: 'regions',
    },
    shipping_country_code: {
        type: String
    },
    shipping_mobile_number: {
        type : Number
    },
    gps_coordinates: {
        type: GpsCoordinates
    },
}, {_id: false});

module.exports = { GpsCoordinates, ShippingAddressSchema }