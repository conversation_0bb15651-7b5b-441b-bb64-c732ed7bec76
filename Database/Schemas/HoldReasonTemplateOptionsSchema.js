const Schema = mongoose.Schema;

const {
    HOLD_REASON_TEMPLATES,
} = require('../../Configs/constants');

const OptionSchema = new Schema(
    {
        id: {
            type: Number,
            required: true,
        },
        title: {
            type: String,
            trim: true,
            required: true,
        },
        key: {
            type: String,
            trim: true,
            required: true,
        },
        type: {
            type: String,
            trim: true,
            required: true,
            enum: [
                HOLD_REASON_TEMPLATES.OPTIONS_TYPE.MAP,
                HOLD_REASON_TEMPLATES.OPTIONS_TYPE.INPUT_FIELD
            ],
        },
        module: {
            type: String,
            trim: true,
            required: true,
        },
        is_active: {
            type: Boolean,
            default: true,
        }
    },
    {
        _id: false
    }
)

const HoldReasonTemplateOptionsSchema = new Schema(
    {
        options: {
            type: [OptionSchema],
            required: true
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        },
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

module.exports = mongoose.model('hold_reason_template_options', HoldReasonTemplateOptionsSchema)
