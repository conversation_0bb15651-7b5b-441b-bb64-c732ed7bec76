const CustomerVerificationSchema = new mongoose.Schema(
    {
        user_role_id: {
            type: mongoose.Schema.ObjectId,
            ref: "user_roles",
            required: true,
        },
        country_code: {
            type: String,
            required: true
        },
        mobile_number: {
            type: Number,
            required: true,
        },
        otp: {
            type: Number,
            required: true
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

module.exports = mongoose.model("customer_verifications", CustomerVerificationSchema)
