const { ShippingAddressSchema, GpsCoordinates } = require("./common_schemas");

const Schema = mongoose.Schema;

const CustomerSchema = new Schema({
    first_name: {
        type: String,
    },
    last_name: {
        type: String,
    },
    email: {
        type: String,
    },
    mobile_number: {
        type: Number,
        required: true
    },
    country_code: {
        type: String,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true,
        default: false
    },
    is_active: {
        type: Boolean,
        required: true,
        default: true
    },
    // shipping_address: {
    //     type: ShippingAddressSchema
    // },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users'
    },
    unique_mobile_number: {
        type: String,  // country_code + mobile_number
        required: true,
        unique: true
    },
    // customer common shipping address starts
    shipping_address: {
        type: String
    },
    shipping_country_id: {
        type: Schema.Types.ObjectId,
        ref: 'countries',
    },
    shipping_city_id: {
        type: Schema.Types.ObjectId,
        ref: 'cities',
    },
    shipping_region_id: {
        type: Schema.Types.ObjectId,
        ref: 'regions',
    },
    shipping_country_code: {
        type: String
    },
    shipping_mobile_number: {
        type: Number
    },
    gps_coordinates: {
        type: GpsCoordinates
    },
    // customer common shipping address ends
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

module.exports = mongoose.model("tenant_customers", CustomerSchema);