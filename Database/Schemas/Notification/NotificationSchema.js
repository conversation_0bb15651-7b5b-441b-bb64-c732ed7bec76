const Schema = mongoose.Schema;

const {
    NOTIFICATION
} = require('../../../Configs/constants');

const NotificationSchema = new Schema(
    {
        tenant_id: {
            type: Number,
        },
        user_role_id: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: 'user_roles',
        },
        title: {
            type: String,
            trim: true,
            required: true
        },
        secondary_language_title: {
            type: String,
            trim: true,
        },
        message: {
            type: String,
            trim: true,
            required: true
        },
        secondary_language_message: {
            type: String,
            trim: true,
        },
        center_message: {
            type: String,
            trim: true,
            required: true
        },
        center_secondary_language_message: {
            type: String,
            trim: true,
            required: true
        },
        is_read: {
            type: Boolean,
            required: true,
            default: false
        },
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(NOTIFICATION.TYPE),
        },
        thread_id: {
            type: String,
            trim: true,
        },
        payload_data: {                   
            type: Schema.Types.Mixed,
            required: true,
            /*
                Since Mixed is a schema-less type, you can change the value to anything else you like, but Mongo<PERSON> loses the ability to auto detect and save those changes. To tell <PERSON>goose that the value of a Mixed type has changed, you need to call doc.markModified(path)
                https://mongoosejs.com/docs/schematypes.html#mixed
            */
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

NotificationSchema.index(
    {
        "tenant_id": 1,
        "user_role_id": 1
    },
    { "name": "tenant_id_user_role_id_asc_idx" }
);

NotificationSchema.index(
    {
        "user_role_id": 1,
        "is_read": 1
    },
    { "name": "user_role_id_is_read_asc_idx" }
);

module.exports = mongoose.model('notifications', NotificationSchema);
