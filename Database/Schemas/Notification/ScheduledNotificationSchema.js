const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const {
    NOTIFICATION
} = require('../../../Configs/constants');

const ScheduledNotificationSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        notification_type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(NOTIFICATION.TYPE),
        },
        title: {
            type: String,
            trim: true,
            required: true,
        },
        message: {
            type: String,
            trim: true,
            required: true,
        },
        image_name: {
            type: String,
            trim: true,
        },
        schedule_at: {
            type: Date,
            required: true,
        },
        price_list_ids: {
            type: [mongoose.Schema.Types.ObjectId],
            required: true,
        },
        data: {
            type: Schema.Types.Mixed,
            required: true,
            /*
                Since Mixed is a schema-less type, you can change the value to anything else you like, but Mongo<PERSON> loses the ability to auto detect and save those changes. To tell Mongoose that the value of a Mixed type has changed, you need to call doc.markModified(path)
                https://mongoosejs.com/docs/schematypes.html#mixed
            */
        },
        is_send: {
            type: Boolean,
            required: true,
            default: false,
        },
        created_by: {
            type: Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: Schema.Types.ObjectId,
        },

    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

ScheduledNotificationSchema.index({
    tenant_id: 1,
    schedule_at: -1,
    is_send: -1,
    notification_type: 1
})

module.exports = mongoose.model('scheduled_notifications', ScheduledNotificationSchema);
