const Schema = languageDB.Schema

const LanguageSchema = new Schema({
    language_code: {
        type: String,
        required: true,
    },
    name: {
        type: String,
        required: true,
    },
    key_count: {
        type: Number,
        required: true,
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    enable_rtl: {
        type: Boolean,
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = languageDB.connections.find(connection => connection.$dbName === "languages").model('languages', LanguageSchema);