const Schema = mongoose.Schema;

const ActionSchema = new Schema({
    view: {
        type: Boolean,
        default: false,
        required: true,
    },
    edit: {
        type: Boolean,
        default: false,
        required: true,
    },
    create: {
        type: Boolean,
        default: false,
        required: true
    },
    delete: {
        type: Boolean,
        default: false,
        required: true
    }
}, { _id: false })

module.exports = ActionSchema