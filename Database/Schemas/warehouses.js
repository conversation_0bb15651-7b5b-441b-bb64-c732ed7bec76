const Schema = mongoose.Schema;

const WareHouseSchema = new Schema({

    tenant_id: {
        type: Number,
        required: true,
        ref: 'tenants',
    },
    branch_id: {
        type: Schema.Types.ObjectId,
        ref: "tenant_branches",
        required: true,
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    warehouse_code: {
        type: String,
        required: true,
    },

}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

module.exports = mongoose.model("warehouses", WareHouseSchema);