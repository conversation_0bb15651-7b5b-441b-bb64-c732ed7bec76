const Schema = mongoose.Schema;

const TenantOrderPrefixSchema = new Schema({
    _id: { // `${tenant_id}_${prefix}`
        type: String,
        required: true
    },
    tenant_id: {
        type: Number,
        required: true
    },
    prefix: {
        type: String,
        default: "",
    },
    counter: {
        type: Number,
        required: true,
        default: 100000
    },
    is_current: {
        type: Boolean,
        default: false
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: 'users'
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("tenant_order_prefixes", TenantOrderPrefixSchema);
