const Schema = mongoose.Schema;

const {
    VALUES
} = require('../../Configs/constants');

const UserRoleDeviceSchema = new Schema(
    {
        tenant_id: {
            type: Number,
        },
        user_role_id: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: 'user_roles',
            index: true
        },
        device_token: {
            type: String,
            required: true,
            trim: true
        },
        fcm_token: {
            type: String,
            required: true,
            trim: true
        },
        device_type: {
            type: String,
            enum: Object.values(VALUES.DEVICE_TYPE),
            trim: true,
            required: true
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

module.exports = mongoose.model('user_role_devices', UserRoleDeviceSchema);
