const Schema = mongoose.Schema;

const IntegrationCredentialSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            ref: 'tenants',
            required: true,
        },
        name: {
            type: String,
            trim: true,
            required: true,
        },
        configurations: {
            type: Object,
            required: true
        },
        unique_integration_credential: {
            type: String,
            required: true,
            unique: true
        },
        is_active: {
            type: Boolean,
            default: true,
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        },
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

IntegrationCredentialSchema.index({
    tenant_id: 1,
    is_active: -1
})

module.exports = mongoose.model('integration_credentials', IntegrationCredentialSchema)
