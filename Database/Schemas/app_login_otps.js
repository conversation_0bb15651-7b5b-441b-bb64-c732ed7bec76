const Schema = mongoose.Schema;

const AppLoginOTPSchema = new Schema({
    country_code: {
        type: String,
        required: true
    },
    mobile_number: {
        type: Number,
        required: true
    },
    email: {
        type: String,
        trim: true,
    },
    otp: {
        type: String,
        required: true
    },
    otp_generation_time: {
        type: Date,
        required: true
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("app_login_otps", AppLoginOTPSchema);