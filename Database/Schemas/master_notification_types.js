const Schema = mongoose.Schema;

const DefaultNotificationConfigSchema = new Schema({
    allow_push_notification: {
        type: Boolean,
        required: true
    },
    allow_sms_notification: {
        type: Boolean,
        required: true
    },
    allow_email_notification: {
        type: Boolean,
        required: true
    },
})

const MasterNotificationTypeSchema = new Schema({
    type: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    key: {
        type: String,
        required: true
    },
    default_value: {
        type: DefaultNotificationConfigSchema,
        required: true
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    }, 
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    } 
});

module.exports = mongoose.model('master_notification_types', MasterNotificationTypeSchema);