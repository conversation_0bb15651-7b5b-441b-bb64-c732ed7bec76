const { VALUES } = require('../../Configs/constants');
const Schema = mongoose.Schema;

const UserSessionSchema = new Schema({
    user_id: {
        type: Schema.Types.ObjectId,
        required: true,
        // ref: 'users'
        refPath: "collection_name"
    },
    collection_name: {
        type: String,
        required: true,
        enum: ["users", "tenant_customers"],
        default: "users"
    },
    user_role_id: {
        type: Schema.Types.ObjectId,
        ref: 'user_roles'
    },
    tenant_id: {
        type: Number,
        ref: 'tenants'
    },
    // when a branch related user needs to be assigned to the access_token
    branch_id: {
        type: Schema.Types.ObjectId,
        ref: "tenant_branches",
    },
    has_role_changed: { // this will be set true when user's role is changed for the provided user_role_id
        type: Boolean
    },
    device_token: {
        type: String,
        required: true
    },
    device_type: {
        type: String,
        // required: true
    },
    device_location: {
        type: String,
    },
    access_token: {
        type: String,
        required: true
    },
    refresh_token: {
        type: String,
    },
    start_time: {
        type: Date,
        required: true
    },
    end_time: {
        type: Date,
    },
    status: { // TODO: do not let CLOSED sessions to be refreshed
        type: String,
        required: true,
        enm: [Object.values(VALUES.sessionStatus)],
    },
    // total_session_time_in_sec: { //TODO: FOR MULTIPLE LOGIN ISSUE
    //     type: Number,
    //     // required: true
    // },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

/**
 * @description Followed ESR rule for below compound index
 * @reference https://www.mongodb.com/docs/manual/tutorial/equality-sort-range-rule/#std-label-esr-indexing-rule
 */
UserSessionSchema.index({
    // Equality fields
    status: 1,
    user_role_id: 1,
})

UserSessionSchema.index({
    // Equality fields
    status: 1,
    access_token: 1,
})

UserSessionSchema.index({
    // Equality fields
    user_id: 1,
})

UserSessionSchema.index({
    // Equality fields
    created_at: 1,
})

UserSessionSchema.index({
    // Equality fields
    access_token: 1,
})

module.exports = mongoose.model('user_sessions', UserSessionSchema);
