const ActionSchema = require('./portal_actions_schema');
const Schema = mongoose.Schema;

const TenantServiceSchema = new Schema({
    key: {
        type: String,
        required: true,
    },
    name: {
        type: String,
        required: true,
    },
    description: {
        type: String,
        required: true
    },
    is_custom: {
        type: Boolean,
        required: true
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    default_value: {
        type: ActionSchema,
        required: true,
        default: {view: false, edit: false, delete: false, create: false}
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('tenant_services', TenantServiceSchema);