const Schema = mongoose.Schema;

const CitySchema = new Schema({
    country_id: {
        type: Schema.Types.ObjectId,
        ref: "countries",
        required: true
    },
    region_id: {
        type: Schema.Types.ObjectId,
        ref: "regions",
        required: true
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: {
        type: String,
        required: true,
        trim: true,
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

// ==================== DATA SYNC OPTIMIZED INDEXES ====================

/**
 * @description Index for data sync - CREATED records with cursor pagination
 * Query: { region_id: { $in: regionIds }, is_deleted: false, is_active: true, $or: [{ created_at: { $gt: lastSyncedDate } }, { created_at: lastSyncedDate, _id: { $gt: cursor } }] }
 */
CitySchema.index({
    region_id: 1,
    is_deleted: 1,
    is_active: 1,
    updated_at: 1,
    _id: 1
}, {
    name: "cities_updated_init_sync_idx",
});

/**
 * @description Index for data sync - UPDATED records with cursor pagination
 * Query: { region_id: { $in: regionIds }, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
 */
CitySchema.index({
    region_id: 1,
    updated_at: 1,
    _id: 1
}, {
    name: "cities_updated_sync_idx",
});

module.exports = mongoose.model("cities", CitySchema);
