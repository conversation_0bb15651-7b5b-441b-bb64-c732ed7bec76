const Schema = mongoose.Schema;

const {
    HOLD_REASON_TEMPLATES,
    PRIMITIVE_ROLES
} = require('../../Configs/constants');

const TemplateSchema = new Schema(
    {
        title: {
            type: String,
            trim: true,
            required: true,
        },
        parameters: {
            type: [
                {
                    system_key: {
                        type: String,
                        trim: true,
                        required: true,
                    },
                    integration_key: {
                        type: String,
                        trim: true,
                        required: true,
                    },
                },
                {
                    _id: false
                }
            ],
        },
        language: {
            type: String,
            trim: true,
            required: true,
        },
        template_id: {
            type: String,
            trim: true,
            required: true,
        },
        role: {
            type: String,
            trim: true,
            required: true,
            enum: [
                PRIMITIVE_ROLES.SALES_PERSON,
                PRIMITIVE_ROLES.CUSTOMER
            ],
        },
    },
    {
        _id: false
    }
)

const HoldReasonSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            ref: 'tenants',
            required: true,
        },
        integration_credential_id : {
            type: Schema.Types.ObjectId,
            ref: 'integration_credentials',
        },
        title: {
            type: String,
            trim: true,
            required: true,
        },
        icon: {
            type: String,
            trim: true,
        },
        secondary_language_title: {
            type: String,
            trim: true,
            required: true,
        },
        hold_templates: {
            type: [TemplateSchema],
        },
        release_templates: {
            type: [TemplateSchema],
        },
        is_whatsapp_message_enabled: {
            type: Boolean,
            default: false,
        },
        is_active: {
            type: Boolean,
            default: false,
        },
        created_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        },
        updated_by: {
            type: Schema.Types.ObjectId,
            ref: 'users',
            required: true
        }
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

module.exports = mongoose.model('hold_reasons', HoldReasonSchema)
