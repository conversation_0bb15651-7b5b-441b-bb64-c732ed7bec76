const Schema = mongoose.Schema;

const CountrySchema = new Schema({
    name: {
        type: String,
        required: true,
        trim: true,
    },
    country_code: {
        type: String,
        required: true,
        maxLength: 5,
        trim: true,
    },
    currency: {
        type: String,
        required: true,
        maxLength: 4,
        trim: true,
    },
    alpha_two_code: {
        type: String,
        maxLength: 4,
        required: true,
        trim: true,
    },
    secondary_language_code: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: { // this is full form of language name
        type: String,
        required: true,
        trim: true,
    },
    timezone: {
        type: String,
        required: true,
        trim: true,
    },
    mobile_number_format: {
        type: String,
        required: true,
        trim: true,
    },
    vat: {
        type: Number,
        // required: true
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    google_place_id: {
        type: String,
        required: true,
        trim: true,
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('countries', CountrySchema);
