const Schema = mongoose.Schema;

const NotificationSettingSchema = new Schema({
    notification_type: {
        type: String,
        required: true,
    },
    allow_push_notification: {
        type: Boolean,
        required: true,
        default: false
    },
    allow_sms_notification: {
        type: Boolean,
        required: true,
        default: false
    },
    allow_email_notification: {
        type: Boolean,
        required: true,
        default: false
    },
});

const TenantNotificationConfigSchema = new Schema({
    tenant_id: {
        type: Number,
        ref: 'tenants',
    },
    notifications: {
        type: [NotificationSettingSchema],
        required: true,
    },

    is_default: {
        type: Boolean,
        required: true,
    },
    is_deleted: {
        type: Boolean,
        required: true
    },

    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    }, 
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('tenant_notification_configurations', TenantNotificationConfigSchema);