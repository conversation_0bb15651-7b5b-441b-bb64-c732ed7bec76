const CustomerPaymentTermSchema = new mongoose.Schema(
    {
        number_of_days: {
            type: Number,
            required: true,
        },
        is_active: {
            type: Boolean,
            required: true,
            default: true
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

module.exports = mongoose.model("customer_payment_terms", CustomerPaymentTermSchema)
