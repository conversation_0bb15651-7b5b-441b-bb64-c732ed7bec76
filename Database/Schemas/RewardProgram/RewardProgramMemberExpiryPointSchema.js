const Schema = mongoose.Schema;

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const RewardProgramMemberExpiryPointSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        customer_user_role_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user_roles",
            required: true,
        },
        reward_program_member_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_program_members",
            required: true,
        },
        reward_program_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_programs",
            required: true,
        },
        points: {
            type: Number,
            required: true,
            default: 0
        },
        point_type: {
            type: String,
            required: true,
            trim: true,
            enum: [
                REWARD_PROGRAM.POINT.TYPE.COINS
            ],
        },
        expiry_month: {
            type: Number,
            required: true
        },
        expiry_year: {
            type: Number,
            required: true
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramMemberExpiryPointSchema.index({
    tenant_id: 1,
    reward_program_id: 1,
    reward_program_member_id: 1,
    point_type: 1,
    expiry_year: 1,
    expiry_month: 1,
})

module.exports = mongoose.model('reward_program_member_expiry_points', RewardProgramMemberExpiryPointSchema);
