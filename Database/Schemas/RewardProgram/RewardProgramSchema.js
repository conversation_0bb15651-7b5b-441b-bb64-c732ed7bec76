const { VipPointsRuleSchema, CoinsRuleSchema } = require("./CommonSchema");

const Schema = mongoose.Schema;

const MilestonesInfoSchema = new Schema(
    {
        coins_required: {
            type: Number,
            required: true,
        },
        bonus_coins: {
            type: Number,
            required: true,
        }
    }
)

const VipRulesSchema = new Schema(
    {
        upgrade_points: {
            type: Number,
            required: true,
        },
        renew_points: {
            type: Number,
            required: true,
        },
        expiry_duration: {
            type: Number,
            required: true,
            default: 12 //In month
        }
    },
    {
        _id: false
    }
)

const RewardProgramSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        name: {
            type: String,
            trim: true,
            required: true
        },
        secondary_language_name: {
            type: String,
            trim: true,
            required: true
        },
        milestones: {
            type: [MilestonesInfoSchema],
            required: true,
        },
        vip_rules: {
            type: VipRulesSchema,
            required: true
        },
        base_amount: {
            type: Number,
            default: 100,
            required: true
        },
        classic_member_coin_rules: {
            type: CoinsRuleSchema,
            required: true,
            default: {},
        },
        vip_member_coin_rules: {
            type: CoinsRuleSchema,
            required: true,
            default: {},
        },
        vip_points_rules: {
            type: VipPointsRuleSchema,
            required: true,
            default: {},
        },
        is_active: {
            type: Boolean,
            required: true,
            default: true
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramSchema.index({
    tenant_id: 1,
    is_active: -1,
    createdAt: -1
})

module.exports = mongoose.model('reward_programs', RewardProgramSchema);
