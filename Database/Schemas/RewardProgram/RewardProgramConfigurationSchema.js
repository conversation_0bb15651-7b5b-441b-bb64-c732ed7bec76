const {
    CoinsRuleSchema,
    VipPointsRuleSchema
} = require("./CommonSchema");

const Schema = mongoose.Schema;

const RewardProgramConfigurationSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        base_amount: {
            type: Number,
            default: 100,
            required: true
        },
        classic_member_coin_rules: {
            type: CoinsRuleSchema,
            required: true,
            default: {},
        },
        vip_member_coin_rules: {
            type: CoinsRuleSchema,
            required: true,
            default: {},
        },
        vip_points_rules: {
            type: VipPointsRuleSchema,
            required: true,
            default: {},
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

module.exports = mongoose.model('reward_program_configurations', RewardProgramConfigurationSchema);
