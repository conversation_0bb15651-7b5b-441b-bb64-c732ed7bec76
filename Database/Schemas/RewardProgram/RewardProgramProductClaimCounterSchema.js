const Schema = mongoose.Schema;

const RewardProgramProductClaimCounterSchema = new Schema({
    _id: {
        //tenantId
        type: String,
        required: true,
        trim: true,
    },
    counter: {
        type: Number,
        required: true,
        default: 1000000
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

module.exports = mongoose.model('reward_program_product_claim_counters', RewardProgramProductClaimCounterSchema);
