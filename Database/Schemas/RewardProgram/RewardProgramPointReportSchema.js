const Schema = mongoose.Schema;

const DistributionInfoSchema = new Schema(
    {
        distributed: {
            type: Number,
            required: true,
            default: 0
        },
        expired: {
            type: Number,
            required: true,
            default: 0
        },
        claimed: {
            type: Number,
            required: true,
            default: 0
        },
        closing_balance: {
            type: Number,
            required: true,
            default: 0
        }
    },
    {
        _id: false
    }
)

const StatisticsInfoSchema = new Schema(
    {
        purchase_invoice: {
            type: Number,
            required: true,
            default: 0
        },
        cancellation_invoice: {
            type: Number,
            required: true,
            default: 0
        },
        "0_30_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "31_60_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "61_90_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "91_120_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "120_days_above_payment": {
            type: Number,
            required: true,
            default: 0
        },
        returns_credit_notes: {
            type: Number,
            required: true,
            default: 0
        },
        discount_credit_notes: {
            type: Number,
            required: true,
            default: 0
        },
        daily_access: {
            type: Number,
            required: true,
            default: 0
        },
        member_scan: {
            type: Number,
            required: true,
            default: 0
        },
        manual: {
            type: Number,
            required: true,
            default: 0
        },
        milestones: {
            type: Number,
            required: true,
            default: 0
        },
        vip_upgrade: {
            type: Number,
            required: true,
            default: 0
        },
        vip_renew: {
            type: Number,
            required: true,
            default: 0
        },
        expired_coins: {
            type: Number,
            required: true,
            default: 0
        },
        reward_claim: {
            type: Number,
            required: true,
            default: 0
        },
        reward_claim_refund: {
            type: Number,
            required: true,
            default: 0
        },
    },
    {
        _id: false
    }
)

const RewardProgramPointReportSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        reward_program_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_programs",
            required: true,
        },
        timezone: {
            type: String,
            required: true,
            trim: true
        },
        date: {
            type: Date,
            required: true,
        },
        coins_distribution: {
            type: DistributionInfoSchema,
            required: true,
            default: {},
        },
        vip_points_distribution: {
            type: DistributionInfoSchema,
            required: true,
            default: {},
        },
        coins_statistics: {
            type: StatisticsInfoSchema,
            required: true,
            default: {},
        },
        vip_points_statistics: {
            type: StatisticsInfoSchema,
            required: true,
            default: {},
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramPointReportSchema.index({
    tenant_id: 1,
    date: -1
})

module.exports = mongoose.model('reward_program_point_reports', RewardProgramPointReportSchema);
