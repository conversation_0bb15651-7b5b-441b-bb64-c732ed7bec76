const Schema = mongoose.Schema;

const RewardProgramProductTagSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true
        },
        name: {
            type: String,
            required: true,
            trim: true
        },
        attached_count: {
            type: Number,
            required: true,
            default: 0
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
);

RewardProgramProductTagSchema.index({
    tenant_id: 1
})

module.exports = mongoose.model("reward_program_product_tags", RewardProgramProductTagSchema);
