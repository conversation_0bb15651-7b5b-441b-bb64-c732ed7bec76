const Schema = mongoose.Schema;

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const RewardProgramProductSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        name: {
            type: String,
            trim: true,
            required: true
        },
        secondary_language_name: {
            type: String,
            trim: true,
            required: true
        },
        product_variant_id: {
            type: mongoose.Schema.Types.ObjectId,
        },
        item_number: {
            type: String,
            required: true,
            trim: true,
        },
        unique_item_number: {
            //tenantId_item_number
            type: String,
            required: true,
            trim: true,
            unique: true
        },
        required_coins: {
            type: Number,
            required: true,
        },
        inventory: {
            type: Number,
            required: true,
        },
        tags: [
            {
                type: Schema.Types.ObjectId,
                ref: "reward_program_product_tags"
            }
        ],
        image_name: {
            type: String,
            trim: true,
        },
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(REWARD_PROGRAM.REWARD.TYPE)
        },
        is_featured: {
            type: Boolean,
            required: true,
            default: false
        },
        is_active: {
            type: Boolean,
            required: true,
            default: true
        },
        is_deleted: {
            type: Boolean,
            required: true,
            default: false
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramProductSchema.index({
    tenant_id: 1,
    required_coins: -1,
    reward_program_id: 1,
    createdAt: -1,
})

module.exports = mongoose.model('reward_program_products', RewardProgramProductSchema);
