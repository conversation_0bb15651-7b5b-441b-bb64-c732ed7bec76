const Schema = mongoose.Schema;

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const PointsInfoSchema = new Schema(
    {
        claimed: {
            type: Number,
            required: true,
            default: 0
        },
        remaining: {
            type: Number,
            required: true,
            default: 0
        },
        expired: {
            type: Number,
            required: true,
            default: 0
        }
    },
    {
        _id: false
    }
)

const PaymentHistoryInfoSchema = new Schema(
    {
        month: {
            type: Number,
            required: true,
        },
        year: {
            type: Number,
            required: true,
        },
        on_time_payment: {
            type: Boolean,
            required: true,
        }
    },
    {
        _id: false
    }
)

const StatisticsInfoSchema = new Schema(
    {
        purchase_invoice: {
            type: Number,
            required: true,
            default: 0
        },
        cancellation_invoice: {
            type: Number,
            required: true,
            default: 0
        },
        "0_30_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "31_60_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "61_90_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "91_120_days_payment": {
            type: Number,
            required: true,
            default: 0
        },
        "120_days_above_payment": {
            type: Number,
            required: true,
            default: 0
        },
        returns_credit_notes: {
            type: Number,
            required: true,
            default: 0
        },
        discount_credit_notes: {
            type: Number,
            required: true,
            default: 0
        },
        daily_access: {
            type: Number,
            required: true,
            default: 0
        },
        member_scan: {
            type: Number,
            required: true,
            default: 0
        },
        manual: {
            type: Number,
            required: true,
            default: 0
        },
        milestones: {
            type: Number,
            required: true,
            default: 0
        },
        vip_upgrade: {
            type: Number,
            required: true,
            default: 0
        },
        vip_renew: {
            type: Number,
            required: true,
            default: 0
        },
        expired_coins: {
            type: Number,
            required: true,
            default: 0
        },
        reward_claim: {
            type: Number,
            required: true,
            default: 0
        },
        reward_claim_refund: {
            type: Number,
            required: true,
            default: 0
        },
    },
    {
        _id: false
    }
)

const RewardProgramMemberSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        customer_user_role_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user_roles",
            required: true,
        },
        reward_program_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_programs",
            required: true,
        },
        member_id: {
            type: String,
            required: true,
            trim: true,
        },
        unique_member_id: {
            //tenantId_member_id
            type: String,
            required: true,
            trim: true,
            unique: true
        },
        membership: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE),
            default: REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.CLASSIC
        },
        vip_membership_achieved_at: {
            type: Date,
        },
        vip_membership_expiry: {
            //This will only used when membership turn on manually
            month: {
                type: Number,
            },
            year: {
                type: Number,
            },
        },
        coins: {
            type: PointsInfoSchema,
            required: true,
            default: {},
        },
        vip_points: {
            type: PointsInfoSchema,
            required: true,
            default: {},
        },
        payment_history: {
            //Only store upto 1 year
            type: [PaymentHistoryInfoSchema],
            required: true,
            default: [],
        },
        coins_statistics: {
            type: StatisticsInfoSchema,
            required: true,
            default: {},
        },
        vip_points_statistics: {
            type: StatisticsInfoSchema,
            required: true,
            default: {},
        },
        is_enrolled: {
            type: Boolean,
            required: true,
            default: true
        },
        is_deleted: {
            type: Boolean,
            required: true,
            default: false
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramMemberSchema.pre('save', function (next) {
    if (
        this.membership === REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.VIP &&
        (
            this.isNew ||
            this.isModified('membership')
        )
    ) {
        this.vip_membership_achieved_at = new Date();
    }
    next();
});

RewardProgramMemberSchema.index({
    tenant_id: 1,
    reward_program_id: 1,
    membership: 1,
    is_enrolled: 1,
    createdAt: -1,
})

module.exports = mongoose.model("reward_program_members", RewardProgramMemberSchema)
