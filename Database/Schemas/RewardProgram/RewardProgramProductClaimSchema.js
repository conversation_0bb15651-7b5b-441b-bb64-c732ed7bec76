const Schema = mongoose.Schema;

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const RewardProgramProductClaimSchema = new Schema(
    {
        tenant_id: {
            type: Number,
            required: true,
        },
        reward_program_product_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_program_products",
            required: true,
        },
        reward_program_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_programs",
            required: true,
        },
        reward_program_member_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "reward_program_members",
            required: true,
        },
        customer_user_role_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user_roles",
            required: true,
        },
        claim_number: {
            type: String,
            required: true,
            trim: true,
        },
        unique_claim_number: {
            //tenantId_claim_number
            type: String,
            required: true,
            trim: true,
            unique: true
        },
        claim_coins: {
            type: Number,
            required: true,
        },
        status: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(REWARD_PROGRAM.REWARD.CLAIM.STATUS),
            default: REWARD_PROGRAM.REWARD.CLAIM.STATUS.PENDING
        },
        created_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
    },
    {
        timestamps: {
            createdAt: "created_at",
            updatedAt: "updated_at"
        }
    }
)

RewardProgramProductClaimSchema.index({
    tenant_id: 1,
    updated_by: -1,
    reward_program_id: 1,
    reward_program_product_id: 1,
    reward_program_member_id: 1,
})

module.exports = mongoose.model('reward_program_product_claims', RewardProgramProductClaimSchema);
