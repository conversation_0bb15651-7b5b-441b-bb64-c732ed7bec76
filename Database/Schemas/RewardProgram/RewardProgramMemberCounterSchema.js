const Schema = mongoose.Schema;

const RewardProgramMemberCounterSchema = new Schema({
    _id: {
        //tenantId
        type: String,
        required: true,
        trim: true,
    },
    counter: {
        type: Number,
        required: true,
        default: 100000
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

module.exports = mongoose.model('reward_program_member_counters', RewardProgramMemberCounterSchema);
