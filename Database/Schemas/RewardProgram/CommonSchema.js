const Schema = mongoose.Schema;

const InvoiceCoinsRuleSchema = new Schema(
    {
        purchase: {
            type: Number,
            required: true,
            default: 20,
        },
        cancellation: {
            type: Number,
            required: true,
            default: -20,
        },
    },
    {
        _id: false
    }
)

const PaymentCoinsRuleSchema = new Schema(
    {
        "0_30_days": {
            type: Number,
            required: true,
            default: 80,
        },
        "31_60_days": {
            type: Number,
            required: true,
            default: 50,
        },
        "61_90_days": {
            type: Number,
            required: true,
            default: 30,
        },
        "91_120_days": {
            type: Number,
            required: true,
            default: 10,
        },
        "120_days_above": {
            type: Number,
            required: true,
            default: 0,
        },
    },
    {
        _id: false
    }
)

const CreditNotesCoinsRuleSchema = new Schema(
    {
        returns: {
            type: Number,
            required: true,
            default: -50,
        },
        discounts: {
            type: Number,
            required: true,
            default: -20,
        },
    },
    {
        _id: false
    }
)

const MemberScanActionTypeCoinsRuleSchema = new Schema(
    {
        scan_duration_limit: {
            type: Number,
            required: true,
            default: 20,
        },
        coins: {
            type: Number,
            required: true,
            default: 20,
        }
    },
    {
        _id: false
    }
)

const ActionTypeCoinsRuleSchema = new Schema(
    {
        daily_access: {
            type: Number,
            required: true,
            default: 20,
        },
        member_scan: {
            type: MemberScanActionTypeCoinsRuleSchema,
            required: true,
            default: {},
        }
    },
    {
        _id: false
    }
)

const CoinsRuleSchema = new Schema(
    {
        invoice: {
            type: InvoiceCoinsRuleSchema,
            required: true,
            default: {}
        },
        payment: {
            type: PaymentCoinsRuleSchema,
            required: true,
            default: {},
        },
        credit_notes: {
            type: CreditNotesCoinsRuleSchema,
            required: true,
            default: {},
        },
        action_types: {
            type: ActionTypeCoinsRuleSchema,
            required: true,
            default: {},
        },
    },
    {
        _id: false
    }
)

const PaymentVipPointsRuleSchema = new Schema(
    {
        "0_30_days": {
            type: Number,
            required: true,
            default: 5,
        },
        "31_60_days": {
            type: Number,
            required: true,
            default: 4,
        },
        "61_90_days": {
            type: Number,
            required: true,
            default: 2,
        },
        "91_120_days": {
            type: Number,
            required: true,
            default: 1,
        },
        "120_days_above": {
            type: Number,
            required: true,
            default: 0,
        },
    },
    {
        _id: false
    }
)

const VipPointsRuleSchema = new Schema(
    {
        payment: {
            type: PaymentVipPointsRuleSchema,
            required: true,
            default: {},
        }
    },
    {
        _id: false
    }
)

module.exports = {
    CoinsRuleSchema,
    VipPointsRuleSchema
}
