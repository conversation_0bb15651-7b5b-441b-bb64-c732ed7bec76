const Schema = mongoose.Schema;


const MasterTenantAdvanceSchema = new Schema({
    name: {
        type: String,
        required: true
    },
    description: {
        type: String,
        required: true
    },
    key: {
        type: String,
        required: true
    },
    default_value: {
        type: Number,
        default: 0
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: true
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model('master_tenant_advances', MasterTenantAdvanceSchema);