const UserDeviceSchema = new mongoose.Schema({
    user_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        // ref: 'users' it can refer to users collection or tenant_customers collections
    },
    device_token: {
        type: String,
        required: true
    },
    otp: {
        type: String,
    },
    device_verified: {
        type: Boolean,
        default: false
    },
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

// Create compound index for optimized queries
UserDeviceSchema.index(
    { device_token: 1, user_id: 1 },
    { name: "device_token_user_id_compound_index" }
);

module.exports = mongoose.model('user_devices', UserDeviceSchema);
