const { DATA_SHEET } = require("../../Configs/constants");

const Schema = mongoose.Schema;

const TenantDataSheetSchema = new Schema({

    tenant_id: {
        type: Number,
        required: true,
        ref: 'tenants'
    },
    file_name: {
        type: String,
        required: true
    }, 
    original_file_name: {
        type: String,
        required: true
    },
    data_type: {
        type: String,
        required: true,
        enum: Object.values(DATA_SHEET.DATA_TYPE)
    },
    operation_type: {
        type: String,
        required: true,
        enum: Object.values(DATA_SHEET.OPERATION_TYPE)
    },
    update_type: {
        type: String,
    },
    status: {
        type: String,
        required: true,
        enum: Object.values(DATA_SHEET.STATUS)
    },
    process_start_time: {
        type: Date,
        default: null
    },
    is_deleted: {
        type: Boolean,
        default: false
    },
    created_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },
    updated_by: {
        type : Schema.Types.ObjectId,
        ref: 'users',
        required: true
    },

}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
})

module.exports = mongoose.model("tenant_datasheets", TenantDataSheetSchema);