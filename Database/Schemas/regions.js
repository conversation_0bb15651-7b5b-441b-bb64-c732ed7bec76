const Schema = mongoose.Schema;

const RegionSchema = new Schema({
    country_id: {
        type: Schema.Types.ObjectId,
        ref: "countries",
        required: true,
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    secondary_language_name: {
        type: String,
        required: true,
        trim: true,
    },
    code: {
        type: String,
        trim: true,
    },
    is_active: {
        type: Boolean,
        required: true
    },
    is_deleted: {
        type: Boolean,
        required: false,
    },
    secondary_language_code: {
        type: String,
        trim: true,
        // required: true
    },
    created_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    updated_by: {
        type: Schema.Types.ObjectId,
        ref: "users",
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

// ==================== DATA SYNC OPTIMIZED INDEXES ====================

/**
 * @description Index for data sync - CREATED records with cursor pagination
 * Query: { country_id: countryId, is_deleted: false, is_active: true, $or: [{ created_at: { $gt: lastSyncedDate } }, { created_at: lastSyncedDate, _id: { $gt: cursor } }] }
 */
RegionSchema.index({
    country_id: 1,
    is_deleted: 1,
    is_active: 1,
    updated_at: 1,
    _id: 1
}, {
    name: "regions_updated_init_sync_idx",
});

/**
 * @description Index for data sync - UPDATED records with cursor pagination
 * Query: { country_id: countryId, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
 */
RegionSchema.index({
    country_id: 1,
    updated_at: 1,
    _id: 1
}, {
    name: "regions_updated_sync_idx",
});

module.exports = mongoose.model("regions", RegionSchema);
