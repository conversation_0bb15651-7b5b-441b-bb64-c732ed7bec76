const { TIME_UNITS } = require("../../Configs/constants")
const Schema = mongoose.Schema;

const AccessTokenInfoSchema = new Schema({
    time: {
        type: Number,
        required: true
    },
    time_unit: {
        type: String,
        enum: Object.values(TIME_UNITS),
        required: true
    }
})

const CognitoConfigSchema = new Schema({
    access_token_info: {
        type: AccessTokenInfoSchema,
        required: true
    }
}, {
    timestamps: {
        createdAt: "created_at",
        updatedAt: "updated_at"
    }
});

module.exports = mongoose.model("cognito_configs", CognitoConfigSchema);