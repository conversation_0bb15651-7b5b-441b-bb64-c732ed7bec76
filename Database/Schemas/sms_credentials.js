const { SMS_PROVIDER_INFO } = require('../../Configs/constants');
const Schema = mongoose.Schema;

const UnifonicSchema = new Schema({
    app_s_id:{
        type: String,
        required: true,
    },
    username: {
        type: String,
        required: true
    },
    password: {
        type: String,
        required: true,
    },
    sender_id: {
        type: String,
        required: true
    },
})

const SmsCredentialSchema = new Schema({
    provider_name: {
        type: String,
        required: true,
        default: SMS_PROVIDER_INFO.UNIFONIC
    },
    unifonic: {
        type: UnifonicSchema,
    },
    //TODO: add new provider info below. To add new Type of SMS provider credentials
});

module.exports = mongoose.model('sms_credentials', SmsCredentialSchema);