# Hawak-user-backend-service
This is user authentication and user role management service.
## Deployment Instructions

- Node version - 14.19.0 (v14 lts)

- Database: MongoDB atlas will be used for it. MongoDB version 5 (at least 4.4)

- for .env file content follow below instructions.
    for staging environment, use .env.staging file
    for development environment, use .env.development file
    for production environment, use .env.master file 

- After installing Node on server, run below commands to install dependencies
```bash
   npm install
```
```bash
   npm start
```

## Run Locally

If, you do not have VSCode, MongoDB or Mongo-Compass  installed follow these steps and after the requirements are met follow the above mentioned steps.

- Download and setup [VSCode](https://code.visualstudio.com/download).

- Download and Install [NodeJs](https://nodejs.org/en/download/) version (14.19.0).

- Download and extract the project in your project directory or,

- Clone the project in your preferred folder from git CLI using

```bash
    cd your_project_folder
```
```bash
    <NAME_EMAIL>:hawak-io/hawak-user-backend.git
```

Install dependencies

```bash
   npm install
```

- When cloned/downloaded the project, you will need to create a `.env` and change the following environment variables to run this project.

For this simply, copy and paste and rename `.env.development` to `.env` in your project folder.

- after following above steps run below command to start the server