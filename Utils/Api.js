const { httpService } = require("./helpers")

class Api {
    async get(req, apiUrl, params) {
        try {
            const response = await httpService(req)
                .get(apiUrl, { params })

            return response?.["data"]?.["data"]
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `API ERROR -> ${error.message}`,
                data: error.response?.data
            })

            throw new Error(error)
        }
    }

    async put(req, apiUrl, data) {
        try {
            const response = await httpService(req)
                .put(apiUrl, data)

            return response?.["data"]?.["data"]
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `API ERROR -> ${error.message}`,
                data: error.response?.data
            })

            throw new Error(error)
        }
    }
}

module.exports = Api
