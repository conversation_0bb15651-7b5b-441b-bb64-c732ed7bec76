const { sleep } = require("./helpers")

// Calculates jittered delay based on exponential backoff
const getJitteredDelay = (baseDelayMs, attempt) => {
    const exponentialDelay = baseDelayMs * Math.pow(2, attempt - 1)
    const jitter = Math.random() * exponentialDelay * 0.5 // up to 50% jitter

    return exponentialDelay - jitter
}

async function retry(fn, { maxRetries = 5, baseDelayMs = 1000, fnName } = {}) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            return await fn() // try the function
        }
        catch (error) {
            logger.error(`[${fnName}]: Attempt ${attempt} failed`, error)

            if (attempt < maxRetries) {
                const jitteredDelay = getJitteredDelay(baseDelayMs, attempt)
                logger.error(`[${fnName}]: Retrying in ${(jitteredDelay / 1000).toFixed(2)}s...`)
                await sleep(jitteredDelay)
            }
            else {
                logger.error(`[${fnName}]: All retry attempts failed`)
                throw error // rethrow after final failure
            }
        }
    }
}

module.exports = { retry }
