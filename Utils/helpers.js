const randomstring = require("randomstring");
const axios = require("axios");
const snakeCase = require('lodash.snakecase');
const mapKeys = require('lodash.mapkeys');
const bwipJs = require("bwip-js")


const {
    VALUES,
    STATUS_CODES
} = require("../Configs/constants");

/*
GENERATE RANDOM NUMBER FROM SPECIFIED RANGE
*/
function generateRandom(min, max) {
    return Math.floor(Math.random() * (max - min)) + min
}

/*
CHECK IF TWO ARRAYS ARE EQUAL
*/
function arraysEqual(a, b) {
    if (a === b) return true;
    if (a == null || b == null) return false;
    if (a.length != b.length) return false;

    for (var i = 0; i < a.length; ++i)  if (a[i] !== b[i]) return false;

    return true;
}

/*
 * Generate Otp
 */
const generateOtp = () => {
    let otp = randomstring.generate({ length: 4, charset: 'numeric' });
    return otp;
}

const requireUncached = (path) => {
    delete require.cache[require.resolve(path)];
    return require(path)
}

const httpService = (request) => axios.create({
    baseURL: VALUES.internalServiceBaseURL,
    headers: { userDetails: JSON.stringify(request.headers?.userDetails) }
});

const httpSalesPersonService = (request) => axios.create({
    baseURL: VALUES.internalSalesPersonServiceBaseURL,
    headers: { userDetails: JSON.stringify(request.headers?.userDetails) }
});

const roundOf = (number = 0, toFix = 3, type = "number") => (
    type === "number"
        ? Number(number.toFixed(toFix))
        : number.toFixed(toFix)
)

const removeAllWhitespace = (string) => {
    let newString = string || ""

    if (typeof newString !== "string") {
        newString = String(newString)
    }
    const whitespaceRemoved = newString.replace(/\s/g, '')
    return whitespaceRemoved
}

const convertListOfKeysToSnakeCase = (list = []) => {
    return list
        .map((element) =>
            mapKeys(
                element,
                (_, key) => snakeCase(key)
            )
        );
}

const containsObject = (arr) => {
    return arr.every(function (item) {
        return typeof item === "object";
    });
}

const convertToSnakeCase = (data, updateObj) => {
    if (Array.isArray(data)) {
        return convertListOfKeysToSnakeCase(data);
    }
    else {
        if (!Object.keys(data).length) return {};

        const update = updateObj || {};

        Object.keys((data)).forEach((key) => {
            if (key) {
                if (Array.isArray(data[key])) {
                    if (containsObject(data[key])) {
                        update[snakeCase(key)] = convertListOfKeysToSnakeCase(data[key]);
                    }
                    else {
                        update[snakeCase(key)] = data[key];
                    }
                }
                else if (typeof data[key] === 'object') {
                    update[snakeCase(key)] = convertToSnakeCase(data[key]);
                }
                else {
                    if (key === "_id") {
                        update[key] = data[key];
                    }
                    else {
                        update[snakeCase(key)] = data[key];
                    }
                }
            }
        })
        return update;
    }
}

function toCamelCase(str) {
    return str.replace(/[_-](\w)/g, (_, c) => c.toUpperCase());
}

function convertKeysToCamelCase(input) {
    if (Array.isArray(input)) {
        return input.map(convertKeysToCamelCase);
    }
    else if (input !== null && typeof input === 'object') {
        return Object.entries(input).reduce((acc, [key, value]) => {
            const camelKey = toCamelCase(key);
            acc[camelKey] = convertKeysToCamelCase(value);
            return acc;
        }, {});
    }
    return input;
}

const arrayBufferToBase64 = (arrayBuffer) => {
    const bytes = new Uint8Array(arrayBuffer)
    const len = bytes.byteLength
    let binary = ''

    for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i])
    }
    return Buffer.from(binary, "binary").toString("base64")
}

const formatAmount = (amount = 0, decimal = 2, currency) => {
    return amount.toFixed(decimal) + (currency ? ` ${currency}` : "")
}

const stringifyObjectId = (objectId) => {
    if (objectId) {
        return objectId.toString()
    }
    return ""
}

const stringFromObject = (obj) => {
    return Object.values(obj).flatMap(value => {
        if (typeof value === "object") {
            return stringFromObject(value)
        }
        else if (typeof value === "string") {
            return value
        }
        else {
            return []
        }
    })
}

const errorHandler = (error = {}, res) => {
    const errorCode = error?.response?.status || STATUS_CODES.SERVER_ERROR
    const errorMessage = error?.response?.data?.message || error?.message

    return res.handler.custom(errorCode, errorMessage, undefined, error);
}

const getQrCodeBase64 = async (text) => {
    if (!text) return

    const qrCodeBuffer = await bwipJs.toBuffer({
        bcid: "qrcode",
        text,
        textxalign: "center",
        scale: 2,
        paddingwidth: 5,
        paddingtop: 5,
        paddingbottom: 4,
    })

    return "data:image/png;base64," + arrayBufferToBase64(qrCodeBuffer)
}

const includeProjections = {
    tenant_id: 1,
}

const excludeProjections = {
    __v: 0,
    created_at: 0,
    updated_at: 0,
    created_by: 0,
    updated_by: 0
};

const toLeanOption = {
    lean: true
}

const prepareApiLog = (req, data, error) => {
    const errorMessage = error?.response?.data?.message || error?.message

    const {
        method,
        originalUrl,
        query,
        body,
        headers: {
            authorization,
            refreshtoken,
            userroleid,
            userDetails,
            devicetoken,
            devicetype,
            deviceaccesstype,
            portalType,
            deviceTypeDetected,
            version,
            build,
        } = {},
    } = req

    const apiInfo = {
        method,
        url: originalUrl,
        headers: {
            authorization,
            refreshtoken,
            userDetails,
            userroleid,
            portalType,
            devicetoken,
            devicetype,
            deviceaccesstype,
            deviceTypeDetected,
            version,
            build,
        },
        errorMessage,
        stack: error?.stack
    }

    if (!isEmpty(data)) {
        apiInfo.data = data
    }

    if (!isEmpty(query)) {
        apiInfo.query = query
    }

    if (!isEmpty(body)) {
        apiInfo.body = body
    }
    return apiInfo
}

const shortenName = (name, maxLen = 24) => {
    if (!name) return ""

    let shortName = name

    if (name.length > maxLen) {
        shortName = shortName.slice(0, maxLen) + "..."
    }
    return shortName
}

function getMobileNumber({ country_code, mobile_number }) {
    if (!country_code || !mobile_number) {
        return ""
    }
    return (country_code + mobile_number).replace("+", "")
}

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms))

module.exports = {
    generateRandom,
    arraysEqual,
    generateOtp,
    requireUncached,
    httpService,
    httpSalesPersonService,
    roundOf,
    removeAllWhitespace,
    convertToSnakeCase,
    stringifyObjectId,
    stringFromObject,
    arrayBufferToBase64,
    getQrCodeBase64,
    formatAmount,
    errorHandler,
    includeProjections,
    excludeProjections,
    toLeanOption,
    prepareApiLog,
    shortenName,
    getMobileNumber,
    convertKeysToCamelCase,
    sleep,
};
