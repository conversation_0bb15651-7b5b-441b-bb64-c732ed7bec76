# Hawak API Documentation for React Native with Realm Integration

This documentation provides comprehensive API integration details for React Native developers using Realm database with `realm` and `@realm/react` packages.

## Base Configuration

```typescript
const BASE_URL = "https://your-api-domain.com"; // Replace with your actual API domain
const API_VERSION = "v1"; // If applicable
```

## Authentication Headers

All authenticated endpoints require the following headers:

```typescript
interface AuthHeaders {
  "Content-Type": "application/json";
  authorization: string; // JWT access token
  userroleid: string; // User role ID
  devicetoken: string; // Device token
  devicetype: "IOS" | "ANDROID"; // Device type
  deviceaccesstype: string; // Device access type
  refreshtoken?: string; // Refresh token (when needed)
}
```

## 1. Authentication APIs

### 1.1 App Sign In

**API URL:** `POST /auth/app-sign-in`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface AppSignInRequest {
  countryCode: string;
  mobileNumber: string;
}
```

**API Response Type:**

```typescript
interface AppSignInResponse {
  message: string;
  data: {
    otpSent: boolean;
    userId?: string;
  };
}
```

**Realm Object Schema:**

```typescript
class UserSession extends Realm.Object<UserSession> {
  _id!: Realm.BSON.ObjectId;
  userId!: string;
  countryCode!: string;
  mobileNumber!: string;
  otpSent!: boolean;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "UserSession",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      userId: "string?",
      countryCode: "string",
      mobileNumber: "string",
      otpSent: "bool",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 1.2 Verify Auth OTP

**API URL:** `POST /auth/verify-auth-otp`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface VerifyAuthOtpRequest {
  countryCode: string;
  mobileNumber: string;
  otp: string;
}
```

**API Response Type:**

```typescript
interface VerifyAuthOtpResponse {
  message: string;
  data: {
    accessToken: string;
    refreshToken: string;
    userDetails: {
      _id: string;
      first_name: string;
      last_name: string;
      email: string;
      mobile_number: number;
      country_code: string;
    };
  };
}
```

**Realm Object Schema:**

```typescript
class AuthUser extends Realm.Object<AuthUser> {
  _id!: Realm.BSON.ObjectId;
  userId!: string;
  firstName!: string;
  lastName!: string;
  email!: string;
  mobileNumber!: number;
  countryCode!: string;
  accessToken!: string;
  refreshToken!: string;
  isActive!: boolean;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "AuthUser",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      userId: "string",
      firstName: "string",
      lastName: "string",
      email: "string",
      mobileNumber: "int",
      countryCode: "string",
      accessToken: "string",
      refreshToken: "string",
      isActive: "bool",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 1.3 Get User Roles

**API URL:** `POST /auth/app-user-roles`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface AppUserRolesRequest {
  countryCode: string;
  mobileNumber: string;
}
```

**API Response Type:**

```typescript
interface AppUserRolesResponse {
  message: string;
  data: {
    userRoles: Array<{
      _id: string;
      role_id: {
        _id: string;
        name: string;
        description: string;
        portal_type: string;
      };
      tenant_id: number;
      user_id: string;
      is_active: boolean;
      collection_name: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class UserRole extends Realm.Object<UserRole> {
  _id!: Realm.BSON.ObjectId;
  userRoleId!: string;
  roleId!: string;
  roleName!: string;
  roleDescription!: string;
  portalType!: string;
  tenantId!: number;
  userId!: string;
  isActive!: boolean;
  collectionName!: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "UserRole",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      userRoleId: "string",
      roleId: "string",
      roleName: "string",
      roleDescription: "string",
      portalType: "string",
      tenantId: "int",
      userId: "string",
      isActive: "bool",
      collectionName: "string",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

## 2. Data Synchronization APIs

### 2.1 Get Tenant App Settings

**API URL:** `GET /dataSync/tenantAppSetting`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetTenantAppSettingRequest {
  tenantId: string;
  type: string; // Must be "APP_SETTING"
}
```

**API Response Type:**

```typescript
interface GetTenantAppSettingResponse {
  message: string;
  data: {
    _id: string;
    tenant_id: number;
    quantity_label: number;
    consider_new_item: number;
    price_change: boolean;
    hide_out_of_stock_product: boolean;
    reduce_inventory: boolean;
    customer_app_access: boolean;
    catalog_mode: boolean;
    customer_auto_catalog_mode: {
      enabled: boolean;
      duration: number;
    };
    preferred_language: string;
    decimal_points: number;
    payment_voucher_whatsapp_notification: boolean;
    created_at: string;
    updated_at: string;
  };
}
```

**Realm Object Schema:**

```typescript
class TenantAppSetting extends Realm.Object<TenantAppSetting> {
  _id!: Realm.BSON.ObjectId;
  settingId!: string;
  tenantId!: number;
  quantityLabel!: number;
  considerNewItem!: number;
  priceChange!: boolean;
  hideOutOfStockProduct!: boolean;
  reduceInventory!: boolean;
  customerAppAccess!: boolean;
  catalogMode!: boolean;
  customerAutoCatalogModeEnabled!: boolean;
  customerAutoCatalogModeDuration!: number;
  preferredLanguage!: string;
  decimalPoints!: number;
  paymentVoucherWhatsappNotification!: boolean;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "TenantAppSetting",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      settingId: "string",
      tenantId: "int",
      quantityLabel: "int",
      considerNewItem: "int",
      priceChange: "bool",
      hideOutOfStockProduct: "bool",
      reduceInventory: "bool",
      customerAppAccess: "bool",
      catalogMode: "bool",
      customerAutoCatalogModeEnabled: "bool",
      customerAutoCatalogModeDuration: "int",
      preferredLanguage: "string",
      decimalPoints: "int",
      paymentVoucherWhatsappNotification: "bool",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 2.2 Get User Role Settings

**API URL:** `GET /dataSync/userRoleSettings`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetUserRoleSettingsRequest {
  tenantId: string;
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // For pagination
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetUserRoleSettingsResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      tenant_id: number;
      user_role_id: string;
      default_master_price_id?: string;
      out_of_stock: {
        visible: boolean;
        searchable: boolean;
      };
      price_change: boolean;
      preferred_language?: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class UserRoleSetting extends Realm.Object<UserRoleSetting> {
  _id!: Realm.BSON.ObjectId;
  settingId!: string;
  tenantId!: number;
  userRoleId!: string;
  defaultMasterPriceId?: string;
  outOfStockVisible!: boolean;
  outOfStockSearchable!: boolean;
  priceChange!: boolean;
  preferredLanguage?: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "UserRoleSetting",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      settingId: "string",
      tenantId: "int",
      userRoleId: "string",
      defaultMasterPriceId: "string?",
      outOfStockVisible: "bool",
      outOfStockSearchable: "bool",
      priceChange: "bool",
      preferredLanguage: "string?",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 2.3 Get User Roles (Data Sync)

**API URL:** `GET /dataSync/userRoles`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetUserRolesDataSyncRequest {
  tenantId: string;
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // MongoDB ObjectId for pagination
  isInitialSync?: boolean; // true for first sync
  roles: string[]; // Array of role names
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetUserRolesDataSyncResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      collection_name: string;
      customer_app_access?: boolean;
      customer_email?: string;
      customer_first_name?: string;
      customer_last_name?: string;
      customer_legal_name?: string;
      external_id?: string;
      branch_id?: string;
      is_active: boolean;
      is_deleted: boolean;
      allow_price_change?: boolean;
      preferred_language?: string;
      role_id: string;
      tenant_id: number;
      user_id: {
        _id: string;
        first_name: string;
        last_name: string;
        email: string;
        mobile_number: number;
        country_code: string;
      };
      shipping_address?: string;
      shipping_city_id?: string;
      shipping_country_code?: string;
      shipping_region_id?: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class DataSyncUserRole extends Realm.Object<DataSyncUserRole> {
  _id!: Realm.BSON.ObjectId;
  userRoleId!: string;
  collectionName!: string;
  customerAppAccess?: boolean;
  customerEmail?: string;
  customerFirstName?: string;
  customerLastName?: string;
  customerLegalName?: string;
  externalId?: string;
  branchId?: string;
  isActive!: boolean;
  isDeleted!: boolean;
  allowPriceChange?: boolean;
  preferredLanguage?: string;
  roleId!: string;
  tenantId!: number;
  userId!: string;
  userFirstName!: string;
  userLastName!: string;
  userEmail!: string;
  userMobileNumber!: number;
  userCountryCode!: string;
  shippingAddress?: string;
  shippingCityId?: string;
  shippingCountryCode?: string;
  shippingRegionId?: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "DataSyncUserRole",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      userRoleId: "string",
      collectionName: "string",
      customerAppAccess: "bool?",
      customerEmail: "string?",
      customerFirstName: "string?",
      customerLastName: "string?",
      customerLegalName: "string?",
      externalId: "string?",
      branchId: "string?",
      isActive: "bool",
      isDeleted: "bool",
      allowPriceChange: "bool?",
      preferredLanguage: "string?",
      roleId: "string",
      tenantId: "int",
      userId: "string",
      userFirstName: "string",
      userLastName: "string",
      userEmail: "string",
      userMobileNumber: "int",
      userCountryCode: "string",
      shippingAddress: "string?",
      shippingCityId: "string?",
      shippingCountryCode: "string?",
      shippingRegionId: "string?",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 2.4 Get Regions

**API URL:** `GET /dataSync/regions`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetRegionsRequest {
  countryId: string; // MongoDB ObjectId
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // For pagination
  isInitialSync?: boolean; // true for first sync
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetRegionsResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      country_id: string;
      name: string;
      secondary_language_name: string;
      code?: string;
      is_active: boolean;
      is_deleted: boolean;
      secondary_language_code?: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class Region extends Realm.Object<Region> {
  _id!: Realm.BSON.ObjectId;
  regionId!: string;
  countryId!: string;
  name!: string;
  secondaryLanguageName!: string;
  code?: string;
  isActive!: boolean;
  isDeleted!: boolean;
  secondaryLanguageCode?: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "Region",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      regionId: "string",
      countryId: "string",
      name: "string",
      secondaryLanguageName: "string",
      code: "string?",
      isActive: "bool",
      isDeleted: "bool",
      secondaryLanguageCode: "string?",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 2.5 Get Cities

**API URL:** `POST /dataSync/cities`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetCitiesRequest {
  regionIds: string[]; // Array of MongoDB ObjectIds
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // For pagination
  isInitialSync?: boolean; // true for first sync
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetCitiesResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      country_id: string;
      region_id: string;
      name: string;
      secondary_language_name: string;
      is_active: boolean;
      is_deleted: boolean;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class City extends Realm.Object<City> {
  _id!: Realm.BSON.ObjectId;
  cityId!: string;
  countryId!: string;
  regionId!: string;
  name!: string;
  secondaryLanguageName!: string;
  isActive!: boolean;
  isDeleted!: boolean;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "City",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      cityId: "string",
      countryId: "string",
      regionId: "string",
      name: "string",
      secondaryLanguageName: "string",
      isActive: "bool",
      isDeleted: "bool",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

## 3. Implementation Examples

### 3.1 Realm Configuration

```typescript
import Realm from "realm";
import {
  AuthUser,
  UserRole,
  TenantAppSetting,
  UserRoleSetting,
  DataSyncUserRole,
  Region,
  City,
} from "./schemas";

const realmConfig: Realm.Configuration = {
  schema: [
    AuthUser,
    UserRole,
    TenantAppSetting,
    UserRoleSetting,
    DataSyncUserRole,
    Region,
    City,
  ],
  schemaVersion: 1,
  migration: (oldRealm, newRealm) => {
    // Handle schema migrations here
  },
};

export const getRealm = async (): Promise<Realm> => {
  return await Realm.open(realmConfig);
};
```

### 3.2 API Service Implementation

```typescript
import axios, { AxiosInstance } from "axios";

class ApiService {
  private api: AxiosInstance;
  private baseURL = "https://your-api-domain.com";

  constructor() {
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
    });
  }

  setAuthHeaders(token: string, userRoleId: string, deviceToken: string) {
    this.api.defaults.headers.common = {
      "Content-Type": "application/json",
      authorization: token,
      userroleid: userRoleId,
      devicetoken: deviceToken,
      devicetype: Platform.OS === "ios" ? "IOS" : "ANDROID",
      deviceaccesstype: "MOBILE_APP",
    };
  }

  async appSignIn(data: AppSignInRequest): Promise<AppSignInResponse> {
    const response = await this.api.post("/auth/app-sign-in", data);
    return response.data;
  }

  async verifyAuthOtp(
    data: VerifyAuthOtpRequest
  ): Promise<VerifyAuthOtpResponse> {
    const response = await this.api.post("/auth/verify-auth-otp", data);
    return response.data;
  }

  async getUserRoles(data: AppUserRolesRequest): Promise<AppUserRolesResponse> {
    const response = await this.api.post("/auth/app-user-roles", data);
    return response.data;
  }

  async getTenantAppSetting(
    params: GetTenantAppSettingRequest
  ): Promise<GetTenantAppSettingResponse> {
    const response = await this.api.get("/dataSync/tenantAppSetting", {
      params,
    });
    return response.data;
  }

  async getUserRoleSettings(
    params: GetUserRoleSettingsRequest
  ): Promise<GetUserRoleSettingsResponse> {
    const response = await this.api.get("/dataSync/userRoleSettings", {
      params,
    });
    return response.data;
  }

  async getUserRolesDataSync(
    params: GetUserRolesDataSyncRequest
  ): Promise<GetUserRolesDataSyncResponse> {
    const response = await this.api.get("/dataSync/userRoles", { params });
    return response.data;
  }

  async getRegions(params: GetRegionsRequest): Promise<GetRegionsResponse> {
    const response = await this.api.get("/dataSync/regions", { params });
    return response.data;
  }

  async getCities(data: GetCitiesRequest): Promise<GetCitiesResponse> {
    const response = await this.api.post("/dataSync/cities", data);
    return response.data;
  }
}

export const apiService = new ApiService();
```
