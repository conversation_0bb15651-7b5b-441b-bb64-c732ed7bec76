const {
    NOTIFICATION,
    PRIMITIVE_ROLES,
    LANGUAGE,
} = require("../Configs/constants")

class TemplateService {

    getOrderNotificationTemplate = (
        notificationType,
        userRole,
        orderInfo
    ) => {
        const {
            orderId,
            salesPersonName,
            customerLegalName,
            formattedAmount,
        } = orderInfo

        const templates = {
            /*  --------------------------------------------------------------------------  */
            /*                          ORDER_FOR_APPROVAL Templates                        */
            /*  --------------------------------------------------------------------------  */
            [NOTIFICATION.TYPE.ORDER_FOR_APPROVAL]: {
                [PRIMITIVE_ROLES.CUSTOMER]: {
                    [LANGUAGE.EN]: {
                        "title": `Pending Approval`,
                        "message": `📤 Your order #${orderId} has been sent for approval.`,
                        "centerMessage": `Your Order #${orderId} is <bold>sent for approval.</bold>`,
                        "smsBody": `Your order ${orderId} has been successfully sent for approval. Thank you for your patience.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `بانتظار الموافقة`,
                        "message": `📤 تم إرسال طلبك رقم ${orderId} للموافقة.`,
                        "centerMessage": `الطلب رقم <bold>${orderId}:</bold> تم إرساله للموافقة`
                    }
                },
                [PRIMITIVE_ROLES.SALES_PERSON]: {
                    [LANGUAGE.EN]: {
                        "title": `Pending Approval`,
                        "message": `📤 ${customerLegalName} has sent an order #${orderId} for approval.`,
                        "centerMessage": `Order from <bold>${customerLegalName}</bold> was sent for approval. Order <bold>#${orderId}</bold>`,
                        "smsBody": `New order ${orderId} for ${customerLegalName} has been successfully sent for approval.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `بانتظار الموافقة`,
                        "message": `📤 ${customerLegalName} ارسال طلبك رقم ${orderId} للموافقة.`,
                        "centerMessage": `تم إرسال الطلب من <bold>${customerLegalName}</bold> للموافقة. رقم <bold>${orderId}</bold>`,
                    },
                },
                [PRIMITIVE_ROLES.SUPERVISOR]: {
                    [LANGUAGE.EN]: {
                        "title": `Pending Approval`,
                        "message": `📤 ${customerLegalName} has sent an order #${orderId} for approval.`,
                        "centerMessage": `Order from <bold>${customerLegalName}</bold> was sent for approval. Order <bold>#${orderId}</bold>`,
                        "smsBody": `New order ${orderId} for ${customerLegalName} has been successfully sent for approval.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `بانتظار الموافقة`,
                        "message": `📤 ${customerLegalName} ارسال طلبك رقم ${orderId} للموافقة.`,
                        "centerMessage": `تم إرسال الطلب من <bold>${customerLegalName}</bold> للموافقة. رقم <bold>${orderId}</bold>`,
                    },
                },
            },

            /*  --------------------------------------------------------------------------  */
            /*                          ORDER_APPROVED Templates                            */
            /*  --------------------------------------------------------------------------  */
            [NOTIFICATION.TYPE.ORDER_APPROVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Approved`,
                        "message": `✅ Great news! Order #${orderId} is approved.`,
                        "centerMessage": `Your Order #${orderId} was <bold>approved.</bold>`,
                        "smsBody": `Your order ${orderId} has been approved. Thank you for your patience.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تمت الموافقة على الطلب`,
                        "message": `✅ تمّت الموافقة على الطلب رقم ${orderId}!`,
                        "centerMessage": `الطلب رقم <bold>${orderId}:</bold> تمت الموافقة عليه!`,
                    }
                },
                [PRIMITIVE_ROLES.SALES_PERSON]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Approved`,
                        "message": `✅ Order from ${customerLegalName} was approved. Order #${orderId}`,
                        "centerMessage": `Order from ${customerLegalName} was approved. Order <bold>#${orderId}</bold>`,
                        "smsBody": `New order ${orderId} for ${customerLegalName} has been approved.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تمت الموافقة على الطلب`,
                        "message": `✅ تمّت الموافقة على طلب من ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `تمّت الموافقة على طلب من <bold>${customerLegalName}.</bold> رقم الطلب <bold>${orderId}</bold>`,
                    }
                },
                [PRIMITIVE_ROLES.SUPERVISOR]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Approved`,
                        "message": `✅ Order from ${customerLegalName} was approved. Order #${orderId}`,
                        "centerMessage": `Order from ${customerLegalName} was approved. Order <bold>#${orderId}</bold>`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تمت الموافقة على الطلب`,
                        "message": `✅ تمّت الموافقة على طلب من ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `تمّت الموافقة على طلب من <bold>${customerLegalName}.</bold> رقم الطلب <bold>${orderId}</bold>`,
                    },
                },
            },

            /*  --------------------------------------------------------------------------  */
            /*                          ORDER_RECEIVED Templates                            */
            /*  --------------------------------------------------------------------------  */
            [NOTIFICATION.TYPE.ORDER_RECEIVED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Received`,
                        "message": `📦 We've received your order #${orderId}. Thanks!`,
                        "centerMessage": `Your Order <bold>#${orderId}</bold> was sent successfully.`,
                        "smsBody": `Great news! We've received your order ${orderId} and will process it soon. Thank you for your business.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم استلام الطلب`,
                        "message": `📦 تم استلام طلبك رقم ${orderId}. شكرًا!`,
                        "centerMessage": `الطلب رقم <bold>${orderId}:</bold> تم الارسال بنجاح.`,
                        "smsBody": `طلب توريد رقم ${orderId} تم استلامه بنجاح`,
                    }
                },
                [PRIMITIVE_ROLES.SALES_PERSON]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Received`,
                        "message": `🆕 New order for ${customerLegalName} has been placed successfully.`,
                        "centerMessage": `A new order has been placed for <bold>${customerLegalName}</bold>.`,
                        "smsBody": `New order ${orderId} has been placed successfully.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم استلام الطلب`,
                        "message": `🆕 تم إنشاء طلب جديد لـ ${customerLegalName} بنجاح`,
                        "centerMessage": `تم إنشاء طلب جديد لـ <bold>${customerLegalName}</bold>`,
                    },
                },
                [PRIMITIVE_ROLES.SUPERVISOR]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Received`,
                        "message": `🆕 New order for ${customerLegalName} has been placed successfully.`,
                        "centerMessage": `<bold>New order</bold> ${orderId}\n<bold>Salesperson:</bold> ${salesPersonName}\n<bold>Customer:</bold> ${customerLegalName}\n<bold>Value:</bold> ${formattedAmount}`,
                        "smsBody": `New order ${orderId}\nSalesperson: ${salesPersonName}\nCustomer: ${customerLegalName}\nValue: ${formattedAmount}`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم استلام الطلب`,
                        "message": `🆕 تم إنشاء طلب جديد لـ ${customerLegalName} بنجاح`,
                        "centerMessage": `<bold>طلب جديد</bold> رقم ${orderId}\n<bold>المندوب:</bold> ${salesPersonName}\n<bold>العميل:</bold> ${customerLegalName}\n<bold>القيمة:</bold> ${formattedAmount}`,
                        "smsBody": `طلب مبيعات جديد رقم ${orderId}\nالعميل: ${customerLegalName}\nالقيمة: ${formattedAmount}`,
                    },
                },
            },

            /*  --------------------------------------------------------------------------  */
            /*                          ORDER_PREPARING Templates                           */
            /*  --------------------------------------------------------------------------  */
            [NOTIFICATION.TYPE.ORDER_PREPARING]: {
                [PRIMITIVE_ROLES.CUSTOMER]: {
                    [LANGUAGE.EN]: {
                        "title": `Preparing Order`,
                        "message": `🔧 We're preparing your order #${orderId}.`,
                        "centerMessage": `Order <bold>#${orderId}</bold>: Currently being prepared.`,
                        "smsBody": `Your order ${orderId} is under processing.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `جاري التحضير`,
                        "message": `🔧 نقوم بتحضير طلبك رقم ${orderId}.`,
                        "centerMessage": `الطلب رقم <bold>${orderId}:</bold> جارٍ التحضير.`,
                        "smsBody": `طلب توريد رقم ${orderId} قيد التنفيذ`,
                    }
                },
                [PRIMITIVE_ROLES.SALES_PERSON]: {
                    [LANGUAGE.EN]: {
                        "title": `Preparing Order`,
                        "message": `🔧 Order for ${customerLegalName} is being prepared. Order #${orderId}`,
                        "centerMessage": `Order <bold>#${orderId}</bold> for <bold>${customerLegalName}</bold> is being prepared.`,
                        "smsBody": `Order ${orderId} for ${customerLegalName} is under processing.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `جاري التحضير`,
                        "message": `🔧 جاري تحضير طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold> قيد التحضير`,
                        "smsBody": `طلب مبيعات جديد رقم ${orderId} للعميل ${customerLegalName} جاهز للتوصيل`,
                    }
                },
                [PRIMITIVE_ROLES.SUPERVISOR]: {
                    [LANGUAGE.EN]: {
                        "title": `Preparing Order`,
                        "message": `🔧 Order for ${customerLegalName} is being prepared. Order #${orderId}`,
                        "centerMessage": `<bold>Order</bold> ${orderId} is under processing.\n<bold>Salesperson:</bold> ${salesPersonName}\n<bold>Customer:</bold> ${customerLegalName}\n<bold>Value:</bold> ${formattedAmount}`,
                        "smsBody": `Order ${orderId} is under processing.\nSalesperson: ${salesPersonName}\nCustomer: ${customerLegalName}\nValue: ${formattedAmount}`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `جاري التحضير`,
                        "message": `🔧 جاري تحضير طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `<bold>طلب </bold>رقم ${orderId} تم الشحن\n<bold>المندوب:</bold> $${salesPersonName}\n<bold>العميل:</bold> $${customerLegalName}\n<bold>القيمة:</bold> ${formattedAmount}$`,
                        "smsBody": `الطلب رقم ${orderId} جاري التحضير\nالمندوب: ${salesPersonName}\nالعميل: ${customerLegalName}\nالقيمة: ${formattedAmount}`,
                    },
                },
            },

            /*  --------------------------------------------------------------------------  */
            /*                          ORDER_SHIPPED Templates                             */
            /*  --------------------------------------------------------------------------  */
            [NOTIFICATION.TYPE.ORDER_SHIPPED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Shipped`,
                        "message": `🚚 Your order #${orderId} is on its way!`,
                        "centerMessage": `Order #${orderId}: Shipped and on the way.`,
                        "smsBody": `Your order ${orderId} has been shipped.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم الشحن`,
                        "message": `🚚 طلبك رقم ${orderId} في الطريق إليك!`,
                        "centerMessage": `الطلب رقم ${orderId}: تم شحنه وهو في الطريق.`,
                        "smsBody": `طلب توريد رقم ${orderId} جاهز للتوصيل`,
                    }
                },
                [PRIMITIVE_ROLES.SALES_PERSON]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Shipped`,
                        "message": `🚚 Order for ${customerLegalName} has shipped. Order #${orderId}`,
                        "centerMessage": `Order <bold>#${orderId}</bold> for <bold>${customerLegalName}</bold> has been shipped.`,
                        "smsBody": `Order ${orderId} for ${customerLegalName} has been shipped.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم الشحن`,
                        "message": `🚚 تم شحن طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `تم شحن الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold>`,
                        "smsBody": `طلب مبيعات جديد رقم ${orderId} للعميل ${customerLegalName} جاهز للتوصيل`,
                    }
                },
                [PRIMITIVE_ROLES.SUPERVISOR]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Shipped`,
                        "message": `🚚 Order for ${customerLegalName} has shipped. Order #${orderId}`,
                        "centerMessage": `<bold>Order</bold> ${orderId} has been shipped.\n<bold>Salesperson:</bold> ${salesPersonName}\n<bold>Customer:</bold> ${customerLegalName}\n<bold>Value:</bold> ${formattedAmount}`,
                        "smsBody": `Order ${orderId} has been shipped.\nSalesperson: ${salesPersonName}\nCustomer: ${customerLegalName}\nValue: ${formattedAmount}`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم الشحن`,
                        "message": `🚚 تم شحن طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `<bold>طلب </bold>رقم ${orderId} تم الشحن\n<bold>المندوب:</bold> ${salesPersonName}\n<bold>العميل:</bold> ${customerLegalName}\n<bold>القيمة:</bold> ${formattedAmount}`,
                        "smsBody": `طلب مبيعات جديد رقم ${orderId} جاهز للتوصيل\nالعميل: ${customerLegalName}\nالقيمة: ${formattedAmount}`,
                    },
                },
            },

            /*  --------------------------------------------------------------------------  */
            /*                          ORDER_DELIVERED Templates                           */
            /*  --------------------------------------------------------------------------  */
            [NOTIFICATION.TYPE.ORDER_DELIVERED]: {
                [PRIMITIVE_ROLES.CUSTOMER]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Delivered`,
                        "message": `📬 Order #${orderId} has been delivered. Enjoy!`,
                        "centerMessage": `Order <bold>#${orderId}:</bold> Delivered successfully.`,
                        "smsBody": `Your order ${orderId} has been delivered.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم التوصيل`,
                        "message": `📬 تم توصيل طلبك رقم ${orderId}. نتمنى لك استخدامًا ممتعًا!`,
                        "centerMessage": `الطلب رقم ${orderId}: تم توصيله بنجاح.`,
                        "smsBody": `طلب توريد رقم ${orderId} تم توصيله بنجاح`,
                    }
                },
                [PRIMITIVE_ROLES.SALES_PERSON]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Delivered`,
                        "message": `📬 Order for ${customerLegalName} was delivered. Order #${orderId}`,
                        "centerMessage": `Order <bold>#${orderId}</bold> for <bold>${customerLegalName}</bold> was delivered.`,
                        "smsBody": `Order ${orderId} for ${customerLegalName} has been delivered.`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم التوصيل`,
                        "message": `📬 تم توصيل طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold> تم التوصيل`,
                        "smsBody": `طلب مبيعات جديد رقم ${orderId} للعميل ${customerLegalName} تم توصيله بنجاح`,
                    }
                },
                [PRIMITIVE_ROLES.SUPERVISOR]: {
                    [LANGUAGE.EN]: {
                        "title": `Order Delivered`,
                        "message": `📬 Order for ${customerLegalName} was delivered. Order #${orderId}`,
                        "centerMessage": `<bold>Order</bold> ${orderId} has been delivered.\n<bold>Salesperson:</bold> ${salesPersonName}\n<bold>Customer:</bold> ${customerLegalName}\n<bold>Value:</bold> ${formattedAmount}`,
                        "smsBody": `Order ${orderId} has been delivered.\nSalesperson: ${salesPersonName}\nCustomer: ${customerLegalName}\nValue: ${formattedAmount}`,
                    },
                    [LANGUAGE.AR]: {
                        "title": `تم التوصيل`,
                        "message": `📬 تم توصيل طلب لـ ${customerLegalName}. رقم الطلب ${orderId}`,
                        "centerMessage": `الطلب رقم <bold>${orderId}</bold> لـ <bold>${customerLegalName}</bold> تم التوصيل`,
                        "smsBody": `طلب مبيعات جديد رقم ${orderId} تم توصيله بنجاح\nالعميل: ${customerLegalName}\nالقيمة: ${formattedAmount}`,
                    },
                },
            },
        }

        return templates[notificationType][userRole]
    }

    getRewardCoinsNotificationTemplate = (notificationType, points) => {
        const templates = {
            [NOTIFICATION.TYPE.REWARD_PAYMENT]: {
                [LANGUAGE.EN]: {
                    "title": `🎉 You've Earned Coins!`,
                    "message": `💰 You earned ${points} coins from your payment!`,
                    "centerMessage": `${points} coins added for your recent payment.`
                },
                [LANGUAGE.AR]: {
                    "title": `🎉 لقد حصلت على نقاط!`,
                    "message": `💰 حصلت على ${points} نقطة مقابل الدفع!`,
                    "centerMessage": `تمت إضافة ${points} نقطة مقابل دفعتك الأخيرة.`
                },
            },
            [NOTIFICATION.TYPE.REWARD_PURCHASE]: {
                [LANGUAGE.EN]: {
                    "title": `🎉 You've Earned Coins!`,
                    "message": `🛒 You earned ${points} coins from your latest purchase!`,
                    "centerMessage": `${points} coins added for your latest purchase.`
                },
                [LANGUAGE.AR]: {
                    "title": `🎉 لقد حصلت على نقاط!`,
                    "message": `🛒 حصلت على ${points} نقطة مقابل الشراء!`,
                    "centerMessage": `تمت إضافة ${points} نقطة مقابل طلبك الأخير.`
                }
            },
            [NOTIFICATION.TYPE.REWARD_MEMBER_SCAN]: {
                [LANGUAGE.EN]: {
                    "title": `🎉 You've Earned Coins!`,
                    "message": `📍 ${points} coins added from your visit with the salesperson.`,
                    "centerMessage": `${points} coins added for your salesperson visit.`
                },
                [LANGUAGE.AR]: {
                    "title": `🎉 لقد حصلت على نقاط!`,
                    "message": `📍 تمت إضافة ${points} نقطة لزيارتك مع مندوب المبيعات.`,
                    "centerMessage": `تمت إضافة ${points} نقطة بسبب زيارتك مع مندوب المبيعات.`
                }
            },
            [NOTIFICATION.TYPE.REWARD_MILESTONE_ONE]: {
                [LANGUAGE.EN]: {
                    "title": `🎉 You've Earned Coins!`,
                    "message": `🎯 You've reached Milestone 1! ${points} coins added!`,
                    "centerMessage": `Milestone 1 reached. ${points} coins added to your balance.`
                },
                [LANGUAGE.AR]: {
                    "title": `🎉 لقد حصلت على نقاط!`,
                    "message": `🎯 لقد وصلت إلى المرحلة 1! تمت إضافة ${points} نقطة!`,
                    "centerMessage": `تم تحقيق المرحلة 1. تمت إضافة ${points} نقطة إلى رصيدك.`
                },
            },
            [NOTIFICATION.TYPE.REWARD_MILESTONE_TWO]: {
                [LANGUAGE.EN]: {
                    "title": `🎉 You've Earned Coins!`,
                    "message": `🚀 Milestone 2 unlocked! You earned ${points} coins.`,
                    "centerMessage": `Milestone 2 unlocked. ${points} coins added to your balance.`
                },
                [LANGUAGE.AR]: {
                    "title": `🎉 لقد حصلت على نقاط!`,
                    "message": `🚀 تم تحقيق المرحلة 2! لقد حصلت على ${points} نقطة.`,
                    "centerMessage": `تم تحقيق المرحلة 2. تمت إضافة ${points} نقطة إلى رصيدك.`
                },
            },
            [NOTIFICATION.TYPE.REWARD_MILESTONE_THREE]: {
                [LANGUAGE.EN]: {
                    "title": `🎉 You've Earned Coins!`,
                    "message": `🏆 Milestone 3 complete! ${points} coins awarded.`,
                    "centerMessage": `Milestone 3 achieved. ${points} coins added to your balance.`
                },
                [LANGUAGE.AR]: {
                    "title": `🎉 لقد حصلت على نقاط!`,
                    "message": `🏆 تمت المرحلة 3! تم منحك ${points} نقطة.`,
                    "centerMessage": `تم تحقيق المرحلة 3. تمت إضافة ${points} نقطة إلى رصيدك.`
                }
            }
        }

        return {
            "title": templates[notificationType][LANGUAGE.EN].title,
            "message": templates[notificationType][LANGUAGE.EN].message,
            "centerMessage": templates[notificationType][LANGUAGE.EN].centerMessage,

            "secondaryLanguageTitle": templates[notificationType][LANGUAGE.AR].title,
            "secondaryLanguageMessage": templates[notificationType][LANGUAGE.AR].message,
            "centerSecondaryLanguageMessage": templates[notificationType][LANGUAGE.AR].centerMessage,
        }
    }
}

module.exports = TemplateService
