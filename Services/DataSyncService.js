const TenantPortalModel = new (require('../Models/tenantPortal'))()
const RoleModel = new (require('../Models/roles'))()
const BaseDataSyncService = require('./BaseDataSyncService');

const {
    toLeanOption,
} = require('../Utils/helpers');

class DataSyncService extends BaseDataSyncService {

    async getTenantAppSetting(query) {
        const {
            tenantId,
        } = query;

        // Get tenant app setting (only one per tenant)
        const tenantAppSetting = await TenantPortalModel.tenantAppSettingExist(tenantId, "-__v", toLeanOption);
        return tenantAppSetting;
    }

    async getUserRoleSettings(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            perPage = 50,
        } = query

        const filter = { tenant_id: tenantId };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => TenantPortalModel.getUserRoleSettingsCount(filter),
            findFunction: (filter, selectFields, options) => TenantPortalModel.findUserRoleSettings(filter, selectFields, options),
            selectFields: "-__v",
            leanOptions: toLeanOption
        });
    }

    async getUserRoles(query) {
        const {
            tenantId,
            lastSyncedAt,
            cursor,
            isInitialSync,
            roles,
            perPage = 50,
        } = query

        const filter = {
            tenant_id: tenantId,
        };

        // Only apply is_deleted and is_active filters for initial sync
        if (isInitialSync) {
            filter.is_deleted = false;
            filter.is_active = true;
        }

        const roleIds = await RoleModel.getRolesWithFilter(
            {
                name: { $in: roles },
                is_deleted: false,
                is_active: true,
            },
            { _id: 1, },
            toLeanOption,
        );
        filter.role_id = { $in: roleIds.map(role => new mongoose.Types.ObjectId(role._id)) };

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => TenantPortalModel.getTenantUsersCount(filter),
            findFunction: (filter, selectFields, options) => TenantPortalModel.getTenantUsers(filter, selectFields, options),
            selectFields: `
                collection_name
                customer_app_access
                customer_app_request
                customer_email
                customer_first_name
                customer_id
                customer_last_name
                customer_legal_name
                customer_name
                external_id
                branch_id
                is_active
                is_deleted
                is_verified
                allow_price_change
                preferred_language
                price_list_id
                role_id
                sales_person_id
                tenant_id
                user_id
                gps_coordinates
                shipping_address
                shipping_city_id
                shipping_country_code
                shipping_country_id
                shipping_region_id
                shipping_mobile_number
                created_at
                updated_at
                `,
            leanOptions: {
                ...toLeanOption,
                populate: [{
                    path: "user_id",
                    select: "first_name last_name email mobile_number country_code"
                }]
            }
        });
    }

}

module.exports = new DataSyncService(); 
