const TenantPortalModel = new (require("../Models/tenantPortal"))()
const UserAuthModel = new (require("../Models/auth"))()
const RoleModal = new (require("../Models/roles"))()
const CommonModel = new (require("../Models/common"))()
const NotificationModel = new (require("../Models/Notification/NotificationModel"))()

const NotificationManager = new (require("../Managers/NotificationManager"))()
const SMSHandler = new (require("../Configs/smsHandler"))()
const TemplateService = new (require("./TemplateService"))()

const {
    PRIMITIVE_ROLES,
    ORDER_STATUS_TYPES,
    NOTIFICATION,
    PROMISE_STATES,
    VALUES,
    LANGUAGE,
    TENANT_SERVICES,
    MASTER_NOTIFICATION_TYPE,
} = require("../Configs/constants")

const {
    toLeanOption,
    getMobileNumber,
    stringifyObjectId,
    formatAmount,
    convertKeysToCamelCase,
} = require("../Utils/helpers")

class OrderNotificationService {

    async fetchTenantDetails(tenantId) {
        return TenantPortalModel.getTenantById(
            tenantId,
            `
            country
            region
            city
            primary_contact_id
            legal_name
            street_name
            services
            enable_sms_services
            `,
            {
                "lean": true,
                "populate": [
                    {
                        "path": "region",
                        "select": { "name": 1 }
                    },
                    {
                        "path": "city",
                        "select": { "name": 1 }
                    },
                    {
                        "path": "primary_contact_id",
                        "select": { "country_code": 1, "mobile_number": 1 }
                    },
                    {
                        "path": "country",
                        "select": { "currency": 1, "secondary_language_code": 1 }
                    }
                ],
            },
        )
    }

    async fetchUserRoles(salesPersonRoleId, customerRoleId) {
        return UserAuthModel.findUserRoles(
            { "_id": { "$in": [salesPersonRoleId, customerRoleId] } },
            `
            collection_name
            user_id
            role_id
            notifications
            customer_email
            customer_legal_name
            customer_id
            device_access
            preferred_language
            `,
            {
                "lean": true,
                "populate": [
                    {
                        "path": "user_id",
                        "select": { "first_name": 1, "last_name": 1, "country_code": 1, "mobile_number": 1, "email": 1 }
                    },
                    {
                        "path": "supervisor_id",
                        "select": { "country_code": 1, "mobile_number": 1, "_id": 1, "email": 1 }
                    }
                ]
            }
        )
    }

    async fetchRequiredRoles() {
        return RoleModal.getRolesWithFilter(
            {
                "name": {
                    "$in": [
                        PRIMITIVE_ROLES.TENANT_OWNER,
                        PRIMITIVE_ROLES.TENANT_ADMIN,
                        PRIMITIVE_ROLES.BRANCH_MANAGER,
                        PRIMITIVE_ROLES.SUPERVISOR
                    ]
                }
            },
            "_id name",
            toLeanOption,
        );
    }

    processUserRolesAndRoles(userRoles, roles) {
        let customerDetails = {}
        let salespersonDetails = {}
        const tenantRoles = []
        let branchRole = {}
        let supervisorRole = {}

        userRoles.forEach(userRole => {
            if (userRole.collection_name === "tenant_customers") {
                customerDetails = userRole
            }
            else {
                salespersonDetails = userRole
            }
        })

        roles.forEach(role => {
            if ([PRIMITIVE_ROLES.TENANT_OWNER, PRIMITIVE_ROLES.TENANT_ADMIN].includes(role.name)) {
                tenantRoles.push(role)
            }
            else if (role.name === PRIMITIVE_ROLES.BRANCH_MANAGER) {
                branchRole = role
            }
            else if (role.name === PRIMITIVE_ROLES.SUPERVISOR) {
                supervisorRole = role
            }
        })

        return {
            customerDetails,
            salespersonDetails,
            tenantRoles,
            branchRole,
            supervisorRole,
        }
    }

    // Get notification configuration based on order status
    getNotificationConfig(orderStatus, orderStatusTrack) {
        const statusConfig = {
            [ORDER_STATUS_TYPES.PENDING]: {
                "emailSubject": "New Order To Process",
                "notificationType": NOTIFICATION.TYPE.ORDER_FOR_APPROVAL
            },
            [ORDER_STATUS_TYPES.RECEIVED]: {
                "statusCode": 0,
                "emailSubject": "Order Received",
                "notificationType":
                    orderStatusTrack[0]?.order_status === ORDER_STATUS_TYPES.PENDING
                        ? NOTIFICATION.TYPE.ORDER_APPROVED
                        : NOTIFICATION.TYPE.ORDER_RECEIVED
            },
            [ORDER_STATUS_TYPES.PREPARING]: {
                "statusCode": 1,
                "emailSubject": "Order Preparing",
                "notificationType": NOTIFICATION.TYPE.ORDER_PREPARING
            },
            [ORDER_STATUS_TYPES.SHIPPED]: {
                "statusCode": 2,
                "emailSubject": "Order Shipped",
                "notificationType": NOTIFICATION.TYPE.ORDER_SHIPPED
            },
            [ORDER_STATUS_TYPES.DELIVERED]: {
                "statusCode": 3,
                "emailSubject": "Order Delivered",
                "notificationType": NOTIFICATION.TYPE.ORDER_DELIVERED
            }
        };
        return statusConfig[orderStatus];
    }

    // Collect all data for sending notifications
    async prepareNotificationData(
        processedData,
        reqData,
        notificationConfig,
        tenantDetails,
        decimalPoints,
    ) {
        const {
            _id,
            tenantId,
            branchId,
            orderId,
            orderDate,
            numberOfItems,
            totalQty,
            orderAmount,
            orderStatus,
            salesPersonName,
            customerLegalName,
        } = reqData

        const {
            customerDetails = {},
            salespersonDetails = {},
            tenantRoles = [],
            branchRole = {},
            supervisorRole = {},
        } = processedData;

        const { notificationType, emailSubject } = notificationConfig

        const emailRecipients = [];
        const notificationsData = [];
        const pushNotificationRecipients = [];
        const smsRecipients = [];
        const userRoleIdSet = new Set()
        let salespersonName = ""

        const tenantSecondaryLanguage = tenantDetails.country.secondary_language_code || LANGUAGE.EN
        const isSmsEnabledForTenant = tenantDetails.enable_sms_services

        const isPushEnabledForTenant = tenantDetails.services.some(service => (
            service.key === TENANT_SERVICES.PUSH_NOTIFICATION &&
            service.permission.create
        ))

        const orderInfo = {
            orderId,
            salesPersonName,
            "customerLegalName": customerLegalName || customerDetails?.customer_legal_name || "",
            "formattedAmount": formatAmount(orderAmount, decimalPoints, tenantDetails.country.currency),
        }

        const commonNotificationData = {
            "tenant_id": tenantId,
            "type": notificationType,
            "thread_id": _id,
            "payload_data": {
                _id,
                orderId,
                orderDate,
                numberOfItems,
                totalQty,
                orderAmount,
                orderStatus,
            }
        }

        // Process customer
        if (customerDetails._id) {
            const customEmailSubject = this.buildEmailSubject(emailSubject, orderId);
            const mobileNumber = getMobileNumber(customerDetails.user_id);

            this.processRecipient({
                "profile": customerDetails,
                "userProfile": { "email": customerDetails.customer_email },
                "roleType": PRIMITIVE_ROLES.CUSTOMER,
                mobileNumber,
                customEmailSubject,
                emailSubject,
                orderId,
                salespersonName,
                orderInfo,
                notificationType,
                emailRecipients,
                isSmsEnabledForTenant,
                smsRecipients,
                userRoleIdSet,
                notificationsData,
                pushNotificationRecipients,
                commonNotificationData,
                tenantId,
                tenantSecondaryLanguage,
                isPushEnabledForTenant,
            });
        }

        // Process salesperson
        if (salespersonDetails._id) {
            const mobileNumber = getMobileNumber(salespersonDetails.user_id)
            salespersonName = `${salespersonDetails.user_id?.first_name || ""} ${salespersonDetails.user_id?.last_name || ""}`

            this.processRecipient({
                "profile": salespersonDetails,
                "userProfile": salespersonDetails.user_id,
                "roleType": PRIMITIVE_ROLES.SALES_PERSON,
                mobileNumber,
                emailSubject,
                orderId,
                salespersonName,
                orderInfo,
                notificationType,
                emailRecipients,
                isSmsEnabledForTenant,
                smsRecipients,
                userRoleIdSet,
                notificationsData,
                pushNotificationRecipients,
                commonNotificationData,
                tenantId,
                tenantSecondaryLanguage,
                isPushEnabledForTenant,
            });
        }

        // Process supervisor
        if (salespersonDetails.supervisor_id?._id) {
            const supervisorProfile = await UserAuthModel.getUserRoleWithPopulation(
                {
                    "user_id": salespersonDetails.supervisor_id._id,
                    "is_deleted": false,
                    "is_active": true,
                    "role_id": supervisorRole._id
                },
                { "notifications": 1, "role_id": 1 },
                toLeanOption,
            );

            if (supervisorProfile) {
                const mobileNumber = getMobileNumber(salespersonDetails.supervisor_id)

                this.processRecipient({
                    "profile": supervisorProfile,
                    "userProfile": salespersonDetails.supervisor_id,
                    "roleType": PRIMITIVE_ROLES.SUPERVISOR,
                    mobileNumber,
                    emailSubject,
                    orderId,
                    salespersonName,
                    orderInfo,
                    notificationType,
                    emailRecipients,
                    isSmsEnabledForTenant,
                    smsRecipients,
                    userRoleIdSet,
                    notificationsData,
                    pushNotificationRecipients,
                    commonNotificationData,
                    tenantId,
                    tenantSecondaryLanguage,
                    isPushEnabledForTenant,
                });
            }
        }

        // Process tenant owners/admins and branch managers
        const tenantEmailProfiles = await CommonModel.getCommonProfilesForOrderEmail(tenantId, tenantRoles, branchId, branchRole);

        if (Array.isArray(tenantEmailProfiles) && tenantEmailProfiles.length) {
            const supervisorTemplate = TemplateService.getOrderNotificationTemplate(
                notificationType,
                PRIMITIVE_ROLES.SUPERVISOR,
                orderInfo
            )

            tenantEmailProfiles.forEach(profile => {
                const mobileNumber = getMobileNumber(profile.user_id)

                this.processRecipient({
                    "profile": profile,
                    "userProfile": profile.user_id,
                    "roleType": PRIMITIVE_ROLES.SUPERVISOR,
                    "existingTemplate": supervisorTemplate,
                    mobileNumber,
                    emailSubject,
                    orderId,
                    salespersonName,
                    orderInfo,
                    notificationType,
                    emailRecipients,
                    isSmsEnabledForTenant,
                    smsRecipients,
                    userRoleIdSet,
                    notificationsData,
                    pushNotificationRecipients,
                    commonNotificationData,
                    tenantId,
                    tenantSecondaryLanguage,
                    isPushEnabledForTenant,
                    branchRole,
                });
            })
        }

        const emailSendList = await this.prepareEmailSendData(emailRecipients, processedData, reqData, notificationConfig, tenantDetails, decimalPoints);
        const userSettingsMap = await this.fetchUserSettings(Array.from(userRoleIdSet), tenantId);

        pushNotificationRecipients.forEach(notification => {
            notification.notificationLanguage = userSettingsMap[notification.userRoleId] || LANGUAGE.EN
        })

        return {
            emailSendList,
            smsRecipients,
            pushNotificationRecipients,
            notificationsData,
            userSettingsMap,
            commonNotificationData,
        }
    }

    // Helper function to process recipients and avoid code duplication
    processRecipient(recipientData) {
        const {
            profile,
            userProfile,
            roleType,
            existingTemplate,
            mobileNumber,
            customEmailSubject,
            emailSubject,
            orderId,
            salespersonName,
            orderInfo,
            notificationType,
            emailRecipients,
            isSmsEnabledForTenant,
            smsRecipients,
            userRoleIdSet,
            notificationsData,
            pushNotificationRecipients,
            commonNotificationData,
            tenantId,
            tenantSecondaryLanguage,
            isPushEnabledForTenant,
            branchRole,
        } = recipientData;

        const notificationAccess = this.getNotificationAccess(profile, notificationType);
        const userRoleId = stringifyObjectId(profile._id)

        // Handle email
        if ((notificationAccess.allowEmail && userProfile?.email) &&
            !(
                roleType === PRIMITIVE_ROLES.CUSTOMER &&
                notificationType === NOTIFICATION.TYPE.ORDER_FOR_APPROVAL
            )
        ) {
            const emailSubjectToUse = customEmailSubject || this.buildEmailSubject(emailSubject, orderId, salespersonName);

            emailRecipients.push({
                "email": userProfile.email,
                "emailSubject": emailSubjectToUse,
                "token": userRoleId,
                roleType
            })
        }

        // Get template
        const template = existingTemplate || TemplateService.getOrderNotificationTemplate(
            notificationType,
            roleType,
            orderInfo
        );

        // Handle SMS
        if (isSmsEnabledForTenant && notificationAccess.allowSms && mobileNumber) {
            smsRecipients.push({
                userRoleId,
                mobileNumber,
                "message": {
                    [LANGUAGE.EN]: template[LANGUAGE.EN].smsBody,
                    [tenantSecondaryLanguage]: template[tenantSecondaryLanguage].smsBody,
                }
            });
            userRoleIdSet.add(`${tenantId}_${profile._id}`);
        }

        if (
            !branchRole?._id ||
            stringifyObjectId(profile.role_id) !== stringifyObjectId(branchRole._id)
        ) {
            // Create notification object
            const notification = {
                "user_role_id": userRoleId,
                "title": template[LANGUAGE.EN].title,
                "message": template[LANGUAGE.EN].message,
                "center_message": template[LANGUAGE.EN].centerMessage,

                "secondary_language_title": template[tenantSecondaryLanguage].title,
                "secondary_language_message": template[tenantSecondaryLanguage].message,
                "center_secondary_language_message": template[tenantSecondaryLanguage].centerMessage,
            };
            notificationsData.push({ ...notification, ...commonNotificationData });

            // Handle push notifications
            if (isPushEnabledForTenant && notificationAccess.allowPush) {
                userRoleIdSet.add(`${tenantId}_${profile._id}`);
                const pushData = convertKeysToCamelCase(notification);
                pushNotificationRecipients.push(pushData);
            }
        }
    };

    // Helper to get notification access settings
    getNotificationAccess(userRole, notificationType) {
        const notification = userRole.notifications?.find(n =>
            n.notification_type === (
                notificationType === NOTIFICATION.TYPE.ORDER_RECEIVED
                    ? MASTER_NOTIFICATION_TYPE.NEW_ORDER
                    : notificationType
            )
        );

        return {
            "allowEmail": notification?.allow_email_notification ?? true,
            "allowSms": notification?.allow_sms_notification ?? true,
            "allowPush": notification?.allow_push_notification ?? true
        };
    }

    buildEmailSubject(baseSubject, orderId, salespersonName) {
        const suffix = salespersonName ? ` - ${salespersonName}` : "";
        return `${baseSubject} - ${orderId}${suffix}`;
    }

    async prepareEmailSendData(emailRecipients, processedData, reqData, notificationConfig, tenantDetails, decimalPoints) {
        const emailSendList = []

        const { customerDetails = {}, salespersonDetails = {} } = processedData;
        const { notificationType, statusCode, } = notificationConfig

        const {
            "_id": dbOrderId,
            orderId,
            orderDate,
            numberOfItems,
            totalQty,
            orderAmount,

            salesPersonName,
            customerLegalName,
            shippingAddress,
            shippingCity,
            shippingRegion,
            shippingMobileNumber,
        } = reqData

        let dynamicLinkByToken = {}

        if (notificationType !== NOTIFICATION.TYPE.ORDER_FOR_APPROVAL) {
            dynamicLinkByToken = await this.generateDynamicLinks(emailRecipients, dbOrderId)
        }

        for (let i = 0; i < emailRecipients.length; i++) {
            const {
                email,
                emailSubject,
                token,
            } = emailRecipients[i]

            let orderLink = dynamicLinkByToken[token]

            if (!orderLink) {
                orderLink = CommonModel.generateOrderDynamicLink(dbOrderId, token)
            }

            emailSendList.push({
                "items": {
                    "_id": dbOrderId,
                    "order_id": orderId,
                    "order_date": moment(orderDate).format("MMMM D, YYYY"),
                    "num_of_items": numberOfItems,
                    "total_qty": totalQty,
                    "order_amount": formatAmount(orderAmount, decimalPoints, tenantDetails.country.currency),
                    "status": ["Received", "Preparing", "Shipped", "Delivered"],
                    "status_code": statusCode,

                    "company_legal_name": tenantDetails.legal_name,
                    "company_address": tenantDetails.street_name,
                    "company_city": tenantDetails.city?.name,
                    "company_region": tenantDetails.region?.name,
                    "company_phone": `${tenantDetails.primary_contact_id?.country_code || ""} ${tenantDetails.primary_contact_id?.mobile_number || ""}`,

                    "shipping_address": shippingAddress,
                    "shipping_city": shippingCity,
                    "shipping_region": shippingRegion,
                    "shipping_mobile_number": shippingMobileNumber,

                    "sales_person_mobile": `${salespersonDetails?.user_id?.country_code || ""} ${salespersonDetails?.user_id?.mobile_number || ""}`,
                    "salesperson_name": salesPersonName,

                    "customer_legal_name": customerLegalName || customerDetails.customer_legal_name,
                    "customer_id": customerDetails.customer_id,

                    "rtl": false,
                    "order_link": orderLink,
                },
                "language": LANGUAGE.EN,
                email,
                "subject": emailSubject
            })
        }
        return emailSendList
    }

    // Generate dynamic links for emails
    async generateDynamicLinks(emailRecipients, dbOrderId) {
        const dynamicLinkByToken = {};

        const dynamicLinksResponse = await Promise.allSettled(
            emailRecipients.map(({ token, roleType }) =>
                CommonModel.generateDynamicLink({ "_id": dbOrderId, token, roleType })
                    .then(value => ({ "status": PROMISE_STATES.FULFILLED, value, token }))
                    .catch(reason => ({ "status": PROMISE_STATES.REJECTED, reason, token }))
            )
        );

        dynamicLinksResponse.forEach(result => {
            if (result.status === PROMISE_STATES.FULFILLED) {
                const { value, token } = result.value;
                dynamicLinkByToken[token] = value;
            }
            else {
                const { reason, token } = result.reason;
                console.error(`❌ Failed to generate link for token ${token}:`, reason);
            }
        });
        return dynamicLinkByToken;
    }

    async fetchUserSettings(userRoleIds, tenantId) {
        const userSettings = await TenantPortalModel.findUserRoleSettings(
            { "_id": { "$in": userRoleIds } },
            { "preferred_language": 1 },
            toLeanOption,
        );

        const userSettingsMap = userSettings.reduce((map, { _id, preferred_language }) => {
            const roleId = _id.replace(`${tenantId}_`, '');
            map[roleId] = preferred_language;
            return map;
        }, {});

        return userSettingsMap
    }

    async sendNotification(data, notificationConfig) {
        const {
            emailSendList,
            smsRecipients,
            pushNotificationRecipients,
            notificationsData,
            userSettingsMap,
            commonNotificationData,
        } = data

        const { notificationType } = notificationConfig
        const smsSendList = []

        try {
            const sendEmailFn =
                notificationType === NOTIFICATION.TYPE.ORDER_FOR_APPROVAL
                    ? "newOrderEmail"
                    : "orderInfoEmail"

            const resultList = await Promise.allSettled([
                // Send emails to all recipients in the emailSendList
                ...emailSendList.map(({ items, language, email, subject }) => {
                    return UserAuthModel[sendEmailFn](items, language, email, subject)
                }),

                // Create notifications in bulk for all recipients
                NotificationModel.bulkCreateNotifications(notificationsData),
            ])

            resultList.forEach(result => {
                if (result.status === PROMISE_STATES.REJECTED) {
                    logger.error(result.reason)
                }
            })

            let pushSendResult = {}
            try {
                pushSendResult = await NotificationManager.sendPushNotificationWithUserRoleIds(
                    pushNotificationRecipients,
                    commonNotificationData.type,
                    commonNotificationData.payload_data,
                    commonNotificationData.thread_id
                );
            }
            catch (error) {
                logger.error(error, { "errorMessage": "Error in sending notification: PUSH_NOTIFICATION" });
            }

            try {
                smsRecipients.forEach(sms => {
                    const isSucceed = pushSendResult.success?.includes(stringifyObjectId(sms.userRoleId))

                    if (!isSucceed) {
                        const language = userSettingsMap[stringifyObjectId(sms.userRoleId)] || LANGUAGE.EN
                        const message = sms.message[language]

                        if (message) {
                            sms.message = encodeURIComponent(message)
                            smsSendList.push(sms)
                        }
                    }
                })

                if (smsSendList.length) {
                    const smsResult = await Promise.allSettled(
                        smsSendList.map(({ mobileNumber, message }) => {
                            return SMSHandler.sendSMS(mobileNumber, message)
                        })
                    )

                    smsResult.forEach(result => {
                        if (result.reason) {
                            logger.error(result.reason)
                        }
                    })
                }
            }
            catch (error) {
                logger.error(error, { "errorMessage": "Error in sending notification: SMS" });
            }
        }
        catch (error) {
            logger.error(error, { "errorMessage": "Error in sending notification: EMAIL, NOTIFICATION_DB_SAVE, PUSH_NOTIFICATION" });
        }
    }
}

module.exports = OrderNotificationService
