const CommonModel = new (require('../Models/common'))();
const BaseDataSyncService = require('./BaseDataSyncService');

const {
    toLeanOption,
} = require('../Utils/helpers');

class RegionService extends BaseDataSyncService {

    async getDataSyncRegions(query) {
        const {
            countryId,
            lastSyncedAt,
            cursor,
            isInitialSync,
            perPage = 50,
        } = query

        const filter = {
            country_id: countryId,
        };

        if (isInitialSync) {
            filter.is_deleted = false;
            filter.is_active = true;
        }

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => CommonModel.findRegionsCount(filter),
            findFunction: (filter, selectFields, options) => CommonModel.findRegions(filter, selectFields, options),
            selectFields: `
                -__v
                -created_by
                -updated_by
                `,
            leanOptions: toLeanOption
        });
    }
}

module.exports = new RegionService();
