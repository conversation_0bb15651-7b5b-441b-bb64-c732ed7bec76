const CommonModel = new (require('../Models/common'))();
const BaseDataSyncService = require('./BaseDataSyncService');

const {
    toLeanOption,
} = require('../Utils/helpers');

class CityService extends BaseDataSyncService {

    async getDataSyncCities(body) {
        const {
            regionIds,
            lastSyncedAt,
            cursor,
            isInitialSync,
            perPage = 50,
        } = body

        const filter = {
            region_id: { $in: regionIds },
        };

        if (isInitialSync) {
            filter.is_deleted = false;
            filter.is_active = true;
        }

        return this.executeDataSyncQuery({
            filter,
            lastSyncedAt,
            cursor,
            perPage,
            countFunction: (filter) => CommonModel.findCitiesCount(filter),
            findFunction: (filter, selectFields, options) => CommonModel.findCities(filter, selectFields, options),
            selectFields: `
                -__v
                -created_by
                -updated_by
                `,
            leanOptions: toLeanOption
        });
    }
}

module.exports = new CityService();
