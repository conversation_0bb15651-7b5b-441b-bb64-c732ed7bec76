class BaseDataSyncService {

    buildDateFilter(lastSyncedAt, cursor) {
        if (!lastSyncedAt || !cursor) {
            return {};
        }

        const lastSyncedDate = new Date(lastSyncedAt).toISOString();

        return {
            $or: [
                { updated_at: { $gt: lastSyncedDate } },
                {
                    updated_at: lastSyncedDate,
                    _id: { $gt: cursor }
                }
            ]
        };
    }

    async executeDataSyncQuery(options) {
        const {
            filter,
            lastSyncedAt,
            cursor,
            perPage = 50,
            countFunction,
            findFunction,
            selectFields,
            leanOptions = {}
        } = options;

        // Add date filter
        const dateFilter = this.buildDateFilter(lastSyncedAt, cursor);
        const finalFilter = { ...filter, ...dateFilter };

        // Execute queries in parallel
        const [totalCount, records] = await Promise.all([
            countFunction(filter),
            findFunction(
                finalFilter,
                selectFields,
                {
                    ...leanOptions,
                    sort: { updated_at: 1, _id: 1 },
                    limit: perPage,
                }
            )
        ]);

        return {
            count: records.length === 0 ? 0 : totalCount,
            list: records,
        };
    }
}

module.exports = BaseDataSyncService; 
