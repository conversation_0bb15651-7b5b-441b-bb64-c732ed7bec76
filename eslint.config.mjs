// eslint.config.mjs
import js from "@eslint/js";
import globals from "globals";
import { defineConfig } from "eslint/config";

export default defineConfig([
  {
    "files": ["**/*.{js,mjs,cjs}"],
    "plugins": { js },
    "extends": ["js/recommended"],
    /* "rules": {
      'quote-props': ['error', 'always']  // 👈 Your rule here
    } */
  },
  {
    "files": ["**/*.js"],
    "languageOptions": { "sourceType": "commonjs" }
  },
  {
    "files": ["**/*.{js,mjs,cjs}"],
    "languageOptions": {
      globals: {
        ...globals.browser,   // keep whatever you already spread
        STATUS_CODES: "readonly", // or "writable" if you assign to it. "readonly" tells E<PERSON>int the global exists and should not be reassigned
        logger: "readonly",
        mongoose: "readonly",
        moment: "readonly",
        momentTimezone: "readonly",
        isEmpty: "readonly",
      },
    }
  }
])
