const { VALUES } = require('../../Configs/constants');

exports.systemPortalRoleCheckValidator = (request, response, next) => {
    const sessionDetails = request.headers.sessionDetails;
    // const portal_type = sessionDetails.user_role_id?.role_id?.portal_type
    const portal_type = sessionDetails?.role_id?.portal_type


    if(!portal_type || portal_type !== VALUES.portals.SYSTEM_PORTAL) {
        return response.handler.forbidden("VALIDATION.INVALID.PORTAL_TYPE");
    }
    next();
}