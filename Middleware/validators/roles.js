const {
    header,
    body,
    query,
} = require("express-validator");

const { VALUES } = require('../../Configs/constants');

exports.addRole  = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("portalType", "Please provide portal type").trim().notEmpty().isIn(Object.values(VALUES.portals)),

    body("tenantId", "Please provide tenant id").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.TENANT_PORTAL) && !value) {
            return false
        }
        if(portalType === VALUES.portals.TENANT_PORTAL && req.body.branchId) {
            return false;
        }
        return true
    }).withMessage("Please provide tenant id only"),
    body("tenantId", "Please provide tenant id").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.SYSTEM_PORTAL) && value) {
            return false
        }
        return true
    }).withMessage("Please do not provide tenant id"),

    body("branchId", "Please provide branch id").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.BRANCH_PORTAL) && !value ) {
            return false
        }
        if((portalType === VALUES.portals.BRANCH_PORTAL) && !req.body.tenantId) {
            return false;
        }
        return true
    }).withMessage("Please provide branch id and tenant id"),
    body("branchId").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.SYSTEM_PORTAL) && value) {
            return false
        }
        return true
    }).withMessage("Please do not provide branch id"),

    body("name", "Please provide name of role").trim().notEmpty(),
    body("description", "please provide description of role").trim().notEmpty(),

];

exports.editRole = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),

    body("roleId", "Please provide role id").trim().notEmpty(),
    body("name", "Please provide name").trim().notEmpty(),
    body("description", "Please provide description").trim().notEmpty(),

    body("portalType", "Please provide portal type").trim().notEmpty().isIn(Object.values(VALUES.portals)),

    body("tenantId", "Please provide tenant id").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.TENANT_PORTAL) && !value) {
            return false
        }
        if(portalType === VALUES.portals.TENANT_PORTAL && req.body.branchId) {
            return false;
        }
        return true
    }).withMessage("Please provide tenant id only"),
    body("tenantId", "Please provide tenant id").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.SYSTEM_PORTAL) && value) {
            return false
        }
        return true
    }).withMessage("Please do not provide tenant id"),

    body("branchId", "Please provide branch id").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.BRANCH_PORTAL) && !value ) {
            return false
        }
        if((portalType === VALUES.portals.BRANCH_PORTAL) && !req.body.tenantId) {
            return false;
        }
        return true
    }).withMessage("Please provide branch id and tenant id"),
    body("branchId").custom((value, { req, path, location }) => {
        const portalType = req.body.portalType;
        if((portalType === VALUES.portals.SYSTEM_PORTAL) && value) {
            return false
        }
        return true
    }).withMessage("Please do not provide branch id"),
]

exports.editRolePermission = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("roleId", "Please provide role id").trim().notEmpty(),
    body("permission", "please provide permission").isObject().withMessage("permission should be an object"),
];

exports.getPortalRoles = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("portalType", "Please provide portal type").trim().notEmpty().isIn(Object.values(VALUES.portals)).withMessage(" please provide valid value for portal type"),
    query("tenantId").optional().custom((value, { req, path, location }) => {
        if(req.query.portalType === VALUES.portals.TENANT_PORTAL && !value) {
            return false;
        }
        return true
    }).withMessage("Please send tenant id")
];

exports.MarkRoleDeleted = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("roleId", "Please provide role id").trim().notEmpty(),
];

exports.getPortalModules = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("portalType", "Please provide role id").trim().notEmpty().isIn(Object.values(VALUES.portals)).withMessage(" please provide valid value for portal type"),
];

