const { body, query, header } = require('express-validator');
const { ORDER_STATUS_TYPES } = require('../../Configs/constants');

exports.getByIdValidator = [
    body("_id", "Please provide record's _id.")
        .trim()
        .notEmpty()
        .isMongoId(),
];

exports.currentPrefix = [
    query("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt(),
    header("userDetails", "Please provide user details").isJSON()
]

exports.sendOrderEmails = [
    body("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt(),
    body("branchId", "please provide branch id").isMongoId(),
    body("salesPersonName", "Please provide sales person name").trim().notEmpty(),
    body("customerLegalName", "Please provide customer name").optional().trim().notEmpty(),
    body("customerRoleId", "Please provide customer id").isMongoId(),
    body("salesPersonRoleId", "Please provide sale person id").isMongoId(),

    body("_id", "Please provide order primary key").isMongoId(),
    body("orderId", "Please provide order id").trim().notEmpty(),
    body("orderDate", "Please provide order date").custom((value) => moment(value).isValid()),
    body("numberOfItems", "Please provide number of items").isInt().toInt(),
    body("totalQty", "Please provide total quantity").isInt().toInt(),
    body("orderAmount", " PLease provide order amount").custom(value => !isNaN(value)),
    body("orderStatus", "Please provide order status").isIn(ORDER_STATUS_TYPES),

    body("shippingAddress", "Please provide shipping address").trim().notEmpty(),
    body("shippingRegion", "Please provide shipping region").trim().notEmpty(),
    body("shippingCity", "Please provide shipping city").trim().notEmpty(),
    body("shippingMobileNumber", "Please provide shipping mobile number").trim().notEmpty(),

    header("userDetails", "Please provide user details").isJSON()
];

exports.salesPersonsBySupervisorId = [
    header("userDetails", "Please provide user details").isJSON(),
    query("supervisorUserRoleId", "Please provide valid supervisor role id").isMongoId(),
    query("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt(),
]

exports.updateBulkDataValidator = [
    body("filter", "Please provide valid filters.")
        .custom(value => {
            return typeof value === "object" && Object.keys(value).length > 0
        }),

    body("updateFields", "Please provide valid updateFields.")
        .custom(value => {
            return typeof value === "object" && Object.keys(value).length > 0
        })
]

exports.tenantBranches = [
    query("tenantId", "Please provide valid tenant id").isInt({ min: 1000 }).toInt()
]
