const { body, query } = require("express-validator")

const {
    headerValidator,
    pageValidator,
    perPageValidator,
    tenantIdBodyValidator,
    timezoneBodyValidator,
    customerExternalIdQueryValidator,
    customerExternalIdBodyValidator
} = require("./CommonValidator")



exports.getAccountBalance = [
    ...headerValidator,
    ...customerExternalIdQueryValidator,

    query("tenantId")
        .optional()
        .toInt()
        .isInt({ min: 1000 })
        .withMessage("Please provide valid 'tenantId'"),

    query("agingDate", "Please provide valid agingDate") //Riyadh's time zone
        .trim()
        .notEmpty()
        .isNumeric()
        .toInt(),
]

exports.getAccountStatements = [
    ...headerValidator,
    ...pageValidator,
    ...perPageValidator,
    ...customerExternalIdQueryValidator,

    query("fromDate", "Please provide valid fromDate") //Riyadh's time zone
        .trim()
        .notEmpty()
        .isNumeric()
        .toInt(),

    query("toDate", "Please provide valid toDate") //Riyadh's time zone
        .trim()
        .notEmpty()
        .isNumeric()
        .toInt(),
]

exports.generateAccountStatementReport = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...timezoneBodyValidator,
    ...customerExternalIdBodyValidator,

    body("fromDate", "Please provide valid fromDate") //Riyadh's time zone
        .trim()
        .notEmpty()
        .isNumeric()
        .toInt(),

    body("toDate", "Please provide valid toDate") //Riyadh's time zone
        .trim()
        .notEmpty()
        .isNumeric()
        .toInt(),

    body("agingSecondaryTitle", "Please provide valid agingSecondaryTitle")
        .optional()
        .trim()
        .notEmpty(),

    body("statementSecondaryTitle", "Please provide valid statementSecondaryTitle")
        .optional()
        .trim()
        .notEmpty(),
]
