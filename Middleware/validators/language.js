const {
    header,
    body,
    query,
} = require('express-validator');

const path = require('path');

exports.addLanguage = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("languageCode", "Please provide language code").trim().notEmpty(),
    body("name", "Please provide language code").trim().notEmpty(),
    body("enableRtl", "Please provide orantation").trim().isIn(["true", "false"]),
],

exports.editLanguage = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("languageId", "Please provide language id").trim().notEmpty(),
    body("enableRtl", "Please provide orantation").trim().isIn(["true", "false"]),
    body("translationFile").optional().isArray({ min: 1, max: 1}).custom((value, { req, reqPath, location }) => {
        const ext = path.extname(value[0]);
        if(!ext || ext !== ".csv") {
            return false
        }
        return true
    }).withMessage("Please provide translation file in csv format"),
],

exports.sampleCsv = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
];

exports.addKey = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("type", "Please provide type parameter").trim().notEmpty().isIn(["web", "tab", "web-tab", "both"]),
    body("key", "please provide language key").trim().notEmpty(),
    body("both", "Please provide both paramter").custom((value, { req, path, location }) => {
        if(req.body.type === "both" && !value) {
            return false
        }
        return true;
    }),
    body("web", "Please provide web paramter").custom((value, { req, path, location }) => {
        if((req.body.type === "web" || req.body.type === "web-tab") && !value) {
            return false
        }
        return true;
    }),
    body("tab", "Please provide tab paramter").custom((value, { req, path, location }) => {
        if((req.body.type === "tab" ||  req.body.type === "web-tab") && !value) {
            return false
        }
        return true;
    }),
]

exports.editLanguageKey = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("languageId", "Please provide language id").trim().notEmpty(),
    body("languageCode", "Please provide language code").trim().notEmpty(),
    body("key", "Please provide language key").trim().notEmpty(),
    body("type", "Please provide type parameter").trim().notEmpty().isIn([ "web-tab", "both"]),
    body("both", "Please provide both paramter").custom((value, { req, path, location }) => {
        if(req.body.type === "both" && !value) {
            return false
        }
        return true;
    }),
    body("web", "Please provide web paramter").custom((value, { req, path, location }) => {
        if((req.body.type === "web-tab") && !value) {
            return false
        }
        return true;
    }),
    body("tab", "Please provide tab paramter").custom((value, { req, path, location }) => {
        if((req.body.type === "web-tab") && !value) {
            return false
        }
        return true;
    }),
    body("hasTypeChanged", "Please provide valid value of hasTypeChanged").optional().isBoolean(),
];

exports.deleteKey = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    query("key", "Please provide language key").trim().notEmpty(),
]