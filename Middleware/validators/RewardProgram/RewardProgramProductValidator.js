const {
    body,
    query
} = require("express-validator")

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const {
    headerValidator,
    tenantIdBodyValidator,
    tenantIdQueryValidator,
    nameBodyValidator,
    secondaryLanguageNameBodyValidator,
    getAttributeValidator,
    customerUserRoleIdBodyValidator,
    perPageValidator,
    pageValidator,
    searchKeyValidator,
    isActiveValidator,
    statusValidator
} = require('../CommonValidator');

const {
    rewardProgramIdBodyValidator,
    rewardProgramMemberIdBodyValidator,
} = require("./RewardProgramValidator")

const rewardProductIdBodyValidator = [
    body("rewardProductId", "Please provide rewardProductId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid rewardProductId"),
]

const rewardProductInfoBodyValidator = [
    body("requiredCoins", "Please provide valid requiredCoins")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),

    body("tags", "Please provide tags")
        .isArray({ min: 0 }),

    body("tags.*", "Please provide valid tags")
        .trim()
        .notEmpty()
        .bail()
        .isLength({
            min: 3,
        })
        .withMessage("Tags must be at least 3 characters long"),

    body("inventory", "Please provide valid inventory")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("customImageName", "Please provide valid customImageName")
        .optional()
        .trim()
        .notEmpty(),

    body("productImageName", "Please provide valid productImageName")
        .optional()
        .trim()
        .notEmpty(),

    body("isFeatured", "Please provide valid isFeatured")
        .isBoolean()
        .toBoolean(),

    body("isActive", "Please provide valid isActive")
        .isBoolean()
        .toBoolean(),
]

exports.addRewardProduct = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    ...nameBodyValidator,
    ...secondaryLanguageNameBodyValidator,
    ...rewardProductInfoBodyValidator,

    body("itemNumber", "Please provide itemNumber")
        .trim()
        .notEmpty(),

    body("productVariantId", "Please provide productVariantId")
        .if(
            body("type")
                .equals(REWARD_PROGRAM.REWARD.TYPE.FROM_PRODUCT)
        )
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid productVariantId"),

    body("type")
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.REWARD.TYPE))
        .withMessage(
            `Please provide type within ${Object.values(REWARD_PROGRAM.REWARD.TYPE)}`
        ),
]

exports.listRewardProduct = [
    ...getAttributeValidator,

    query("onlyFeatured", "Please provide valid onlyFeatured")
        .optional()
        .isBoolean()
        .toBoolean(),

    query("availableCoins", "Please provide valid availableCoins")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    query("tagIds", "Please provide tagIds")
        .optional()
        .isArray({ min: 1 }),

    query("tagIds.*", "Please provide valid tagIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
]

exports.deleteRewardProduct = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("rewardProductIds", "Please provide rewardProductIds")
        .isArray({ min: 1 }),

    query("rewardProductIds.*", "Please provide valid rewardProductIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
]

exports.updateRewardProducts = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...isActiveValidator,

    body("rewardProductIds", "Please provide rewardProductIds")
        .isArray({ min: 1 }),

    body("rewardProductIds.*", "Please provide valid rewardProductIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
]

exports.updateRewardProduct = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...rewardProductInfoBodyValidator,
    ...rewardProductIdBodyValidator
]

exports.listRewardProductTags = [
    ...headerValidator,
    ...getAttributeValidator,
]

exports.claimRewardProduct = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...rewardProductIdBodyValidator,
    ...rewardProgramIdBodyValidator,
    ...rewardProgramMemberIdBodyValidator,
    ...customerUserRoleIdBodyValidator,
]

exports.updateRewardProductClaimsStatus = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    body("status")
        .optional()
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.REWARD.CLAIM.STATUS))
        .withMessage(
            `Please provide status within ${Object.values(REWARD_PROGRAM.REWARD.CLAIM.STATUS)}`
        ),

    body("claimIds", "Please provide claimIds")
        .isArray({ min: 1 }),

    body("claimIds.*", "Please provide valid claimIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
]

exports.listRewardProductClaims = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...searchKeyValidator,
    ...pageValidator,
    ...perPageValidator,

    query("claimStatus")
        .optional()
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.REWARD.CLAIM.STATUS))
        .withMessage(
            `Please provide claimStatus within ${Object.values(REWARD_PROGRAM.REWARD.CLAIM.STATUS)}`
        ),
]
