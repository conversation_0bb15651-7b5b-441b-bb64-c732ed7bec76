const {
    query
} = require("express-validator")

const {
    REWARD_PROGRAM,
    DURATION_PERIOD_OPTIONS
} = require("../../../Configs/constants")

const {
    headerValidator,
    tenantIdQueryValidator,
    timezoneQueryValidator
} = require('../CommonValidator');

const {
    rewardProgramIdOptionalQueryValidator
} = require("./RewardProgramValidator")

const pointTypeQueryValidator = [
    query("pointType")
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.POINT.TYPE))
        .withMessage(
            `Please provide pointType within ${Object.values(REWARD_PROGRAM.POINT.TYPE)}`
        ),
]

exports.getDashboardStatistics = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...rewardProgramIdOptionalQueryValidator,
    ...timezoneQueryValidator,

    query("year", "Please provide valid year")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    query("pointType")
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.POINT.TYPE))
        .withMessage(
            `Please provide pointType within ${Object.values(REWARD_PROGRAM.POINT.TYPE)}`
        ),
]

exports.getDashboardHistoricDistributions = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...rewardProgramIdOptionalQueryValidator,
    ...pointTypeQueryValidator,
    ...timezoneQueryValidator,

    query("durationPeriod")
        .trim()
        .isIn(Object.values(DURATION_PERIOD_OPTIONS))
        .withMessage(
            `Please provide durationPeriod within ${Object.values(DURATION_PERIOD_OPTIONS)}`
        ),
]
