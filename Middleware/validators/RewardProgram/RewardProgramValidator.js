const {
    body,
    query
} = require("express-validator")

const {
    headerValidator,
    tenantIdBodyValidator,
    tenantIdQueryValidator,
    isActiveValidator,
    nameBodyValidator,
    secondaryLanguageNameBodyValidator,
    getAttributeValidator
} = require('../CommonValidator');

exports.rewardProgramIdBodyValidator = [
    body("rewardProgramId", "Please provide rewardProgramId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid rewardProgramId"),
]

exports.rewardProgramIdQueryValidator = [
    query("rewardProgramId", "Please provide rewardProgramId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid rewardProgramId"),
]

exports.rewardProgramIdOptionalQueryValidator = [
    query("rewardProgramId", "Please provide rewardProgramId")
        .optional()
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid rewardProgramId"),
]

exports.rewardProgramMemberIdBodyValidator = [
    body("rewardProgramMemberId", "Please provide rewardProgramMemberId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid rewardProgramMemberId"),
]

exports.rewardProgramMemberIdQueryValidator = [
    query("rewardProgramMemberId", "Please provide rewardProgramMemberId")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId()
        .withMessage("Please provide valid rewardProgramMemberId"),
]

const milestonesBodyValidator = [
    body("milestones", "Please provide milestones upto 3 objects")
        .isArray({
            max: 3
        })
        .bail()
        .custom((milestones) => {
            for (let i = 1; i < milestones.length; i++) {
                if (milestones[i].coinsRequired <= milestones[i - 1].coinsRequired) {
                    throw new Error(
                        'coinsRequired must be unique and in ascending order.'
                    );
                }
            }
            return true;
        }),

    body("milestones.*.coinsRequired", "Please provide valid coinsRequired in milestones")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),

    body("milestones.*.bonusCoins", "Please provide valid bonusCoins in milestones")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),
]

const vipRulesBodyValidator = [
    body("vipRules.upgradePoints", "Please provide valid 'vipRules.upgradePoints'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),

    body("vipRules.renewPoints", "Please provide valid 'vipRules.renewPoints'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),
]

const rewardProgramInfoBodyValidator = [
    ...headerValidator,
    ...tenantIdBodyValidator,

    ...milestonesBodyValidator,
    ...vipRulesBodyValidator,

    ...nameBodyValidator,
    ...secondaryLanguageNameBodyValidator,

    ...isActiveValidator,
]

const classicMemberCoinRulesBodyValidations = [
    body("classicMemberCoinRules.invoice.purchase", "Please provide valid 'classicMemberCoinRules.invoice.purchase'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.invoice.cancellation", "Please provide valid 'classicMemberCoinRules.invoice.cancellation'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.payment.0_30_days", "Please provide valid 'classicMemberCoinRules.payment.0_30_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.payment.31_60_days", "Please provide valid 'classicMemberCoinRules.payment.31_60_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.payment.61_90_days", "Please provide valid 'classicMemberCoinRules.payment.61_90_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.payment.91_120_days", "Please provide valid 'classicMemberCoinRules.payment.91_120_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.payment.120_days_above", "Please provide valid 'classicMemberCoinRules.payment.120_days_above'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.creditNotes.returns", "Please provide valid 'classicMemberCoinRules.creditNotes.returns'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.creditNotes.discounts", "Please provide valid 'classicMemberCoinRules.creditNotes.discounts'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.actionTypes.dailyAccess", "Please provide valid 'classicMemberCoinRules.actionTypes.dailyAccess'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.actionTypes.memberScan.coins", "Please provide valid 'classicMemberCoinRules.actionTypes.memberScan.coins'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("classicMemberCoinRules.actionTypes.memberScan.scanDurationLimit", "Please provide valid 'classicMemberCoinRules.actionTypes.memberScan.scanDurationLimit'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),
]

const vipMemberCoinRulesBodyValidations = [
    body("vipMemberCoinRules.invoice.purchase", "Please provide valid 'vipMemberCoinRules.invoice.purchase'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.invoice.cancellation", "Please provide valid 'vipMemberCoinRules.invoice.cancellation'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.payment.0_30_days", "Please provide valid 'vipMemberCoinRules.payment.0_30_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.payment.31_60_days", "Please provide valid 'vipMemberCoinRules.payment.31_60_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.payment.61_90_days", "Please provide valid 'vipMemberCoinRules.payment.61_90_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.payment.91_120_days", "Please provide valid 'vipMemberCoinRules.payment.91_120_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.payment.120_days_above", "Please provide valid 'vipMemberCoinRules.payment.120_days_above'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.creditNotes.returns", "Please provide valid 'vipMemberCoinRules.creditNotes.returns'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.creditNotes.discounts", "Please provide valid 'vipMemberCoinRules.creditNotes.discounts'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.actionTypes.dailyAccess", "Please provide valid 'vipMemberCoinRules.actionTypes.dailyAccess'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),
    body("vipMemberCoinRules.actionTypes.memberScan.coins", "Please provide valid 'vipMemberCoinRules.actionTypes.memberScan.coins'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipMemberCoinRules.actionTypes.memberScan.scanDurationLimit", "Please provide valid 'vipMemberCoinRules.actionTypes.memberScan.scanDurationLimit'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),
]

const vipPointsRulesBodyValidations = [
    body("vipPointsRules.payment.0_30_days", "Please provide valid 'vipPointsRules.payment.0_30_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipPointsRules.payment.31_60_days", "Please provide valid 'vipPointsRules.payment.31_60_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipPointsRules.payment.61_90_days", "Please provide valid 'vipPointsRules.payment.61_90_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipPointsRules.payment.91_120_days", "Please provide valid 'vipPointsRules.payment.91_120_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("vipPointsRules.payment.120_days_above", "Please provide valid 'vipPointsRules.payment.120_days_above'")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),
]

const baseAmountBodyValidator = [
    body("baseAmount", "Please provide valid baseAmount")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),
]

const rewardProgramConfigurationInfoBodyValidator = [
    ...baseAmountBodyValidator,
    ...classicMemberCoinRulesBodyValidations,
    ...vipMemberCoinRulesBodyValidations,
    ...vipPointsRulesBodyValidations,
]

exports.addRewardProgram = [
    ...rewardProgramInfoBodyValidator,
    ...rewardProgramConfigurationInfoBodyValidator
]

exports.updateRewardProgram = [
    this.rewardProgramIdBodyValidator,
    ...rewardProgramInfoBodyValidator,
    ...rewardProgramConfigurationInfoBodyValidator
]

exports.listRewardProgram = [
    ...getAttributeValidator,
]

exports.updateRewardProgramConfiguration = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...rewardProgramConfigurationInfoBodyValidator
]

exports.getRewardProgramConfiguration = [
    ...headerValidator,
    ...tenantIdQueryValidator,
]
