const {
    body,
    query
} = require("express-validator")

const {
    headerValidator,
    tenantIdBodyValidator,
    tenantIdQueryValidator,
    getAttributeValidator,
    customerUserRoleIdQueryValidator,
    timezoneBodyValidator
} = require('../CommonValidator');

const {
    rewardProgramIdOptionalQueryValidator,
    rewardProgramIdBodyValidator,
} = require("./RewardProgramValidator");
const { usedForMobileQueryValidator } = require("../common");

exports.enrollMembers = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...rewardProgramIdBodyValidator,

    /*
    FIX:ME Currently we are considering tenant's time zone, but SAP server is from riyadh so we need to define logic for get timezone as per SAP server
    */
    ...timezoneBodyValidator,

    body("effectiveDate", "Please provide valid effectiveDate")
        .trim()
        .notEmpty()
        .bail()
        .isDate({
            format: "YYYY/MM/DD"
        })
        .custom((value, { req }) => {
            const timezone = req.body.timezone
            const inputDate = moment(value).tz(timezone);
            const today = moment().tz(timezone);
            const from = moment().subtract(3, "months").subtract(1, "days").tz(timezone);

            if (inputDate >= today || from >= inputDate) {
                throw new Error(`Effective date (${value}) should be less than upto 3 months or equal to today's date`)
            }
            return true
        }),

    body("customerUserRoleIds", "Please provide customerUserRoleIds")
        .isArray({ min: 1 }),

    body("customerUserRoleIds.*", "Please provide valid customerUserRoleIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    body("customerPaymentTermInfo.id", "Please provide valid 'customerPaymentTermInfo.id'")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    body("customerPaymentTermInfo.numberOfDays", "Please provide valid 'customerPaymentTermInfo.numberOfDays'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),
]

exports.listMembers = [
    ...getAttributeValidator,
]

exports.memberDetails = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...customerUserRoleIdQueryValidator,
    ...usedForMobileQueryValidator,
]

exports.countMembers = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...rewardProgramIdOptionalQueryValidator,
]

exports.deactivateMembers = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("customerUserRoleIds", "Please provide customerUserRoleIds")
        .isArray({ min: 1 }),

    query("customerUserRoleIds.*", "Please provide valid customerUserRoleIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),
]
