const {
    body,
    query
} = require("express-validator")

const {
    REWARD_PROGRAM,
} = require("../../../Configs/constants")

const {
    headerValidator,
    tenantIdBodyValidator,
    tenantIdQueryValidator,
    customerExternalIdQueryValidator,
    getAttributeValidator,
    customerUserRoleIdBodyValidator,
    customerUserRoleIdQueryValidator,
    timezoneBodyValidator
} = require('../CommonValidator');

const {
    rewardProgramIdQueryValidator,
    rewardProgramIdBodyValidator,
    rewardProgramMemberIdBodyValidator,
    rewardProgramMemberIdQueryValidator,
} = require("./RewardProgramValidator");
const { usedForMobileQueryValidator } = require("../common");

exports.addManualPoints = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...rewardProgramIdBodyValidator,
    ...rewardProgramMemberIdBodyValidator,
    ...customerUserRoleIdBodyValidator,

    body("points", "Please provide valid points")
        .trim()
        .notEmpty()
        .bail()
        .isInt()
        .toInt(),

    body("description", "Please provide valid description")
        .trim()
        .notEmpty(),

    body("pointType")
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.POINT.TYPE))
        .withMessage(
            `Please provide actionType within ${Object.values(REWARD_PROGRAM.POINT.TYPE)}`
        ),
]

exports.listPointsLogs = [
    ...getAttributeValidator,

    ...usedForMobileQueryValidator,
    ...rewardProgramIdQueryValidator,
    ...rewardProgramMemberIdQueryValidator,
]

exports.addDailyAccessPoints = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...customerUserRoleIdBodyValidator,
    ...timezoneBodyValidator,
]

exports.getQrCodeForMemberScan = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...customerUserRoleIdQueryValidator,
    ...rewardProgramIdQueryValidator,
]

exports.calculateSAPPoints = [
    body("paymentAmount", "Please provide valid amount")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 1 })
        .toInt(),

    body("aging.balance_0_30_days", "Please provide valid 'aging.balance_0_30_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("aging.balance_31_60_days", "Please provide valid 'aging.balance_31_60_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("aging.balance_61_90_days", "Please provide valid 'aging.balance_61_90_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("aging.balance_91_120_days", "Please provide valid 'aging.balance_91_120_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("reward.baseAmount", "Please provide valid 'reward.baseAmount'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("reward.payment.0_30_days", "Please provide valid 'reward.payment.0_30_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("reward.payment.31_60_days", "Please provide valid 'reward.payment.31_60_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("reward.payment.61_90_days", "Please provide valid 'reward.payment.61_90_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),

    body("reward.payment.91_120_days", "Please provide valid 'reward.payment.91_120_days'")
        .trim()
        .notEmpty()
        .bail()
        .isInt({ min: 0 })
        .toInt(),
]

exports.checkSAPPoints = [
    ...rewardProgramIdQueryValidator,
    ...customerExternalIdQueryValidator,

    query("date", "Please provide valid date")
        .trim()
        .notEmpty()
        .isNumeric()
        .toInt(),

    query("membership")
        .trim()
        .isIn(Object.values(REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE))
        .withMessage(
            `Please provide membership within ${Object.values(REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE)}`
        ),

    query("configurations.base_url", "Please provide 'configurations.base_url'")
        .trim()
        .notEmpty()
        .bail()
        .isURL()
        .withMessage("Please provide valid 'configurations.base_url'"),

    query("configurations.username", "Please provide valid 'configurations.username'")
        .trim()
        .notEmpty(),

    query("configurations.password", "Please provide valid 'configurations.password'")
        .trim()
        .notEmpty()
]
