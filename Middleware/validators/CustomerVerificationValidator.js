const { body } = require("express-validator")

const { VERIFY_CUSTOMER_ACTIONS } = require("../../Configs/constants")
const { headerValidator } = require("./CommonValidator")

exports.verifyCustomerValidator = [
    ...headerValidator,

    body("action")
        .isIn(Object.values(VERIFY_CUSTOMER_ACTIONS))
        .withMessage(`Action must be within ${Object.values(VERIFY_CUSTOMER_ACTIONS)}`),

    body("countryCode", "Please provide a country code.")
        .if(
            body("action")
                .equals(VERIFY_CUSTOMER_ACTIONS.SEND_OTP)
        )
        .trim()
        .notEmpty(),

    body("mobileNumber", "Please provide a mobile number.")
        .trim()
        .notEmpty(),

    body("otp", "Please provide otp.")
        .if(
            body("action")
                .equals(VERIFY_CUSTOMER_ACTIONS.VERIFY_OTP)
        )
        .notEmpty(),
]
