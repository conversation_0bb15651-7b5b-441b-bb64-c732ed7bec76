const {
    header,
    body,
    query,
} = require('express-validator');

const path = require("path")

const {
    headerValidator,
    searchKeyValidator,
} = require('./CommonValidator');

const {
    VALUES,
    LISTING_TYPES,
    SIGNING_TYPE,
    REGEX,
    ENTITY_STATUS,
    OBJECT_UPLOAD_SIGNATURE_TYPE,
    VALID_IMAGES_EXTS,
    SIGNATURE_EXT_MAPPING,
} = require('../../Configs/constants');

const validator = require("validator")

exports.addCountry = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("countryCode", "Please provide country code").trim().notEmpty().custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code").isLength({ max: 5 }).withMessage("max 4 characters"),
    body("countryName", "Please provide country name").trim().notEmpty(),
    body("timeZone", "Please provide time zone").trim().notEmpty(),
    body("mobileNumberFormat", "Please provide mobile number format").trim().notEmpty(),
    body("currency", "Please provide Currency").trim().notEmpty().isLength({ max: 4 }).withMessage("max 4 characters"),
    body("alphaTwoCode", "Please provide alpha two code").trim().notEmpty().isLength({ max: 4 }).withMessage("max 4 characters"),
    body("isActive", "Please provide is active").isBoolean(),
    // body("vat").isFloat({min: 0}).withMessage("Please provide vat parameter"),
    body("secondaryLanguage", "Please provide secondary language").trim().notEmpty(),
    body("googlePlaceId", "Please provide google place id").trim().notEmpty(),
    body("secondaryLanguageName", "Please provide secondary language name").trim().notEmpty(),
];

exports.editCountry = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("countryCode", "Please provide country code").trim().notEmpty().custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code").isLength({ max: 5 }).withMessage("max 4 characters"),
    body("countryName", "Please provide country name").trim().notEmpty(),
    body("timeZone", "Please provide time zone").trim().notEmpty(),
    body("mobileNumberFormat", "Please provide mobile number format").trim().notEmpty(),
    body("currency", "Please provide Currency").trim().notEmpty().isLength({ max: 4 }).withMessage("max 4 characters"),
    body("alphaTwoCode", "Please provide alpha two code").trim().notEmpty().isLength({ max: 4 }).withMessage("max 4 characters"),
    body("isActive", "Please provide is active").isBoolean(),
    // body("vat").isFloat({min: 0}).withMessage("Please provide vat parameter"),
    body("countryId", "please provide country id").trim().notEmpty().isMongoId(),
    body("googlePlaceId", "Please provide google place id").trim().notEmpty(),
    body("secondaryLanguage", "Please provide secondary language").trim().notEmpty(),
    body("secondaryLanguageName", "Please provide secondary language name").trim().notEmpty(), // this is ful form of language name
];

exports.deleteCountry = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    query("countryId", "please provide country id").trim().notEmpty().isMongoId()
]

exports.searchUsers = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    query('type', 'Please provide type').trim().isIn(Object.values(SIGNING_TYPE)).notEmpty(),
    query('countryCode').custom((value, { req, path, location }) => {
        if (req.body.type === SIGNING_TYPE.MOBILE && !value) {
            return false;
        }
        return true;
    }).withMessage('Please provide country code'),
    query('mobileNumber').trim().custom((value, { req, path, location }) => {
        if (req.body.type === SIGNING_TYPE.MOBILE && !value) {
            return false;
        }
        return true;
    }).withMessage('Please provide mobile number'),
    query('email').trim().custom((value, { req, path, location }) => {
        if (req.body.type === SIGNING_TYPE.EMAIL && !value) {
            return false;
        }
        return true;
    }).withMessage('Please provide email'),
];

exports.getCountries = [
    ...searchKeyValidator,

    query("type", "Please provide valid 'type'")
        .optional()
        .trim()
        .notEmpty()
        .isIn(Object.values(LISTING_TYPES))
        .withMessage(`type parameter should be in ${Object.values(LISTING_TYPES).join(", ")}`)
];

exports.changeCountryStatus = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("countryId", "please provide country id").trim().notEmpty().isMongoId(),
    body("status", "Please provide status").trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`Status should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
];

exports.addRegion = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("countryId", "Please country code").trim().notEmpty().isMongoId(),
    body("isActive", "please send status").isBoolean(),
    body("name", "please provide name").trim().notEmpty(),
    body("code", "please provide code").optional().trim().notEmpty(),
    // body("secondaryLanguage", "Please provide secondary language").trim().notEmpty(),
    body("secondaryLanguageName", "Please provide name in secondary language").trim().notEmpty()
];
exports.editRegion = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("regionId", "Please Provide region id").trim().notEmpty().isMongoId(),
    body("countryId", "Please country code").trim().notEmpty().isMongoId(),
    body("isActive", "please send status").isBoolean(),
    body("name", "please provide name").trim().notEmpty(),
    body("code", "please provide code").optional().trim().notEmpty(),
    // body("secondaryLanguage", "Please provide secondary language").trim().notEmpty(),
    body("secondaryLanguageName", "Please provide name in secondary language").trim().notEmpty()
];

exports.changeRegionStatus = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("regionId", "please provide country id").trim().notEmpty().isMongoId(),
    body("status", "Please provide status").trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`Status should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
];

exports.deleteRegion = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    query("regionId", "Please Provide region id").trim().notEmpty().isMongoId(),
]

exports.getRegions = [
    ...headerValidator,
    ...searchKeyValidator,

    query('countryId', "Please provide country id").optional().trim().notEmpty().isMongoId(),
    query("type", "Please provide type parameter").optional().trim().notEmpty().isIn(Object.values(LISTING_TYPES)).withMessage(`type parameter should be in ${Object.values(LISTING_TYPES).join(", ")}`),
    query("status").optional().trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`status should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
];

exports.getCities = [
    ...headerValidator,
    ...searchKeyValidator,

    query("cityIds", "Please provide as least one value in cityIds")
        .optional()
        .isArray({ min: 1 }),

    query("cityIds.*", "Please provide valid cityIds")
        .trim()
        .notEmpty()
        .isMongoId(),

    query("projections", "Please provide as least one value in projections")
        .optional()
        .isArray({ min: 1 }),

    query("projections.*", "Please provide valid projections")
        .trim()
        .notEmpty(),

    query('countryId', "Please provide country id")
        .optional()
        .trim()
        .notEmpty()
        .isMongoId(),

    query('regionId', "Please provide region id")
        .optional()
        .trim()
        .notEmpty()
        .isMongoId(),

    query("type", "Please provide type parameter")
        .optional()
        .trim()
        .notEmpty()
        .isIn(Object.values(LISTING_TYPES)).withMessage(`type parameter should be in ${Object.values(LISTING_TYPES).join(", ")}`),

    query("status")
        .optional()
        .trim()
        .notEmpty()
        .isIn(Object.values(ENTITY_STATUS)).withMessage(`status should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
]

exports.addCity = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("countryId", "Please country code").trim().notEmpty().isMongoId(),
    body("regionId", "Please country code").trim().notEmpty().isMongoId(),
    body("isActive", "please send status").isBoolean(),
    body("name", "please provide name").trim().notEmpty(),
    body("secondaryLanguageName", "Please provide name in secondary language").trim().notEmpty()
];

exports.automaticAddCity = [
    ...headerValidator,

    body("cityName", "Please provide valid 'cityName'")
        .trim()
        .notEmpty(),

    body("countryName", "Please provide valid 'countryName'")
        .trim()
        .notEmpty(),

    body("regionName", "Please provide valid 'regionName'")
        .trim()
        .notEmpty(),

    body("regionCode", "Please provide valid 'regionCode'")
        .optional()
        .trim(),
]

exports.editCity = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("cityId", "Please provide city id").trim().notEmpty().isMongoId(),
    body("countryId", "Please country code").trim().notEmpty().isMongoId(),
    body("regionId", "Please country code").trim().notEmpty().isMongoId(),
    body("isActive", "please send status").isBoolean(),
    body("name", "please provide name").trim().notEmpty(),
    body("secondaryLanguageName", "Please provide name in secondary language").trim().notEmpty()
];

exports.getLanguages = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
];

exports.changeCitiesStatus = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("status", "Please provide status").trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`Status should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
    body("cities", "Please provide cities").isArray({ min: 1 }).withMessage("Please provide cities")
];

exports.deleteCities = [
    body("cities", "Please provide cities").isArray({ min: 1 }).withMessage("Please provide cities"),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
];

exports.changeUserPassword = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("userId", "Please provide user id").trim().notEmpty().isMongoId(),
    body('type', "Please provide type parameter").trim().notEmpty().isIn(Object.values(VALUES.PASSWORD_RESET_TYPE)),
    body("password").custom((value, { req, path, location }) => {
        if (req.body.type === VALUES.PASSWORD_RESET_TYPE.MANUAL && !value) {
            return false;
        }
        return true;
    }).withMessage("please provide password"),
];

exports.changeUserProfilePic = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("userId", "Please provide user id").trim().notEmpty().isMongoId(),
    body("profilePic").optional().isArray({ min: 1, max: 1 }) // it should be undefined if user wants to delete the profile pic
]

exports.getUploadSignatureValidator = [
    ...headerValidator,
    body("tenantId")
        .custom((value, { req, path, location }) => {
            if ((req.body.type !== OBJECT_UPLOAD_SIGNATURE_TYPE.SYSTEM_USER_PROFILE) && !value) {
                return false
            }
            return true
        })
        .withMessage(`Please send valid tenant id`),

    body("type", "Please provide valid signature type.")
        .trim()
        .notEmpty()
        .bail()
        .isIn(Object.values(OBJECT_UPLOAD_SIGNATURE_TYPE))
        .withMessage(`Upload signature type must be ${Object.values(OBJECT_UPLOAD_SIGNATURE_TYPE).join(" or ")}`),

    body("standId", "Please provide valid standId.")
        .if(
            body("type")
                .equals(OBJECT_UPLOAD_SIGNATURE_TYPE.STAND_IMAGES)
        )
        .trim()
        .notEmpty(),

    body("customerUserRoleId", "Please provide valid customerUserRoleId.")
        .if(
            body("type")
                .equals(OBJECT_UPLOAD_SIGNATURE_TYPE.STAND_IMAGES)
        )
        .trim()
        .isMongoId(),

    body("fileName", 'Please provide valid file name.')
        .custom((value, { req }) => {
            let { type, fileName } = req.body

            const validImgExtensions =
                SIGNATURE_EXT_MAPPING[type] ||
                VALID_IMAGES_EXTS

            const ERROR_MESSAGE = {
                INVALID: "Please provide valid file name.",
                AT_LEAST_ONE: "Please provide at least one file name.",
                INVALID_EXT: `File extension must be ${Object.values(validImgExtensions).join(" or ")}`,
                INVALID_TYPE: "Please provide file name in 'string' or 'array'.",
            }

            if (type) {
                const isValidType = Object.values(OBJECT_UPLOAD_SIGNATURE_TYPE).includes(type)

                if (isValidType) {
                    if (!fileName) {
                        throw new Error(ERROR_MESSAGE.INVALID)
                    }
                    else if (Array.isArray(fileName)) {
                        if (!fileName.length) {
                            throw new Error(ERROR_MESSAGE.AT_LEAST_ONE)
                        }
                        let error = ""

                        for (let fName of fileName) {
                            if (["string", "number"].includes(typeof fName)) {
                                fName = String(fName).trim()

                                if (fName) {
                                    const ext = path.extname(fName)

                                    if (!validImgExtensions.includes(ext.toLocaleLowerCase())) {
                                        error = ERROR_MESSAGE.INVALID_EXT
                                        break
                                    }
                                }
                                else {
                                    error = ERROR_MESSAGE.INVALID
                                    break
                                }
                            }
                            else {
                                error = ERROR_MESSAGE.INVALID_TYPE
                                break
                            }
                        }

                        if (error) {
                            throw new Error(error)
                        }
                    }
                    else if (["string", "number"].includes(typeof fileName)) {
                        fileName = String(fileName).trim()

                        if (fileName) {
                            const ext = path.extname(fileName)

                            if (!validImgExtensions.includes(ext.toLocaleLowerCase())) {
                                throw new Error(ERROR_MESSAGE.INVALID_EXT)
                            }
                        }
                        else {
                            throw new Error(ERROR_MESSAGE.INVALID)
                        }
                    }
                    else {
                        throw new Error(ERROR_MESSAGE.INVALID_TYPE)
                    }
                }
            }
            return true
        })
]

exports.imageNameValidator = [
    body("imageName", 'Please provide valid image name.')
        .trim()
        .notEmpty()
        .custom(value => {
            const ext = path.extname(value)
            const userRoleId = path.basename(value, ext);

            if (!VALID_IMAGES_EXTS.includes(ext)) {
                throw new Error(`Image extension must be in ${Object.values(VALID_IMAGES_EXTS).join(", ")}`)
            }

            if (!validator.isMongoId(userRoleId)) {
                throw new Error(`Please enter valid userRoleId`)
            }
            return true
        })
]

exports.addImageValidator = [
    ...headerValidator,
    ...this.imageNameValidator,
];

exports.deleteImageValidator = [
    ...headerValidator,
    ...this.imageNameValidator,
];

exports.usedForMobileQueryValidator = [
    query("usedForMobile", "Please send usedForMobile value either true or false.")
        .default(false)
        .isBoolean()
        .toBoolean()
]
