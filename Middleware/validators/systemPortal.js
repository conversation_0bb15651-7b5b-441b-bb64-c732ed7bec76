const {
    header,
    body,
    query,
} = require("express-validator");

const {
    VALUES,
    REGEX,
    ENTITY_STATUS,
    SALES_TYPE,
    DURATION_PERIOD_OPTIONS,
    TENANT_QUERY_TYPE_ENUM,
} = require('../../Configs/constants');

const {
    tenantIdQueryValidator,
    headerValidator,
    pageValidator,
    perPageValidator,
    searchKeyValidator,
} = require('./CommonValidator');

exports.tenantDefaults = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
];

exports.addTenants = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),

    body("details", "Please provide tenant details object").isObject(),
    body("services", "Please provide tenant services object").isObject(),
    body("branches", "Please provide tenant branches").isArray({min : 1 }),
    body("advanced", "Please provide tenant advanced object").isObject(),
];

exports.updateTenant = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").trim().notEmpty(),
    body("details", "Please provide tenant details object").isObject(),
    body("services", "Please provide tenant services object").isObject(),
    body("advanced", "Please provide tenant advanced object").isObject(),
];

exports.getTenant = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query('tenantId', 'please provide tenant id').trim().notEmpty(),
    query('tenantQueryType', "Please provide valid query type").optional().isIn(Object.values(TENANT_QUERY_TYPE_ENUM)),
    query('projection', "Please provide valid projection value").optional().trim().notEmpty()
];

exports.addBranch = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("tenantId", "Please provide tenant id").isInt({min: 1000}),
    body("name", "Please provide name").trim().notEmpty(),
]

exports.editTenantBranch = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("tenantId", "Please provide tenant id").isInt({min: 1000}),
    body("name", "Please provide name").trim().notEmpty(),
    body("branchId", "Please provide branch id").trim().notEmpty().isMongoId(),
]

exports.deleteTenantBranch = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({min: 1000}),
    query("branchId", "Please provide branch id").trim().notEmpty().isMongoId(),
]

exports.getTenants = [
    ...headerValidator,
    ...pageValidator,
    ...perPageValidator,
    ...searchKeyValidator,
];

exports.searchTenant = [
    ...headerValidator,
    ...searchKeyValidator,
]

exports.addLinkedTenant = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("tenantId", "Please provide tenant id").isInt({min: 1000}),
    body("LinkingTenantId", "please provide linking tenant id").isInt({min: 1000}).custom((value, { req, path, location }) => {
        if(parseInt(req.body.tenantId) === parseInt(value)) {
            return false;
        }
        return true
    }).withMessage("tenant id and linking tenant id should not be equal"),
];

exports.removeLinkedTenant = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({min: 1000}),
    query("LinkingTenantId", "please provide linking tenant id").isInt({min: 1000}).custom((value, { req, path, location }) => {
        if(parseInt(req.body.tenantId) === parseInt(value)) {
            return false;
        }
        return true
    }).withMessage("tenant id and linking tenant id should not be equal"),
];

exports.changeTenantOwnerPassword = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("tenantId", "Please provide tenant id").isInt({min: 1000}),
    body('type', "Please provide type parameter").trim().notEmpty().isIn(Object.values(VALUES.PASSWORD_RESET_TYPE)),
    body("password").custom((value, { req, path, location }) => {
        if(req.body.type === VALUES.PASSWORD_RESET_TYPE.MANUAL && !value) {
            return false;
        }

        return true;
    }).withMessage("please provide password"),
];

exports.deleteTenant = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({min: 1000}),
]

exports.getUserDetailsWithRole = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    // query("portalType").isIn(Object.values(VALUES.portals)).withMessage(`Please provide portal type as in ${Object.values(VALUES.portals).join(", ")}`),
    query("userId", "Please provide user id").trim().notEmpty().isMongoId(),
];

exports.addSystemUser = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("firstName", "Please provide first name").trim().notEmpty(),
    body("lastName", "Please provide last name").trim().notEmpty(),
    body("roleId", "Please provide role id").trim().notEmpty().isMongoId(),
    body("email", "Please provide email id").trim().notEmpty().isEmail(),
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("countryCode", "Please provide country code").custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code"),
    body("assignedTenants").optional().isArray({min : 1}),
    body("profilePic").optional().isArray({min: 1}),
    body("isActive", "Please provide is active").trim().notEmpty().custom((value, { req, path, location }) => {
        if (value === "true") {
            req.body.isActive = true;
            return true
        } else if(value === "false") {
            req.body.isActive = false;
            return true;
        } else {
            return false;
        }
    }).withMessage("Please send isActive status in true or false value only"),
];

exports.editSystemUser = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("userRoleId", "Please provide user role id").trim().notEmpty().isMongoId(),
    body("firstName", "Please provide first name").trim().notEmpty(),
    body("lastName", "Please provide last name").trim().notEmpty(),
    body("roleId", "Please provide role id").trim().notEmpty().isMongoId(),
    body("email", "Please provide email id").trim().notEmpty().isEmail(),
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("countryCode", "Please provide country code").custom((value, { req, path, location }) => {
        return REGEX.COUNTRY_CODE.test(value);
    }).withMessage("Pleas provide valid country code"),
    body("isActive", "Please provide is active").isBoolean(),
];
exports.deleteSystemUser = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    query("userId", "Please provide user id").trim().notEmpty(),
]

exports.getSystemUsers = [
    ...headerValidator,
    ...searchKeyValidator,
];

exports.deleteAssignedTenant = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    query("userRoleId", "Please provide user role id").trim().notEmpty(),
    query("tenantId", "Please provide tenant id").isInt({min: 1000}).withMessage("Please provide valid tenant id"),
];

exports.addAssignedTenant = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("userRoleId", "Please provide user role id").trim().notEmpty(),
    body("tenantId", "Please provide tenant id").isInt({min: 1000}).withMessage("Please provide valid tenant id"),
];

exports.getAssignedTenants = [
    ...headerValidator,
    ...searchKeyValidator,

    query("userRoleId", "Please provide user role id").trim().notEmpty(),
];

exports.changeSystemUserStatus = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("users", "Please provide user roles").isArray({min: 1}),
    body("status", "Please provide status").trim().notEmpty().isIn(Object.values(ENTITY_STATUS)).withMessage(`value should be in ${Object.values(ENTITY_STATUS).join(", ")}`),
]

exports.getSalesState = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    query("branchId", "Please provide branch id").trim().notEmpty().isMongoId(),
    query("type", "Please provide type parameter").trim().notEmpty().isIn(Object.values(SALES_TYPE)),
    query("durationPeriod", "Please provide durationPeriod parameter").trim().notEmpty().isIn(Object.values(DURATION_PERIOD_OPTIONS))
]