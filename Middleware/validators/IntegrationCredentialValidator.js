const {
    body,
    query
} = require('express-validator');

const {
    headerValidator,
    tenantIdBodyValidator,
    tenantIdQueryValidator,
    isActiveValidator
} = require('./CommonValidator');

const {
    INTEGRATION_CHANNELS,
} = require('../../Configs/constants');

const addOrEditValidators = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...isActiveValidator,

    body("name", "Please provide 'name'")
        .trim()
        .notEmpty()
        .bail()
        .isIn(Object.values(INTEGRATION_CHANNELS))
        .withMessage(`'name' must be ${Object.values(INTEGRATION_CHANNELS).join(" or ")}`),

    body("configurations", "Please provide 'configurations' with its value.")
        .custom(value => {
            return typeof value === "object" && Object.keys(value).length > 0
        }),

    body("configurations.haveMobileAccess", "Please send 'configurations.haveMobileAccess' value either true or false.")
        .if(
            body("name")
                .equals(INTEGRATION_CHANNELS.SAP_SERVICE)
        )
        .default(true) //Fixe:me remove this line once rewards deployed from web portal
        .trim()
        .bail()
        .isBoolean()
        .toBoolean(),

    body("configurations.isRewardProgramEnabled", "Please send 'configurations.isRewardProgramEnabled' value either true or false.")
        .if(
            body("name")
                .equals(INTEGRATION_CHANNELS.SAP_SERVICE)
        )
        .default(true) //Fixe:me remove this line once rewards deployed from web portal
        .trim()
        .bail()
        .isBoolean()
        .toBoolean(),

    body("configurations.baseUrl", "Please provide 'configurations.baseUrl'")
        .if(
            body("name")
                .equals(INTEGRATION_CHANNELS.SAP_SERVICE)
        )
        .trim()
        .notEmpty()
        .bail()
        .isURL()
        .withMessage("Please provide valid 'configurations.baseUrl'"),

    body("configurations.username", "Please provide valid 'configurations.username'")
        .if(
            body("name")
                .equals(INTEGRATION_CHANNELS.SAP_SERVICE)
        )
        .trim()
        .notEmpty(),
]

exports.addCredentials = [
    ...addOrEditValidators,

    body("configurations.password", "Please provide valid 'configurations.password'")
        .if(
            body("name")
                .equals(INTEGRATION_CHANNELS.SAP_SERVICE)
        )
        .trim()
        .notEmpty(),
];

exports.editCredentials = [
    ...addOrEditValidators,

    body("configurations.password", "Please provide valid 'configurations.password'")
        .optional()
        .trim()
        .notEmpty(),
];

exports.getCredentials = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("names", "Please provide as least one value in names")
        .optional()
        .isArray({ min: 1 }),

    query("names.*", "Please provide valid names")
        .trim()
        .notEmpty()
        .bail()
        .isIn(Object.values(INTEGRATION_CHANNELS))
        .withMessage(`'names' must be ${Object.values(INTEGRATION_CHANNELS).join(" or ")}`),
];
