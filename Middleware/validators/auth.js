const {
    header,
    body,
    query,
} = require('express-validator');

const {
    VALUES,
    SIGNING_TYPE,
    TIME_UNITS,
} = require('../../Configs/constants');

const { 
    headerValidator
} = require('./CommonValidator');

exports.signup = [
    header('deviceType', 'deviceType field is required!').trim().notEmpty().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID, VALUES.DEVICE_TYPE.DESKTOP, VALUES.DEVICE_TYPE.WEB]),
    // header('deviceToken', 'deviceToken field is required!').trim().notEmpty(),
    header('buildVersion', 'build_version field is required!').trim().notEmpty(),
    body('email', 'Please Provide valid email id!').notEmpty().isEmail(),
    body('firstName', 'Please Provide valid first name!').trim().notEmpty(),
    body('lastName', 'Please Provide valid last name!').trim().notEmpty(),
    body('password', 'Please Provide password & make sure it has length between 6 to 16 characters.').trim().notEmpty(),
    body("mobileNumber", "Please Provide mobile number").trim().notEmpty().isMobilePhone(),
    body("countryCode", "Please Provide countryCode").trim().notEmpty(),
];

exports.signIn = [
    body("type", "Please provide type").isIn(Object.values(SIGNING_TYPE)),
    body("email", "Please Provide email").trim().custom((value, { req, path, location }) => {
        if(req.body.type === "EMAIL" && !req.body.email) {
            return false
        }
        return true;
    }).withMessage("Please provide email"),
    body("countryCode", "Please Provide countryCode").trim().custom((value, { req, path, location }) => {
        if(req.body.type === "MOBILE" && !req.body.countryCode) {
            return false
        }
        return true;
    }).withMessage("Please provide countryCode"),
    body("mobileNumber", "Please Provide mobileNumber").custom((value, { req, path, location }) => {
        if(req.body.type === "MOBILE" && !req.body.mobileNumber) {
            return false
        }
        return true;
    }).withMessage("Please provide valid mobileNumber"),
    body("password", "Please Provide password").trim().notEmpty(),
    header('devicetype', 'devicetype field is required!').trim().notEmpty().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID, VALUES.DEVICE_TYPE.DESKTOP, VALUES.DEVICE_TYPE.WEB]),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
];

exports.appSignIn = [
    body("countryCode", "Please Provide countryCode").trim().notEmpty(),
    body("mobileNumber", "Please Provide mobileNumber").trim().notEmpty(),

    header("deviceaccesstype", "Please provide 'deviceaccesstype'")
        .trim()
        .notEmpty()
        .bail()
        .isIn(Object.values(VALUES.deviceAccessType))
        .withMessage(
            `'deviceaccesstype' must be ${Object.values(VALUES.deviceAccessType).join(" or ")}`
        ),

    header("devicetoken", "Please provide 'devicetoken'")
        .trim()
        .notEmpty(),

    header('devicetype', "Please provide 'devicetype'")
        .trim()
        .notEmpty()
        .bail()
        .isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID])
        .withMessage(
            `'devicetype' must be ${[VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID].join(" or ")}`
        ),
];

exports.appResendOtp = [
    header("deviceaccesstype", "please provide valid device type").isIn(Object.values(VALUES.deviceAccessType)),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header('devicetype', 'devicetype field is required!').trim().notEmpty().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID]),
    body("session", "Please provide valid session").isMongoId(),
]

exports.appUserRoleList = [
    header("deviceaccesstype", "please provide valid device type").isIn(Object.values(VALUES.deviceAccessType)),
    header('devicetype', 'devicetype field is required!').trim().notEmpty().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID]),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("countryCode", "Please Provide countryCode").trim().notEmpty(),
    body("mobileNumber", "Please Provide mobileNumber").trim().notEmpty(),
    // query("userId", "Please provide user id").trim().notEmpty(),
]

exports.verifyAppAuthOtp = [
    header("deviceaccesstype", "please provide valid device type").isIn(Object.values(VALUES.deviceAccessType)),
    header('devicetype', 'devicetype field is required!').trim().notEmpty().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID]),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    body("session", "Please provide valid session").trim().notEmpty(),
    body("otp", "Please provide otp").trim().notEmpty(),
    body("countryCode", "Please Provide countryCode").trim().notEmpty(),
    body("mobileNumber", "Please Provide mobileNumber").trim().notEmpty(),
    body("fcmToken", "Please provide fcmToken").optional().trim().notEmpty(),
    // body("username", "Please provide username").trim().notEmpty()
]

exports.verifyUserEmail= [
    body("confirmationCode", "Please provide confirmationCode").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
];

exports.verifyUserMobile= [
    body("confirmationCode", "Please provide confirmationCode").trim().notEmpty(),
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
];

exports.roleAccessed = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("refreshToken", "Please provide refresh token").trim().notEmpty(),
    header("deviceaccesstype", "please provide valid device type").optional().isIn(Object.values(VALUES.deviceAccessType)),
    header('devicetype', 'devicetype field is required!').optional().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID, VALUES.DEVICE_TYPE.WEB]),
    body("userRoleId", "Please provide user role id").trim().notEmpty(),
    body("fcmToken", "Please provide fcmToken").optional().trim().notEmpty(),
];

exports.logout = [
    ...headerValidator
];

exports.userRolesList = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    query("userId", "Please provide user id").trim().notEmpty(),
    header('deviceType', 'deviceType field is required!').trim().notEmpty().isIn([VALUES.DEVICE_TYPE.IOS, VALUES.DEVICE_TYPE.ANDROID, VALUES.DEVICE_TYPE.DESKTOP, VALUES.DEVICE_TYPE.WEB]),
];

exports.resendOTP = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    // header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("sendOtpTo", "Please provide send otp to").trim().isIn(Object.values(SIGNING_TYPE)),
]

exports.forgotPassword = [
    body("sendOtpTo", "Please provide send otp to").trim().isIn(Object.values(SIGNING_TYPE)),
    body("email", "Please Provide email").trim().custom((value, { req, path, location }) => {
        if(req.body.sendOtpTo === "EMAIL" && !req.body.email) {
            return false
        }
        return true;
    }).withMessage("Please provide email"),
    body("countryCode", "Please Provide countryCode").trim().custom((value, { req, path, location }) => {
        if(req.body.sendOtpTo === "MOBILE" && !req.body.countryCode) {
            return false
        }
        return true;
    }).withMessage("Please provide countryCode"),
    body("mobileNumber", "Please Provide mobileNumber").custom((value, { req, path, location }) => {
        if(req.body.sendOtpTo === "MOBILE" && !req.body.mobileNumber) {
            return false
        }
        return true;
    }).withMessage("Please provide valid mobileNumber"),
]

exports.verifyMobileForgotPasswordOTP = [
    body("mobileNumber", "Please provide mobile number").trim().notEmpty(),
    body("token", "Please provide a valid token").trim().notEmpty(),
]

exports.forgotPasswordVerify = [
    body("newPassword", "Please provide newPassword").trim().notEmpty(),
    body("confirmPassword", "Please provide newPassword").trim().notEmpty().custom((value, { req, path, location }) => {
        if(req.body.newPassword !== value) {
            return false;
        }
        return true;
    }),
    body("token", "Please provide a valid token").trim().notEmpty(),
    body("type", "Please provide type parameter").trim().notEmpty().isIn(Object.values(SIGNING_TYPE)),
    body("id", "Please provide id parameter").custom((value, { req, path, location }) => {
        if(req.body.type === SIGNING_TYPE.EMAIL && !value) {
            return false
        }
        return true
    }),
    body("mobileNumber", "Please provide mobile number").custom((value, { req, path, location }) => {
        if(req.body.type === SIGNING_TYPE.MOBILE && !value) {
            return false
        }
        return true
    }),
];

exports.checkRestPasswordLink = [
    body("token", "Please provide a valid token").trim().notEmpty(),
    body("id", "Please provide id parameter").trim().notEmpty(),
]

exports.testing = [
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
]


exports.getTokenConfigInfo = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
]

exports.updateTokenConfigInfo = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    body("timeUnit").trim().notEmpty().isIn(Object.values(TIME_UNITS)).withMessage(`Please provide value within ${Object.values(TIME_UNITS)}`),
    body("time", "Please provide time").toInt().custom((value, { req, path, location }) => {
        switch (req.body.timeUnit) {
            case TIME_UNITS.seconds:
                return value <= 86400

            case TIME_UNITS.minutes:
                return value <= 1440

            case TIME_UNITS.hours:
                return value <= 24

            case TIME_UNITS.days:
                return value === 1

        }
    }).withMessage("time should not exceed 1 day duration"),
];

exports.serviceAuthenticator = [
    header('devicetoken', 'deviceToken field is required!').trim().notEmpty(),
    header("authorization", "Please provide authorization").trim().notEmpty(),
    header("userroleid", "Please provide user role id").trim().notEmpty(),
    header("refreshToken", "Please provide authorization").trim().notEmpty(),
]