const { body } = require('express-validator');

const {
    headerValidator,
    tenantIdQueryValidator,
    tenantIdBodyValidator,
    perPageValidator,
    pageValidator,
    userRoleIdQueryValidator,
    timezoneBodyValidator,
} = require('./CommonValidator');

const {
    NOTIFICATION,
} = require("../../Configs/constants")

exports.createNotifications = [
    ...tenantIdBodyValidator,

    body("userRoleIds", "Please provide userRoleIds")
        .isArray({ min: 1 }),

    body("userRoleIds.*", "Please provide valid userRoleIds")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),

    body("title", "Please provide title")
        .trim()
        .notEmpty(),

    body("secondaryLanguageTitle", "Please provide secondaryLanguageTitle")
        .optional()
        .trim()
        .notEmpty(),

    body("message", "Please provide message")
        .trim()
        .notEmpty(),

    body("secondaryLanguageMessage", "Please provide secondaryLanguageMessage")
        .optional()
        .trim()
        .notEmpty(),

    body("centerMessage", "Please provide centerMessage")
        .trim()
        .notEmpty(),

    body("centerSecondaryLanguageMessage", "Please provide centerSecondaryLanguageMessage")
        .optional()
        .trim()
        .notEmpty(),

    body("smsBody", "Please provide smsBody")
        .optional()
        .trim()
        .notEmpty(),

    body("secondaryLanguageSmsBody", "Please provide secondaryLanguageSmsBody")
        .optional()
        .trim()
        .notEmpty(),

    body("type")
        .trim()
        .isIn(Object.values(NOTIFICATION.TYPE))
        .withMessage(
            `Please provide type within ${Object.values(NOTIFICATION.TYPE)}`
        ),

    body("threadId", "Please provide threadId")
        .trim()
        .notEmpty(),

    body("payloadData", "Please provide valid payloadData")
        .isObject(),

    body("smsRequired", "Please send smsRequired value either true or false.")
        .default(false)
        .isBoolean()
        .toBoolean(),

    body("sentPushNotification", "Please send sentPushNotification value either true or false.")
        .default(true)
        .isBoolean()
        .toBoolean()
]

exports.listNotifications = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...perPageValidator,
    ...pageValidator,
    ...userRoleIdQueryValidator,
];

exports.countUnreadNotifications = [
    ...headerValidator,
    ...tenantIdQueryValidator,
    ...userRoleIdQueryValidator,
];

const scheduleNotificationInfoValidator = [
    ...timezoneBodyValidator,

    body("title", "Please provide title")
        .trim()
        .notEmpty(),

    body("message", "Please provide message")
        .trim()
        .notEmpty(),

    body("imageName", "Please provide imageName")
        .optional()
        .trim()
        .notEmpty(),

    body("scheduleAt", "Please provide scheduleAt")
        .notEmpty()
        .bail()
        .isISO8601()
        .withMessage("Please provide valid scheduleAt")
]

const scheduleNotificationTypes = [
    NOTIFICATION.TYPE.DEALS_UPDATES
]

exports.scheduleNotification = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...scheduleNotificationInfoValidator,

    body("notificationType")
        .trim()
        .isIn(scheduleNotificationTypes)
        .withMessage(
            `Please provide notificationType within ${scheduleNotificationTypes}`
        ),

    body("priceListIds", "Please provide priceListIds")
        .isArray({ min: 1 }),

    body("priceListIds.*", "Please provide valid priceListIds")
        .trim()
        .notEmpty(),

    body("data", "Please provide valid data")
        .isObject(),
];

exports.updateScheduledNotification = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...scheduleNotificationInfoValidator,

    body("scheduledNotificationId", "Please provide scheduledNotificationId")
        .trim()
        .notEmpty(),
];
