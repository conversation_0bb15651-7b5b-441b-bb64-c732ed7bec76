const {
    body,
    query
} = require('express-validator');

const {
    headerValidator,
    tenantIdBodyValidator,
    isActiveValidator,
    tenantIdQueryValidator
} = require('./CommonValidator');

const {
    TEMPLATE_REASON_TYPE,
    ENTITY_STATUS
} = require('../../Configs/constants');

exports.addHoldReason = [
    ...headerValidator,
    ...tenantIdBodyValidator,
    ...isActiveValidator,

    body("title", "Please provide 'title'")
        .trim()
        .notEmpty(),

    body("secondaryLanguageTitle", "Please provide 'secondaryLanguageTitle'")
        .trim()
        .notEmpty(),

    body("isWhatsappMessageEnabled", "Please send 'isWhatsappMessageEnabled' value either true or false.")
        .isBoolean(),

    body("holdTemplates", "Please provide 'holdTemplates'")
        .optional()
        .isArray(),

    body("holdTemplates.*.templateId", "Please provide 'templateId' in 'holdTemplates'")
        .trim()
        .notEmpty(),

    body("holdTemplates.*.title", "Please provide 'title' in 'holdTemplates'")
        .trim()
        .notEmpty(),

    body("holdTemplates.*.language", "Please provide 'language' in 'holdTemplates'")
        .trim()
        .notEmpty(),

    body("holdTemplates.*.role", "Please provide 'role' in 'holdTemplates'")
        .trim()
        .notEmpty(),

    body("releaseTemplates", "Please provide 'releaseTemplates'")
        .optional()
        .isArray(),

    body("releaseTemplates.*.templateId", "Please provide 'templateId' in 'releaseTemplates'")
        .trim()
        .notEmpty(),

    body("releaseTemplates.*.title", "Please provide 'title' in 'releaseTemplates'")
        .trim()
        .notEmpty(),

    body("releaseTemplates.*.language", "Please provide 'language' in 'releaseTemplates'")
        .trim()
        .notEmpty(),

    body("releaseTemplates.*.role", "Please provide 'role' in 'releaseTemplates'")
        .trim()
        .notEmpty(),
];

exports.editHoldReason = [
    ...this.addHoldReason,

    body("id", "Please provide valid id")
        .trim()
        .notEmpty()
        .bail()
        .isMongoId(),
];

exports.getHoldReasons = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("status")
        .optional()
        .isIn(Object.values(ENTITY_STATUS))
        .withMessage(`Please provide status type as in ${Object.values(ENTITY_STATUS).join(", ")}`),

];

exports.getReasonTemplateWithDetails = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("holdReasonId")
        .trim()
        .notEmpty()
        .withMessage("Please provide 'holdReasonId'"),

    query("orderId")
        .trim()
        .notEmpty()
        .withMessage("Please provide 'orderId'"),

    query("type", "Please provide valid 'type'")
        .trim()
        .notEmpty()
        .bail()
        .isIn(Object.values(TEMPLATE_REASON_TYPE))
        .withMessage(`'type' must be ${Object.values(TEMPLATE_REASON_TYPE).join(" or ")}`),
];

exports.getTemplates = [
    ...this.getHoldReasons
];

exports.getTemplate = [
    ...headerValidator,
    ...tenantIdQueryValidator,

    query("templateId")
        .trim()
        .notEmpty()
        .withMessage("Please provide 'templateId'"),
];
