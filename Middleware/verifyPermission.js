const {
    PRIMITIVE_ROLES,
    PERMISSION_MODULES,
    DATA_SHEET,
    SETTINGS,
    ACTIONS,
    INTEGRATION_CHANNELS,
    VALUES
} = require("../Configs/constants");

const TenantPortalModal = new (require("../Models/tenantPortal"))();
const IntegrationCredentialModel = new (require("../Models/IntegrationCredentialModel"))();

const checkModulePermission = (
    permission,
    req
) => {
    let canAccess = false

    const userRoleDetails = req.headers.sessionDetails?.role_id;
    if (!userRoleDetails)
        return canAccess

    const moduleServicePermissions = req.headers.tenantDetail?.services.reduce((data, service) => {
        if (permission[service.key] !== undefined) {
            data[service.key] = service.permission
        }

        return data
    }, {}) ?? {}

    Object.keys(permission).forEach(module => {
        const actions = Array.isArray(permission[module]) ? permission[module] : [permission[module]]

        actions.forEach(action => {
            const tenantHasModuleAccess = moduleServicePermissions[module]?.[action] ?? true;
            const userHasModuleAccess = userRoleDetails.permission[module]?.[action] ?? false;

            if (tenantHasModuleAccess && userHasModuleAccess) {
                canAccess = true
            }
        })
    })

    return canAccess
}

exports.verifyPermission = (permission) => async (req, res, next) => {
    try {
        const canAccessModule = checkModulePermission(permission, req)
        if (!canAccessModule)
            return res.handler.forbidden('access_denied');

        return next();
    }
    catch (error) {
        return res.handler.serverError(error);
    }
};

exports.verifyDataSheetPermission = (action) => async (req, res, next) => {
    try {
        /*
        For use this middleware required,
        - dataType in body or query
        - action in function param / operationType in body or query
        */

        const userRoleDetails = req.headers.sessionDetails?.role_id;
        if (!userRoleDetails)
            return res.handler.forbidden('access_denied');

        const {
            dataType,
            operationType
        } = req.body ?? req.query

        const dataSheetPermissionModules = {
            [DATA_SHEET.DATA_TYPE.PRODUCT]: PERMISSION_MODULES.DATA_SHEET_PRODUCT,
            [DATA_SHEET.DATA_TYPE.PRICE]: PERMISSION_MODULES.DATA_SHEET_PRICE,
            [DATA_SHEET.DATA_TYPE.INVENTORY]: PERMISSION_MODULES.DATA_SHEET_INVENTORY,
            [DATA_SHEET.DATA_TYPE.CUSTOMER]: PERMISSION_MODULES.DATA_SHEET_CUSTOMER,
        }

        const module = dataSheetPermissionModules[dataType]
        let canAccess = false

        if (action) {
            canAccess = userRoleDetails.permission[module]?.[action] ?? false
        }
        else {
            const dataSheetPermissionActions = {
                [DATA_SHEET.OPERATION_TYPE.CREATE]: [ACTIONS.CREATE],
                [DATA_SHEET.OPERATION_TYPE.UPDATE]: [ACTIONS.EDIT],
                [DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS]: [
                    ACTIONS.CREATE,
                    ACTIONS.EDIT,
                ],
            }

            dataSheetPermissionActions[operationType].forEach(permissionAction => {
                const hasAccess = userRoleDetails.permission[module]?.[permissionAction] ?? false
                if (hasAccess) {
                    canAccess = true
                }
            })
        }

        if (canAccess) {
            return next();
        }
        else {
            return res.handler.forbidden('access_denied');
        }
    }
    catch (error) {
        return res.handler.serverError(error);
    }
};

exports.verifySettingConfigPermission = (action, otherPermission) => async (req, res, next) => {
    try {
        /*
        For use this middleware function type required in body or query
        */

        const userRoleDetails = req.headers.sessionDetails?.role_id;
        if (!userRoleDetails)
            return res.handler.forbidden('access_denied');

        let module
        switch (req.body.type ?? req.query.type) {
            case SETTINGS.CONFIGURATIONS.TYPE.APP_SETTING: {
                module = PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING
                break;
            }
            case SETTINGS.CONFIGURATIONS.TYPE.SHIPPING_LABEL: {
                module = PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_SHIPPING_LABEL
                break;
            }
            default: {
                module = PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_ACCOUNT_INFO
            }
        }

        const canAccess = userRoleDetails.permission[module]?.[action] ?? false;
        if (canAccess) {
            return next();
        }
        else {
            //Check for other permissions
            this.verifyPermission(otherPermission)(req, res, next)
        }
    }
    catch (error) {
        return res.handler.serverError(error);
    }
}

const checkSAPPermission = async (req, res) => {
    const tenant_id = req.headers.tenantDetail?._id ?? req.body.tenantId ?? req.query.tenantId
    if (!tenant_id) {
        res.handler.notFound("tenant_id_not_found")
        return false
    }

    const credentialsResponse = await IntegrationCredentialModel.getCredential(
        {
            tenant_id,
            name: INTEGRATION_CHANNELS.SAP_SERVICE
        },
        "configurations is_active",
        {
            lean: true
        }
    )

    if (!credentialsResponse) {
        res.handler.forbidden('sap_integration_not_found')
        return false
    }

    if (!credentialsResponse.is_active) {
        res.handler.notFound('sap_integration_not_active')
        return false
    }

    req.headers.sapIntegrationCredentials = credentialsResponse

    return true
}

exports.verifySAPAccess = (otherPermission) => async (req, res, next) => {
    try {
        //Check for other permissions
        const canAccessModule = checkModulePermission(otherPermission, req)
        if (!canAccessModule)
            return res.handler.forbidden('access_denied');

        const canAccessSAP = await checkSAPPermission(req, res) //Response sent in function it self
        if (!canAccessSAP)
            return

        if (req.headers.devicetype !== VALUES.DEVICE_TYPE.WEB) {
            const tenant_id = req.headers.tenantDetail?._id ?? req.body.tenantId ?? req.query.tenantId

            if (!(req.headers.sapIntegrationCredentials?.configurations?.have_mobile_access ?? false)) {
                res.handler.forbidden('access_denied');
                return false
            }

            const customerExternalId = req.body.customerExternalId ?? req.query.customerExternalId

            const customerDetails = await TenantPortalModal.findUserProfileWithFilter(
                {
                    external_id: customerExternalId,
                    tenant_id
                },
                {
                    tenant_id: 1,
                    is_payment_enabled: 1,
                }
            );

            if (!(customerDetails?.is_payment_enabled ?? false)) {
                res.handler.forbidden('access_denied');
                return false
            }
        }

        return next();
    }
    catch (error) {
        return res.handler.serverError(error);
    }
}

exports.verifyRewardProgramAccess = (otherPermission) => async (req, res, next) => {
    try {
        //Check for other permissions
        const canAccessModule = checkModulePermission(otherPermission, req)
        if (!canAccessModule)
            return res.handler.forbidden('access_denied');

        const canAccessSAP = await checkSAPPermission(req, res) //Response sent in function it self
        if (!canAccessSAP)
            return

        if (!(req.headers.sapIntegrationCredentials?.configurations?.is_reward_program_enabled ?? false))
            return res.handler.forbidden('reward_program_disabled_for_tenant');

        return next();
    }
    catch (error) {
        return res.handler.serverError(error);
    }
}
