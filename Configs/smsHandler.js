const { ERROR_MESSAGES } = require("./constants");

class SMSHandler {

    constructor() {
        this.appSId = process.env.UNIFONIC_APPS_ID;
        this.authToken = process.env.TWILIO_AUTH_TOKEN;
        this.baseUrl = process.env.UNIFONIC_BASE_URL;
        this.senderId = process.env.UNIFONIC_SENDER_ID;
        this.authorization = Buffer.from(`${process.env.UNIFONIC_USER_ID}:${process.env.UNIFONIC_USER_PASSWORD}`)
    }

    async sendSMS(recipientNumber, body) {
        logger.info(`SendSMS for ${recipientNumber}. Body: ${body}`)

        const unifonicRes = await fetch(
            `https://${this.baseUrl}rest/SMS/messages?AppSid=${this.appSId}&SenderID=${this.senderId}&Body=${body}&Recipient=${recipientNumber}`,
            {
                method: "POST",
                headers: {
                    Authorization: `Basic ${this.authorization}`
                }
            }
        );
        const res = await unifonicRes.json();

        if (!res?.success || !unifonicRes?.ok) {
            logger.error("SMS_SEND_ERROR ===> file: smsHandler.js:26 ~ SMSHandler ~ sendSMS ~", {
                recipientNumber,
                errorResponse: res,
            })
            throw new Error(ERROR_MESSAGES.SMS_SEND)
        }
    }
}

module.exports = SMSHandler
