
const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const stream = require("stream")

const { MEDIA_TYPE, BUCKET_TYPE } = require('./../Configs/constants');

//CREATE FILE NAME
function getFileName(file) {
    return file.originalname.split('.')[0].replace(/[^A-Z0-9]/ig, "_") + '_' + Date.now() + '_' + Math.floor(Math.random() * 999) + 99 + path.extname(file.originalname)
}

//MULTER CONFIG
var storage = multer.diskStorage({
    destination: function (req, file, cb) {
        //Check Temp folder is present...
        let filePath = "./../Assets/Images/Temp/Original";
        let fileTempPath = "./../Assets/Images/Temp/Thumb";
        let tempPath = path.join(__dirname, filePath)
        let tempThumbPath = path.join(__dirname, fileTempPath)
        if (!fs.existsSync(tempPath) || !fs.existsSync(tempThumbPath)) {
            // Do something
            fs.mkdirSync(tempPath)
            fs.mkdirSync(tempThumbPath)
            cb(null, tempPath)
        } else
            cb(null, tempPath)

    },
    filename: function (req, file, cb) {
        let fileName = getFileName(file)
        if (req.body[file.fieldname] === undefined) {
            req.body[file.fieldname] = []
            req.body[file.fieldname].push(fileName)
        } else
            req.body[file.fieldname].push(fileName)
        cb(null, fileName)
    }
})

//FILE FILTER
//CHANGE MIME TYPE ACCORDING TO YOUR NEEDS
const fileMimeTypes = [
    'image/jpeg',
    'image/png',
    'text/csv'
]
const imagesExt = ["jpg", "jpeg", "png"]
const videosExt = ["mp4", "avi"]

const fileFilter = (req, file, cb) => {

    if (fileMimeTypes.indexOf(`${file.mimetype}` !== -1)) {
        cb(null, true)
    } else {
        cb(new Error('File is not supported!'))
    }
}

var upload = multer({
    storage: storage,
    // fileFilter: fileFilter,
    limits: {
        fileSize: (process.env.FILE_LIMITS || 5) * 1024 * 1024 // WE ARE ALLOWING ONLY 50 MB FILES
    },
})

var self
//GET AWS CONFIG
//Note: First of all, you have to create a bucket on AWS with proper permissions.
const credentials = {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY,
    Bucket: process.env.AWS_BUCKET_NAME,
    region: process.env.AWS_REGION,
    signatureVersion: 'v4'
}

//S3 CONFIG
let s3Config;
class S3Upload {

    constructor(bucketType = BUCKET_TYPE.PRIVATE) {
        self = this;
        if (bucketType === BUCKET_TYPE.PUBLIC) {
            credentials["Bucket"] = process.env.AWS_PUBLIC_BUCKET_NAME;
        } else if (bucketType === BUCKET_TYPE.LOCALES) {
            credentials["Bucket"] = process.env.AWS_LOCALES_BUCKET_NAME;
        }
        s3Config = new AWS.S3(credentials)
    }

    //UPLOAD FILES
    uploadFiles(pathName, fileNames, localFilePath, metadata) {
        try {
            //Create params and promise Array
            let promiseArray = [];

            if (Array.isArray(fileNames)) {
                fileNames.forEach(async element => {

                    promiseArray.push(self.promisifyFile(element, pathName, localFilePath, metadata))
                    // let ext = element.split(".")
                    // if (imagesExt.indexOf(ext[ext.length - 1]) >= 0) {
                    // self.thumbFile(element, pathName, MEDIA_TYPE.IMAGE);
                    // }
                    // else if (videosExt.indexOf(ext[ext.length - 1]) >= 0) {
                    // let image = await self.thumbFile(element, pathName, MEDIA_TYPE.VIDEO)
                    // promiseArray.push(self.promisifyFile(element, pathName, localFilePath))
                    // }
                });
            } else {
                promiseArray.push(self.promisifyFile(fileNames, pathName, localFilePath, metadata))
                // let ext = fileNames.split(".")
                // if (imagesExt.indexOf(ext[ext.length - 1]) >= 0) {
                //     self.thumbFile(fileNames, pathName, MEDIA_TYPE.IMAGE);
                // }
            }


            //Upload to S3
            return Promise.all(promiseArray).then(result => {
                return {
                    status: STATUS_CODES.SUCCESS,
                    data: result
                }
            }).catch(err => {
                return {
                    status: STATUS_CODES.SERVER_ERROR,
                    error: err
                }
            })

        } catch (error) {
            logger.error(error)
        }
    }

    promisifyFile(fileName, pathName, localFilePath, metadata = {}) {
        return new Promise((resolve, reject) => {
            let directoryPath = './../Assets/Images/Temp/Original/'
            const getFileFromLocal = localFilePath || path.join(__dirname, directoryPath + fileName)
            const filePathName = pathName

            const params = {
                Bucket: s3Config.config.Bucket,
                Body: fs.createReadStream(getFileFromLocal),
                Key: (filePathName === '/' || filePathName === '') ? fileName : filePathName + '/' + fileName,
                ContentType: path.extname(getFileFromLocal),
            };

            if (metadata["CacheControl"] !== undefined) {
                params["CacheControl"] = metadata["CacheControl"]
            }

            s3Config.upload(params, async function (err, data) {
                if (err) {
                    fs.unlinkSync(getFileFromLocal);
                    reject(err)
                    return;
                }

                //success
                if (data) {
                    //Remove file from local
                    let ratio = 0;
                    let ext = fileName.split("."), mediaType = '';
                    if (imagesExt.indexOf(ext[ext.length - 1]) >= 0)
                        mediaType = MEDIA_TYPE.IMAGE;
                    else if (videosExt.indexOf(ext[ext.length - 1]) >= 0) {
                        mediaType = MEDIA_TYPE.VIDEO;
                        // let image = await self.thumbFile(fileName, pathName, MEDIA_TYPE.VIDEO)
                        // ratio = image.ratio
                    }
                    fs.unlinkSync(getFileFromLocal)

                    resolve({
                        fileName: fileName,
                        location: data.Location,
                        mediaType, ratio
                    })
                }
            })
        })
    }

    // async thumbFile(fileName, pathName, type = MEDIA_TYPE.IMAGE) {
    //     return new Promise(async (resolve, reject) => {
    //         const getFileFromLocal = path.join(__dirname, './../Assets/Images/Temp/Original/' + fileName);
    //         const folderName = path.join(__dirname, './../Assets/Images/Temp/Thumb/');
    //         let thumbFile = path.join(__dirname, './../Assets/Images/Temp/Thumb/' + fileName);

    //         // FOR TYPE VIDEO
    //         if (type === MEDIA_TYPE.VIDEO) {
    //             fileName = fileName.split('.');
    //             fileName = fileName[0] + '.png';
    //             thumbFile = path.join(__dirname, './../Assets/Images/Temp/Thumb/' + fileName)

    //             // TAKE SCREENSHOT FROM FFMPEG
    //             await ffmpeg(getFileFromLocal)
    //                 .screenshots({
    //                     count: 1,
    //                     filename: fileName,
    //                     folder: folderName
    //                 }).on('end', async function () {
    //                     const filePathName = pathName + '/thumb'

    //                     let imageData = sharp(thumbFile);
    //                     let ratio;

    //                     imageData.metadata()
    //                         .then(data => {
    //                             function gcd(a, b) {
    //                                 return (b == 0) ? a : gcd(b, a % b);
    //                             }

    //                             var r = gcd(data.width, data.height);

    //                             ratio = `${data.width / r}` + ":" + `${data.height / r}`
    //                         }
    //                         )
    //                         .catch(err =>
    //                             logger.error(err)
    //                         );
    //                     let thumb = await self.generateThumb(thumbFile, filePathName, fileName);
    //                     thumb.ratio = ratio;
    //                     resolve(thumb);
    //                 })
    //             return;
    //         }

    //         // CREATIING THUMB IMAGE

    //         await sharp(getFileFromLocal)
    //             .rotate()
    //             .resize({
    //                 width: IMAGE_THUMB_SIZE.WIDTH,
    //                 height: IMAGE_THUMB_SIZE.HEIGHT,
    //                 fit: 'fill'
    //             })
    //             .toFile(thumbFile, function (err, info) {
    //                 if (err) {
    //                     reject(err)
    //                 }

    //                 const filePathName = pathName + '/thumb'
    //                 let thumb = self.generateThumb(thumbFile, filePathName, fileName);
    //                 resolve(thumb);
    //             });
    //     })
    // }

    /*
     * GENERATE THUMB
     */
    // generateThumb(thumbFile, filePathName, fileName) {
    //     return new Promise(async (resolve, reject) => {
    //         const params = {
    //             Bucket: process.env.AWS_BUCKET_NAME,
    //             Body: fs.createReadStream(thumbFile),
    //             Key: (filePathName === '/' || filePathName === '') ? fileName : filePathName + '/' + fileName,
    //             ContentType: path.extname(thumbFile),
    //         }

    //         // UPLOADING THUMB IMAGE
    //         s3Config.upload(params, function (err, data) {
    //             if (err) {
    //                 reject(err)
    //             }

    //             //success
    //             if (data) {
    //                 //Remove file from local
    //                 fs.unlinkSync(thumbFile)
    //                 resolve({
    //                     fileName: fileName,
    //                     location: data.Location
    //                 })
    //             }
    //         });
    //     });
    // }

    //GET SIGNED URL
    async getSignedUrl(pathName, fileName) {
        const url = s3Config.getSignedUrl('getObject', {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName,
            Expires: process.env.AWS_SIGNED_URL_EXPIRE_TIME * 60 || 60 // time in seconds: e.g. 60 * 5 = 5 mins
            // AWS_SIGNED_URL_EXPIRE_TIME = 60, Expiry time will be 1 hour
        })
        return url
    }

    //GET SIGNED URL FOR PUT OBJECT
    async getPutObject(pathName, fileName) {
        const Key =
            ["", "/"].includes(pathName)
                ? fileName
                : pathName + '/' + fileName
        const Expires = process.env.AWS_SIGNED_UPLOAD_URL_EXPIRE_TIME * 60 || 60

        const s3Params = {
            Bucket: s3Config.config.Bucket,
            Key,
            Expires,  // time in seconds: e.g. 5 * 60 = 5 mins
        }
        const signedUrl = await s3Config.getSignedUrl('putObject', s3Params)
        return signedUrl
    }

    //GET MULTIPLE SIGNED URLS
    getMultipleSignedUrls(pathName, fileNames) {
        let promiseArray = []
        fileNames.forEach(element => {
            let createPromise = new Promise((resolve, reject) => {
                let url = s3Config.getSignedUrl('getObject', {
                    Bucket: process.env.AWS_BUCKET_NAME,
                    Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + element,
                    Expires: process.env.AWS_SIGNED_URL_EXPIRE_TIME * 60 || 60 // time in seconds: e.g. 60 * 5 = 5 mins
                    // AWS_SIGNED_URL_EXPIRE_TIME = 60, Expiry time will be 1 hour
                })
                if (url)
                    resolve(url)
                else
                    reject()
            })
            promiseArray.push(createPromise)
        })

        return Promise.all(promiseArray).then(result => {
            return {
                status: STATUS_CODES.SUCCESS,
                data: result
            }
        }).catch(err => {
            return {
                status: STATUS_CODES.SERVER_ERROR,
                error: err
            }
        })

    }

    //DELETE SINGLE FILE
    deleteFile(pathName, fileName) {

        let params = {
            Bucket: s3Config.config.Bucket,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName,
        }
        return new Promise((resolve, reject) => {

            s3Config.deleteObject(params, function (err, data) {
                if (err) {
                    reject({
                        status: STATUS_CODES.SERVER_ERROR,
                        error: err
                    })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })


    }

    //DELETE MULTIPLE FILES
    deleteFiles(pathName, fileNames) {
        const isArray = Array.isArray(pathName)
        //MAP FILES ARRAY
        let mappedArray = fileNames.map((fileName, index) => {
            if (isArray) {
                return {
                    Key: (pathName[index] === '/' || pathName[index] === '') ? fileName : pathName[index] + '/' + fileName
                }
            } else {
                return {
                    Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName
                }
            }
        })

        let params = {
            Bucket: s3Config.config.Bucket,
            Delete: { // required
                Objects: mappedArray
            }
        }

        return new Promise((resolve, reject) => {

            s3Config.deleteObjects(params, function (err, data) {
                if (err) {
                    reject({
                        status: STATUS_CODES.SERVER_ERROR,
                        error: err
                    })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })
    }

    //GET SIGNED URL WITH DEFINED TIME
    getSignedUrlWithExpiryTime(pathName, fileName, expiryTime) {
        const url = s3Config.getSignedUrl('getObject', {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + element,
            Expires: expiryTime * 60 || 60 // time in seconds: e.g. 60 * 5 = 5 mins
        })
        return url
    }

    //GET PUBLIC URL
    getPublicUrl(pathName, fileName) {
        let params = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName,
        }

        return new Promise((resolve, reject) => {
            s3Config.getObject(params, function (err, data) {
                if (err) {
                    if (err.code === 'NoSuchKey')
                        resolve({
                            status: STATUS_CODES.NOT_FOUND,
                            data: err
                        })
                    else
                        reject({
                            status: STATUS_CODES.SERVER_ERROR,
                            error: err
                        })
                }

                //Success
                if (data) {
                    let location = `https://${process.env.AWS_BUCKET_NAME}.s3.amazonaws.com/${params.Key}`
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: {
                            ...data,
                            location: location
                        }
                    })
                }
            });
        })
    }

    getReadStreamOfObject(Key) {
        return s3Config
            .getObject({
                Bucket: s3Config.config.Bucket,
                Key,
            }).createReadStream()
    }

    getObject(pathName, fileName) {
        let params = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: (pathName === '/' || pathName === '') ? fileName : pathName + '/' + fileName,
        }

        return new Promise((resolve, reject) => {
            s3Config.getObject(params, function (err, data) {
                if (err) {
                    reject(err)
                }

                //Success
                if (data) {
                    resolve(data)
                }
            });
        })
    }

    writeStreamToS3(Key) {
        const pass = new stream.PassThrough()

        return {
            writeStream: pass,
            uploadFinished: s3Config
                .upload({
                    Key,
                    Body: pass,
                    Bucket: s3Config.config.Bucket
                }).promise()
        }
    }

    copyImage(copySource, copyTo) {
        let params = {
            CopySource: encodeURIComponent(s3Config.config.Bucket + '/' + copySource),
            Bucket: s3Config.config.Bucket,
            Key: copyTo
        }

        return new Promise((resolve, reject) => {
            s3Config.copyObject(params, function (err, data) {
                if (err) {
                    if (err.code === 'NoSuchKey')
                        reject({
                            status: STATUS_CODES.NOT_FOUND,
                            data: err.message
                        })
                    else
                        reject({
                            status: STATUS_CODES.SERVER_ERROR,
                            error: err.message
                        })
                }

                //Success
                if (data) {
                    resolve({
                        status: STATUS_CODES.SUCCESS,
                        data: data
                    })
                }
            });
        })
    }

    listAllObjectsFromS3Bucket = async (prefix) => {
        let isTruncated = true
        let marker
        const elements = []

        while (isTruncated) {
            const params = {
                Bucket: s3Config.config.Bucket,
            }

            if (prefix) {
                params.Prefix = prefix
            }

            if (marker) {
                params.Marker = marker
            }

            try {
                const response = await s3Config.listObjects(params).promise()

                response.Contents.forEach(item => {
                    const {
                        Key: key,
                        Size: size,
                    } = item

                    if (key && size) {
                        elements.push({
                            "s3": {
                                "object": {
                                    key,
                                    size,
                                }
                            }
                        })
                    }
                })
                isTruncated = response.IsTruncated

                if (isTruncated) {
                    marker = response.Contents.slice(-1)[0].Key
                }
            }
            catch (error) {
                throw error
            }
        }
        return elements
    }

}

module.exports = { S3Upload, upload }