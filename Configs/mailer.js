const sgMail = require("@sendgrid/mail");

sgMail.setApiKey(process.env.SEND_GRID_API_KEY);

exports.sendEmail = async (
    receiver,
    subject,
    data,
    cc = [],
    from = "<EMAIL>",
) => {
    try {
        await sgMail.send({
            to: receiver,
            from: from, // for otp verification and <NAME_EMAIL> & for order notification, <NAME_EMAIL>
            subject,
            cc,
            html: data["html"]
        });
    }
    catch (error) {
        logger.error(error, {
            errorMessage: "sendEmail ~ error",
            errors: error.response?.body?.errors
        })
    }
}
