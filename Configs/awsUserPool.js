const { VALUES } = require('./constants')
const AmazonCognitoIdentity = require('amazon-cognito-identity-js');
const AWS = require('aws-sdk');

const CognitoUserPool = AmazonCognitoIdentity.CognitoUserPool;


const userPool = new CognitoUserPool(VALUES.userPoolData);

AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY,
    region: process.env.AWS_REGION
});

const AWSAdminCognito = new AWS.CognitoIdentityServiceProvider();
// AWSAdminCognito.adminSetUserPassword

module.exports = { userPool, AWSAdminCognito };