const admin = require("firebase-admin")
const FileUpload = require("./awsUploader").S3Upload
const fileUpload = new FileUpload()

const { FILE_PATH } = require("./constants")
const { retry } = require("../Utils/retryHelper")

let isInitialized = false

const initializeFirebase = async () => {
    if (isInitialized) return admin

    try {
        await retry(
            async () => {
                const data = await fileUpload.getObject("", FILE_PATH.FIREBASE_SERVICE_ACCOUNT)
                const serviceAccount = JSON.parse(data.Body.toString("utf-8"))

                admin.initializeApp({
                    credential: admin.credential.cert(serviceAccount)
                })

                isInitialized = true
                logger.info("✅ Firebase initialized successfully :)")
            },
            { fnName: "initializeFirebase" }
        )

        return admin
    }
    catch (error) {
        logger.error("❌ Firebase initialization failed after all retries.", error)
    }
}

const getFirebaseAdmin = () => {
    if (!isInitialized) {
        logger.error("❌ Firebase not initialized yet. Call initializeFirebase() first.")
        return null
    }
    return admin
}

module.exports = {
    initializeFirebase,
    getFirebaseAdmin,
}
