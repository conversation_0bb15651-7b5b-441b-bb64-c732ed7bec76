const axios = require('axios');

const {
    MESSAGE_BIRD,
    STATUS_CODES,
    MESSAGE_BIRD_API_DYNAMIC_ID,
} = require('./constants');

class MessageBird {

    errorHandler = (error = {}) => {
        const errorCode = error.response?.status || STATUS_CODES.SERVER_ERROR
        const errorMessage = error.response?.data?.message || ''

        return {
            status: errorCode,
            message: errorMessage,
            data: {}
        };
    }

    replaceUrl = (url, projectId, templateId, configurations = {}) => {
        if (
            url.includes(MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID)
        ) {
            url = url.replace(
                MESSAGE_BIRD_API_DYNAMIC_ID.WORKSPACE_ID,
                configurations.workspace_id
            )
        }

        if (
            url.includes(MESSAGE_BIRD_API_DYNAMIC_ID.CHANNEL_ID)
        ) {
            url = url.replace(
                MESSAGE_BIRD_API_DYNAMIC_ID.CHANNEL_ID,
                configurations.channel_id
            )
        }

        if (
            url.includes(MESSAGE_BIRD_API_DYNAMIC_ID.PROJECT_ID)
        ) {
            url = url.replace(
                MESSAGE_BIRD_API_DYNAMIC_ID.PROJECT_ID,
                projectId
            );
        }

        if (
            url.includes(MESSAGE_BIRD_API_DYNAMIC_ID.TEMPLATE_ID)
        ) {
            url = url.replace(
                MESSAGE_BIRD_API_DYNAMIC_ID.TEMPLATE_ID,
                templateId
            )
        }

        return url;
    }

    sendMessageToReceiver = async (payload = {}, configurations = {}) => {
        try {
            let url = configurations.base_url +
                this.replaceUrl(
                    MESSAGE_BIRD.API_END_POINT.SEND_MESSAGE,
                    null,
                    null,
                    configurations
                );

            const response = await axios.post(
                url,
                payload,
                {
                    headers: {
                        Authorization: `AccessKey ${configurations.access_key}`
                    }
                }
            );

            return {
                status: response.status || STATUS_CODES.SUCCESS,
                data: response.data || {},
                message: ""
            };
        }
        catch (error) {
            return this.errorHandler(error);
        }
    }

    getTemplateDetails = async (projectId = '', templateId = '', configurations = {}) => {
        try {
            const url = configurations.base_url +
                this.replaceUrl(
                    MESSAGE_BIRD.API_END_POINT.PROJECT_TEMPLATE_DETAILS,
                    projectId,
                    templateId,
                    configurations
                )

            const projectTemplateResponse = await axios.get(
                url,
                {
                    headers: {
                        Authorization: `AccessKey ${configurations.access_key}`
                    }
                }
            );

            const projectTemplate = projectTemplateResponse.data || {};

            return {
                status: projectTemplateResponse.status || STATUS_CODES.SUCCESS,
                data: projectTemplate,
                message: ""
            };
        }
        catch (error) {
            return this.errorHandler(error);
        }
    }

    getTemplates = async (configurations = {}) => {
        try {
            const url = configurations.base_url +
                this.replaceUrl(
                    MESSAGE_BIRD.API_END_POINT.PROJECTS,
                    null,
                    null,
                    configurations
                );

            let templates = [],
                pageToken = "INITIAL_CALL"

            while (pageToken) {
                const projectsResponse = await axios.get(
                    url,
                    {
                        headers: {
                            Authorization: `AccessKey ${configurations.access_key}`
                        },
                        params: {
                            isActive: true,
                            limit: MESSAGE_BIRD.PAGINATION_LIMIT,
                            pageToken: pageToken === "INITIAL_CALL" ? undefined : pageToken,
                        }
                    }
                );

                const projects = projectsResponse.data.results || [];
                pageToken = projectsResponse.data.nextPageToken || undefined;

                if (projects?.length) {
                    templates = [...templates, ...projects];
                }
            }

            // let projectTemplates = [];

            // if (templates.length) {
            //     for (let index = 0; index < templates.length; index++) {
            //         const project = templates[index];

            //         if (project.activeResourceId) {
            //             const projectTemplateResponse = await this.getTemplateDetails(
            //                 project.id,
            //                 project.activeResourceId,
            //                 configurations,
            //             )

            //             if (projectTemplateResponse.status === STATUS_CODES.SUCCESS &&
            //                 projectTemplateResponse.data &&
            //                 Object.values(projectTemplateResponse.data)?.length
            //             ) {
            //                 projectTemplates.push(projectTemplateResponse.data)
            //             }
            //         }
            //     }
            // }

            return {
                status: STATUS_CODES.SUCCESS,
                data: templates,
                message: ""
            };

        }
        catch (error) {
            return this.errorHandler(error);
        }
    };

    getProjectDetail = async (projectId, configurations = {}) => {
        try {
            const url = configurations.base_url +
                this.replaceUrl
                    (
                        MESSAGE_BIRD.API_END_POINT.PROJECTS_BY_ID,
                        projectId,
                        null,
                        configurations
                    )

            const projectsResponse = await axios.get(
                url,
                {
                    headers: {
                        Authorization: `AccessKey ${configurations.access_key}`
                    }
                }
            );

            const project = projectsResponse.data || {};
            let projectTemplateResponse = {
                status: "",
                data: {},
                message: ""
            };

            if (project?.activeResourceId) {
                projectTemplateResponse = await this.getTemplateDetails(
                    project.id,
                    project.activeResourceId,
                    configurations,
                )
            }

            return {
                status: projectTemplateResponse.status || STATUS_CODES.SUCCESS,
                data: projectTemplateResponse.data || {},
                message: projectTemplateResponse.message
            };
        }
        catch (error) {
            return this.errorHandler(error);
        }
    };
}

module.exports = MessageBird;
