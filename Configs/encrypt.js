const randomstring = require("randomstring");
const bcrypt = require('bcryptjs');
const crypto = require("crypto");

const { STATUS_CODES } = require("./constants");

const algorithm = process.env.ENCRYPTION_ALGORITHM;
const key = process.env.ENCRYPTION_KEY;
const saltRounds = parseInt(process.env.ENCRYPTION_SALT_ROUNDS);

class EncryptionHandler {

    encrypt = async (text) => {
        try {
            const iv = crypto.randomBytes(12) // Generate a 96-bit IV for GCM mode
            const bufferKey = Buffer.from(key, "base64") // Convert key back to Buffer
            const cipher = crypto.createCipheriv(algorithm, bufferKey, iv)

            const encrypted =
                cipher.update(text, 'utf8', 'base64') +
                cipher.final('base64')

            const tag = cipher.getAuthTag()

            // Prepend the IV and tag to the encrypted text
            const encryptedText = iv.toString('base64') + ':' + encrypted + ':' + tag.toString('base64')

            return {
                status: STATUS_CODES.SUCCESS,
                data: encryptedText
            }
        }
        catch (error) {
            return {
                status: STATUS_CODES.SERVER_ERROR,
                error: error
            }
        }
    }

    decrypt(encryptedText) {
        try {
            const [ivBase64, encryptedData, tagBase64] = encryptedText.split(':', 3)
            const iv = Buffer.from(ivBase64, 'base64') // Convert IV back to Buffer
            const tag = Buffer.from(tagBase64, 'base64') // Convert tag back to Buffer
            const bufferKey = Buffer.from(key, "base64") // Convert key back to Buffer

            const decipher = crypto.createDecipheriv(algorithm, bufferKey, iv)
            decipher.setAuthTag(tag)

            const decryptedText =
                decipher.update(encryptedData, 'base64', 'utf8') +
                decipher.final('utf8')

            return {
                status: STATUS_CODES.SUCCESS,
                data: decryptedText
            }
        }
        catch (error) {
            return {
                status: STATUS_CODES.SERVER_ERROR,
                error: error
            }
        }
    }

    generateAuthToken() {
        var tokenString = randomstring.generate(25);
        var token = bcrypt.hashSync(tokenString, bcrypt.genSaltSync(saltRounds), null);
        return token;
    }

    generateRandomString(length = 9) {
        return randomstring.generate(length);
    }

    bcrypt(data) {
        return bcrypt.hashSync(data.toString(), bcrypt.genSaltSync(saltRounds), null);
    }

    compareBcrypt(entity, encryptEntity) {
        return bcrypt.compareSync(entity, encryptEntity);
    }
}

module.exports = EncryptionHandler