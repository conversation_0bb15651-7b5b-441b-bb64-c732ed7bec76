USERS (TABLE)
    ROLE_ID

MODULES_ACCESS (TABLE) {PORTAL}
    MODULE_NAME
    
FUNCTIONS (TABLE) {SCREEN}
    MODULE_ACCESS_ID
    NAME
    
OPERATIONS (TABLE) {CRUD OPERATION}
    FUNCTION_ID (FOR <PERSON>HICH SCREEN)
    NAME=VIEW, DELETE, UPDATE, CREATE
    
ROLE_OPERATION_MAPPING
    OPERATION_ID
    ROLE_ID

ROLES
    ROLE_NAME
    MODULE_ACCESS_ID








USERS 
    USER_ID = 1
    NAME = SHRIDUTT
    ROLE_ID = 1

MODULE_ACCESS
    MODULE_ACCESS_ID = 10 , NAME = SYSTEM,
    MODULE_ACCESS_ID = 11, NAME = TENANT

FUNCTIONS
    FUNCTION_ID = 1, NAME = DASHBOARD, MODULE_ACCESS_ID = 10 (SYSTEM)
    FUNCTION_ID = 2, NAME = PAYMENT, MODULE_ACCESS_ID = 10
    FUNCTION_ID = 3, NAME = DASHBOARD, MODULE_ACCESS_ID = 11 (TENANT)

OPERATIONS
    OPERATION_ID = 1, NAME=VIEW, FUNCTION_ID = 1,
    OPERATION_ID = 2, NAME=DELETE, FUNCTION_ID = 1
    OPERATION_ID = 3, NAME=UPDATE, FUNCTION_ID = 1
    OPERATION_ID = 4, NAME=VIEW, FUNCTION_ID = 3,
    OPERATION_ID = 5, NAME=DELETE, FUNCTION_ID = 3,
    OPERATION_ID = 6, NAME=UPDATE, FUNCTION_ID = 3,
    
    
ROLE_OPERATION_MAPPING (with the use of join there will be always unique FUNCTION_ID AND MODULE_ACCESS_ID available)
    OPERATION_ID = 1, ROLE_ID = 1
    OPERATION_ID = 2, ROLE_ID = 1
    OPERATION_ID = 3, ROLE_ID = 2,
    OPERATION_ID = 5, ROLE_ID = 3,
    OPERATION_ID = 6, ROLE_ID = 3,

ROLES
    ROLE_ID = 1, ROLE_NAME = ADMIN, MODULE_ACCESS_ID = 10,
    ROLE_ID = 2, ROLE_NAME = SALES, MODULE_ACCESS_ID = 10,

    ROLE_ID = 3, ROLE_NAME = CUSTOM_1, MODULE_ACCESS_ID = 11,

======================================================================================



user_schema { // most of the details will be stored in amazon-cognito
    _id: ObjectId ,       [not null, PK]
    first_name: String,    [not null]
    last_name: String,     [not null]
    mobile_number: Number, [not null]   ===> one number can be linked to different user profiles  ===>  unique constraint ( with country code),
    password: String      [not null]
    email: String,      [not null]  ==> unique constraint
    last_accessed_profiles: Array[]   ==> last 
    ....
}

user_profiles {
    _id ObjectID [not null, PK]  ==> one profile can be linked with multiple tenants or multiple branch managers of branch
    role_id ObjectID,    [not null]
    user_id ObjectID,    [not null],
    tenant_id ObjectId,
    branch_id ObjectId, 
}


role_schema {
    _id: ObjectID,          [not null, PK]
    name: String,           [not null]
    description: String     [not null]
    portal_type: String      [not null]  ===> ANY ON OF [ SYSTEM_PORTAL, TENANT_PORTAL ]
    is_custom: Boolean     [not null]  ===> IF THE ROLE IS A CUSTOM ROLE, IT WILL BE TRUE
    is_editable: Boolean    [not null] ===>  (if true, their permissions will be editable)
    is_delete_able: Boolean  [not null] ==> if true this role type can be deleteAble (what should happen if the role gets deleted, anf the deleted role was assigned to some user's profile)
    
    1: blow property is not editable if the role is not custom
    2: for non-custom role we need to set them according to (the permissions from the https://docs.google.com/document/d/1PCmeBIG0m4g6grkcrPS7ab1kCQABJ7Kr-Hki7Pj9CsM/edit#)
    3: below is for Warehouse Permissions from the figma ( which should be custom role)
    permissions: {
        dashboard: { view: Boolean, edit: Boolean, create: Boolean, delete: Boolean },
        orders: { view: Boolean, edit: Boolean, create: Boolean, delete: Boolean },
        catalog: { view: Boolean, edit: Boolean, create: Boolean, delete: Boolean },
        customer: { view: Boolean, edit: Boolean, create: Boolean, delete: Boolean },
        import: { view: Boolean, edit: Boolean, create: Boolean, delete: Boolean },
        marketing: { view: Boolean, edit: Boolean, create: Boolean, delete: Boolean },
        ....
    }
}

portal_screens {
    _id: ObjectID,          [not null, PK],
    screen_name: String      [not null],  ==> screens of the particular dashboard
    portal_type: String      [not null]  ===> ANY ON OF [ SYSTEM_PORTAL, TENANT_PORTAL, BRANCH_PORTAL ]
}


// ongoing
tenants {
    ...tenant demographic data (name, legal and etc)
}

hawak_branches {
    ... branch demographic data
    hawak_tenant_id ObjectId ==> this may change (I can embed this collection in tenants document it self)
}