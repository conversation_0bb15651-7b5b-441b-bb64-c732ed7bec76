<!DOCTYPE html>
<html lang="en" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]-->
    <!--[if !mso]><!-->
    <title>New Order Received</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

        <% for(var i = 0; i< mediaQuery.length; i++) {%>
            <% if(mediaQuery[i] < 380) { %>
                <% boxWidth = boxWidth - 3 %>
            <% } else { %>
                <% boxWidth = boxWidth - 2 %>
            <% } %>
            @media (max-width: <%= mediaQuery[i] %>px) {
                .progress-bar{
                    width: <%= boxWidth %>px !important;
                    margin-left: <%= (boxWidth / 2) %>px !important;
                }
            }
        <% } %>
        
        @media (max-width: 600px) {
            .main-container .template .logo-container {
                padding: 32px 16px !important;
            }
            .main-container .template .main-content{
                padding: 14px 16px 20px 16px !important;
            }
            .main-container .template .main-content .welcome-p{
                padding: 0 !important;
            }
            .main-container .template .main-content .welcome-p p:first-of-type{
                margin-top: 0 !important;
            }
            .main-container .template .parent-app {
                margin: 10px 8px !important;
            }
            .main-container .flex-md-column{
                flex-direction: column !important;
            }
            .main-container .template .cheers {
                margin: 91px 0 55px 0 !important;
            }
            .main-container footer{
                padding: 29px 29px 13px 29px !important;
            }
            .main-container .mt-25 {
                margin-top: 30px !important;
            }
            .progress-bar{
                width: calc(100vw / 4 - 0px) !important;
                margin-left: calc(100vw / 8 - 0px) !important;
            }
        }
    </style>
    
</head>
<body style="font-family:Manrope, sans-serif;font-style: normal;font-weight: 400;color: #000000;<% if (items.rtl) { %> text-align: right; <% } %>">
    <div class="main-container" style="width: 100%;">
        <div class="template" style="border: 1px solid #EFF2F5;border-radius: 12px;max-width: 560px;margin: 10px auto;">
            <div class="logo-container" style="padding: 32px;">
                <img src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/logo.png" style="width: 102px;height: 24px;" alt="">
            </div>
            <div style="width: 100%;background: #F5F5F5;padding: 20px 0 0 0;overflow: hidden;">
                        <table style="border-collapse: collapse;border-spacing:0;width:100%;">
                            <tbody>
                            <tr>
                                <% for(var i = 0; i< items.status.length; i++) {%>
                                <td style="width: 25%; text-align:center;padding:0;">
                                    <table style="border-collapse: collapse;border-spacing:0;width:100%;">
                                        <tbody>
                                        <tr>
                                            <td style="padding:0;">
                                                <div class="set <% if (i == 0) { %>first<% } %> <% if (i == 3) { %>last<% } %> <% if (items.status_code >= i+1) { %>active-after<% } %>" style="position: relative;height: 60px; z-index:1; text-align:center; <% if (items.status_code >= i) { %> margin-top: 0; <% } else{ %> margin-top: 0px; <% } %>">
                                                    <% if (i == 3) { %> <% } else{ %>  
                                                        <div style="width: 100%;">
                                                            <div style="max-height: 0;max-width: 0;overflow: visible;">
                                                                <% if (items.status_code >= i+1) { %>
                                                                    <img class="progress-bar" src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/progressbar-email-active.png" style="width: 140px;height: 4px;margin-top: 10px;margin-left: 70px;"/>  
                                                                <% } else{ %>  
                                                                    <img class="progress-bar" src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/progressbar-email.png" style="width: 140px;height: 4px;margin-top: 10px;margin-left: 70px;"/>     
                                                                <% } %>
                                                            </div>
                                                        </div>
                                                    <% } %>
                                                    <% if (items.status_code >= i) { %>
                                                        <img src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/checked-status.png" style="width: 24px; height: 24px;margin-bottom: 10px;" />
                                                        <p style="font-weight: 700;font-size: 12px;line-height: 16px;margin: 0 0 10px 0;color: #0286D0;"><%= items.status[i] %></p>
                                                    <% } else{ %>  
                                                        <img src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/status.png" style="width: 18px; height: 18px;margin-bottom: 13px; margin-top: 3px;<% if (i == 3) { %>margin-left: 15px;padding-right: 15px;background: #F5F5F5;<% } %>" />
                                                        <p style="font-weight: 400;font-size: 12px;line-height: 16px;margin: 0 0 13px 0;color: #A0A0A0;"><%= items.status[i] %></p>
                                                    <% } %>
                                                </div>
                                            </td>    
                                        </tr>
                                        </tbody>
                                    </table>
                                </td>   
                                <% } %>
                            </tr>
                            </tbody>
                        </table>
                    </div>
            <div class="main-content" style="background: #F5F5F5;padding: 14px 22px 20px 22px;">
                <div class="welcome-p" style="padding: 0px 20px;">
                    <p style="font-size: 18px;line-height: 25px;font-weight: 600; margin-top: 0;"><%= fonts.order_p %></p>
                    <p style="font-weight: 400;font-size: 16px;line-height: 22px;color: #5E6278;margin-top: 8px">Dear <%= items.customer_legal_name %>,</p>
                    <p style="font-weight: 400;font-size: 16px;line-height: 22px;color: #5E6278;margin-top: 8px; margin-bottom: 41px;"><%= fonts.order_desc_p %></p>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 600; margin: 0px 0 6px 0;"><%= fonts.order_info_label %> </p>
                    <table style="border-collapse: separate;border-spacing:0; width: 100%">
                        <% if (!items.rtl) { %> 
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.order_id %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.order_id  %></td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.order_date %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.order_date %></td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.order_from_label %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.company_legal_name %></td>
                        </tr>
                        <% } else{ %>  
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.order_id  %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.order_id %></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.order_date %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.order_date %></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.company_legal_name %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.order_from_label %></td>
                        </tr>
                        <% } %>
                    </table>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 600; margin: 18px 0 6px 0;"><%= fonts.delivery_address_label %></p>
                    <table style="border-collapse: separate;border-spacing:0; width: 100%; margin: 0px 0 22px 0;">
                        <% if (!items.rtl) { %> 
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.address_label %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_address %></td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.region_label %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_region %></td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.city_label %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_city %></td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.mobile_number_label %></td>
                            <td style="text-align: right;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_mobile_number %></td>
                        </tr>
                        <% } else{ %>  
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_address %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.address_label %></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_region %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.region_label %> %></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_mobile_number %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.city_label %></td>
                        </tr>
                        <tr>
                            <td style="text-align: left;padding: 5px 0;font-weight: 400;font-size: 14px;line-height: 19px;color: #3F4254;border-bottom: 1px solid #E4E6EF;"><%= items.shipping_mobile_number %></td>
                            <td style="text-align: right;padding: 5px 0;color: #3F4254;font-weight: 600;font-size: 14px;width: 50%;line-height: 19px;border-bottom: 1px solid #E4E6EF;"><%= fonts.mobile_number_label %></td>
                        </tr>
                        <% } %>
                    </table>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 600; margin: 18px 0 9px 0;"><%= fonts.order_summery %></p>
                    <div style="background: #FFFFFF;border-radius: 12px;padding: 17px 16px 28px 16px;margin-bottom: 45px;height: 128px;">
                        <table style="border-collapse: separate;border-spacing:0;width: 100%">
                            <% if (!items.rtl) { %> 
                                <tr>
                                    <td style="padding-bottom: 23px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.num_of_items %></td>
                                    <td style="text-align: right;padding-bottom: 23px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.num_of_items %></td>
                                </tr>
                                <tr>
                                    <td style="padding-bottom: 23px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.total_qty  %></td>
                                    <td style="text-align: right;padding-bottom: 23px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.total_qty  %></td>
                                </tr>
                                <tr>
                                    <td style="padding-bottom: 23px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.order_amount  %></td>
                                    <td style="text-align: right;padding-bottom: 23px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.order_amount  %></td>
                                </tr>
                            <% } else{ %>  
                                <tr>
                                    <td style="direction: rtl;text-align: left;padding-bottom: 23px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.num_of_items %></td>
                                    <td style="direction: rtl;text-align: right;padding-bottom: 23px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.num_of_items %></td>
                                </tr>
                                <tr>
                                    <td style="direction: rtl;text-align: left;padding-bottom: 23px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.total_qty  %></td>
                                    <td style="direction: rtl;text-align: right;padding-bottom: 23px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.total_qty  %></td>
                                </tr>
                                <tr>
                                    <td style="direction: rtl;text-align: left;padding-bottom: 23px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.order_amount  %></td>
                                    <td style="direction: rtl;text-align: right;padding-bottom: 23px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.order_amount  %></td>
                                </tr>
                            <% } %>
                        </table>
                        <div class="text-center" style="text-align: center;">
                            <a class="classic-btn" href="<%= fonts.hawak_link %>" style="margin: auto;right: 0;text-decoration: none;color: #fff;background: #0387D1;border-radius: 32px;font-family: 'Poppins', sans-serif;font-style: normal;font-weight: 600;font-size: 16px;line-height: 24px;padding: 14px 32px;border: none;display: block;max-width: fit-content;transition: 0.7s all;">
                                <%= fonts.track_order_label %>
                            </a>
                        </div>
                    </div>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 600; margin: 18px 0 9px 0;"><%= fonts.order_paced_with %></p>
                    <div style="background: #FFFFFF;margin-bottom: 45px;border-radius: 12px;padding: 17px 16px 28px 16px;margin-bottom: 45px;height: 80px;">
                        <table style="border-collapse: separate;border-spacing:0;width: 100%">
                            <% if (!items.rtl) { %>
                                <tr>
                                    <td style="padding-bottom: 10px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.salesperson_label %></td>
                                    <td style="text-align: right;padding-bottom: 10px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.salesperson_name %></td>
                                </tr>
                                <tr>
                                    <td style="padding-bottom: 10px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.mobile_number_label  %></td>
                                    <td style="text-align: right;padding-bottom: 10px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.sales_person_mobile %></td>
                                </tr>
                            <% } else{ %>  
                                <tr>
                                    <td style="direction: rtl;text-align: left;padding-bottom: 10px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.salesperson_name %></td>
                                    <td style="direction: rtl;text-align: right;padding-bottom: 10px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.salesperson_label %></td>
                                </tr>
                                <tr>
                                    <td style="direction: rtl;text-align: left;padding-bottom: 10px;font-weight: 400;font-size: 14px;line-height: 19px;color: #000000;"><%= items.sales_person_mobile %></td>
                                    <td style="direction: rtl;text-align: right;padding-bottom: 10px;font-weight: 600;font-size: 14px;line-height: 19px;color: #3F4254;width: 50%;"><%= fonts.mobile_number_label  %></td>
                                </tr>
                            <% } %> 
                        </table>
                        <div class="text-center" style="text-align: center;">
                            <a class="classic-btn" href="{{reset_link}}" style="text-decoration: none;color: #fff;background: #0387D1;border-radius: 32px;font-family: 'Poppins', sans-serif;font-style: normal;font-weight: 600;font-size: 16px;line-height: 24px;padding: 14px 32px;border: none;display: block;max-width: fit-content;transition: 0.7s all;margin: 23px auto 0px auto;height: 25px;">
                                <img src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/call.png" alt="call" style="height: 23px; width: 23px;"/>
                            </a>
                        </div>
                    </div>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 600; margin: 18px 0 15px 0;"><%= fonts.company_details %></p>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 400; margin: 0; color: #5E6278;"><%= items.company_legal_name %></p>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 400; margin: 0; color: #5E6278;"><%= items.company_address %> </p>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 400; margin: 0; color: #5E6278;"><%= items.company_region %></p>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 400; margin: 0; color: #5E6278;"><%= items.company_city %></p>
                    <p style="font-size: 16px;line-height: 22px;font-weight: 400; margin: 0; color: #5E6278;"><%= items.company_phone %> </p>
                </div>
                <div class="cheers" style="margin: 91px 0 50px 0;">
                    <p style="font-size: 18px;line-height: 10px;"><%= fonts.thanks %>,</p>
                    <p style="font-size: 18px;line-height: 10px;"><%= fonts.team_hawak %></p>
                </div>
                <div style="text-align:center; margin: 0px 0 38px 0;">
                   <a href="<%= fonts.app_store_url %>"><img style="margin: 0 8px; width: 180px; height: 52px;"src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/App+Store.png" alt="App Store"></a>
                    <a href="<%= fonts.play_store_url %>"><img style="margin: 0 8px; width: 180px; height: 52px;" class="parent-app" src="https://hawak-dev-public.s3.me-south-1.amazonaws.com/hawak-dev/template-images/Google+Play.png" alt="Play Store"></a>
                </div>
            </div>
            <footer style="padding: 30px 39px 13px 39px;text-align: center;font-size: 14px;line-height: 19px;">
                <p style="margin-top: 0;"><%= fonts.copy_rights %></p>
                <p style="margin-top: 35px;line-height: 8px;" class="district"><%= fonts.address %></p>
                <p style="color: #A1A5B7;line-height: 13px;margin: 0;" class="contact"><%= fonts.contact %></p>
            </footer>
        </div>
    </div>
</body>
</html>