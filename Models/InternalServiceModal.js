const axios = require("axios");

const TenantSchema = require("../Database/Schemas/tenants");

const {
    httpSalesPersonService,
} = require('../Utils/helpers');

const {
    VALUES
} = require("../Configs/constants")

class InternalServiceModal {
    async findTenantById(_id, projection = "") {
        const element = await TenantSchema.findById(_id, projection)
        return element
    }

    async updateTenantById(_id, incObj) {
        const element = await TenantSchema.updateOne(
            {
                _id
            },
            {
                "$inc": incObj
            }
        )
        return element
    }

    async validateStandUpload(body) {
        try {
            const url = VALUES.internalSalesPersonServiceBaseURL + "validateStandUpload"

            const data = {
                tenantId: body.tenantId,
                standId: body.standId,
                customerUserRoleId: body.customerUserRoleId,
            }

            const response = await axios({
                method: "POST",
                url,
                data,
            })

            const details = response?.["data"]?.["data"]
            return details;
        }
        catch (error) {
            throw error
        }
    }

    async updateAssignedStandCustomerDetails(body) {
        try {
            const url = VALUES.internalSalesPersonServiceBaseURL + "assignedStandCustomer"

            const response = await axios({
                method: "PUT",
                url,
                data: body,
            })

            const details = response?.["data"]?.["data"]
            return details;
        }
        catch (error) {
            throw error
        }
    }

    async updateAssignStandSalesPersonUserRoleId(body) {
        try {
            const url = VALUES.internalSalesPersonServiceBaseURL + "assignStandSalesPersonUserRoleId"

            const response = await axios({
                method: "PUT",
                url,
                data: body,
            })

            const details = response?.["data"]?.["data"]
            return details;
        }
        catch (error) {
            throw error
        }
    }

    async standQrCodeScan(req) {
        let statuscode
        let data
        let error

        try {
            const response = await httpSalesPersonService(req).post("/validateStandQr", req.body)
            statuscode = response.status
            data = response.data
        }
        catch (err) {
            statuscode = err.response?.status ?? 500
            data = err.response?.data
            error = data?.error ?? err
        }
        finally {
            return {
                statuscode,
                data,
                error
            }
        }
    }

    removeSalespersonFromTracking = async (req, data) => {
        try {
            const response = await httpSalesPersonService(req)
                .delete(
                    "/removeSalesperson",
                    {
                        data
                    }
                )

            const details = response?.["data"]
            return details;
        }
        catch (error) {
            throw error
        }
    }

    async updateOrders(body) {
        try {
            const response = await axios({
                method: "PUT",
                url: VALUES.internalServiceBaseURL + "orders",
                data: body,
            })

            return response?.["data"]?.["data"]
        }
        catch (error) {
            throw error
        }
    }

}

module.exports = InternalServiceModal
