const UserRoleDeviceSchema = require("../Database/Schemas/UserRoleDeviceSchema")

module.exports = class {

    upsertDevice = (filter, update, options) => {
        return UserRoleDeviceSchema.findOneAndUpdate(
            filter,
            update,
            {
                "upsert": true,
                "runValidators": true,
                ...options
            }
        )
    }

    deleteDevicesByFilter = (filter) => {
        return UserRoleDeviceSchema.deleteMany(filter)
    }

    deleteDevicesByFcmTokens = (fcmTokens, session) => {
        return UserRoleDeviceSchema.deleteMany(
            {
                fcm_token: {
                    $in: fcmTokens
                }
            },
            {
                session
            }
        )
    }

    getFcmTokensByUserRoleIds = async (
        userRolesIds,
        session
    ) => {
        const data = await UserRoleDeviceSchema.find(
            {
                user_role_id: {
                    $in: userRolesIds
                }
            },
            {
                user_role_id: 1,
                fcm_token: 1
            },
            {
                lean: true,
                session
            }
        )

        return data.reduce((tokens, value) => {
            if (!tokens[value.user_role_id]) {
                tokens[value.user_role_id] = []
            }
            tokens[value.user_role_id].push(value.fcm_token)

            return tokens
        }, {})
    }

}
