const HoldReasonSchema = require('../Database/Schemas/HoldReasonSchema');

class HoldReasonModel {

    createHoldReason = async (body) => {
        return new HoldReasonSchema(body);
    };

    updateHoldReason = async (filter = {}, update = {}) => {
        return HoldReasonSchema.updateOne(
            filter,
            {
                $set: update,
            }
        );
    };

    getHoldReasons = async (filter = {}, projection = {}, options = {}) => {
        return HoldReasonSchema.find(
            filter,
            projection,
            options
        );
    };

    getHoldReason = async (filter = {}, projection = {}, options = {}) => {
        return HoldReasonSchema.findOne(
            filter,
            projection,
            options
        );
    };

    createHoldReasons = async (body) => {
        return HoldReasonSchema.insertMany(body);
    }


    async updateHoldReasons(filter, updateFields) {
        return HoldReasonSchema.updateMany(
            filter,
            {
                "$set": {
                    ...updateFields,
                },
            }
        )
    }
}

module.exports = HoldReasonModel;
