const HoldReasonTemplateOptionsSchema = require('../Database/Schemas/HoldReasonTemplateOptionsSchema');

class HoldReasonTemplateOptionsModel {

    getHoldReasonTemplateOption = async (filter = {}, projection = {}, options = {}) => {
        return HoldReasonTemplateOptionsSchema.findOne(
            filter,
            projection,
            options
        );
    };

}

module.exports = HoldReasonTemplateOptionsModel;
