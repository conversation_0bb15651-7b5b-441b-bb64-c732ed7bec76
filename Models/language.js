const https = require('https');
const path = require('path');
const fs = require('fs');

const { languages: LanguageSchema } = require("../Database/Schemas");

class LanguageModel {
    async getMasterFileContent(languageCode = "en", type = "master") {
        return new Promise((resolve, reject) => {
            https.get(`${process.env.AWS_LOCALES_BUCKET_BASE_URL}${type}/${languageCode}.json`, function(response) {
                const filePath = path.join(__dirname, `/../Assets/Jsons/${type}/${languageCode}_${Date.now()}.json`);
                const fileStream = fs.createWriteStream(filePath);
                response.pipe(fileStream);
                fileStream.on("finish", function() {
                    fileStream.close();
                    const fileContent = require(filePath);
                    delete require.cache[filePath]; // delete require method cache
                    fs.unlinkSync(filePath);
                    resolve(fileContent);
                    return;
                })
                fileStream.on("error", function(error) {
                    reject(error);
                    return;
                })
            })
        })
    }

    async getExistingLanguageByCode(languageCode) {

        return LanguageSchema.aggregate([
            {
                $addFields: {
                    lowerCode: {
                        $toLower: "$language_code"
                    }
                }
            },
            {
                $match: {
                    lowerCode: languageCode.toLowerCase()
                }
            },
            {
                $count: "count"
            },
            {
                $unwind: "$count"
            }
        ]);
    }

    async createLanguage(body, headers) {
        return LanguageSchema({
            name: body.name,
            language_code: body.languageCode,
            created_by: headers.userDetails?._id,
            updated_by: headers.userDetails?._id,
            key_count: body.key_count,
            enable_rtl: body.enableRtl === "true" ? true : false,
        })
    }

    async updateEngLanguageKeyCountByOne() {
        return LanguageSchema.updateOne({ language_code: "en" }, { $inc: { key_count : 1 } });
    }

    async getAllLanguages() {
        return LanguageSchema.find({});
    }

    async getExistingLanguageById(languageId) {
        return LanguageSchema.findById(languageId);
    }

    async translationCountForLanguage(enMasterJson, langMasterJson) {
        let count = 0;
        for (const key in enMasterJson) {
            if (Object.hasOwnProperty.call(enMasterJson, key) && Object.hasOwnProperty.call(langMasterJson, key)) {
                if (enMasterJson[key].both && langMasterJson[key].both && enMasterJson[key].both !== langMasterJson[key].both) {
                    count++;
                    langMasterJson[key].isTranslated = true;
                } else if (enMasterJson[key].web || enMasterJson[key].tab) {
                    if ((langMasterJson[key].web && (enMasterJson[key].web !== langMasterJson[key].web)) || (langMasterJson[key].tab && (enMasterJson[key].tab !== langMasterJson[key].tab))) {
                        count++;
                        langMasterJson[key].isTranslated = true;
                    }
                }
            }
        }
        return count
    }
}

module.exports = LanguageModel;