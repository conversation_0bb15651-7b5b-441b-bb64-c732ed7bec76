const IntegrationCredentialSchema = require('../Database/Schemas/IntegrationCredentialSchema');

class IntegrationCredentialModel {

    createCredential = async (body) => {
        return new IntegrationCredentialSchema(body);
    };

    updateCredential = async (filter = {}, body) => {
        return IntegrationCredentialSchema.updateOne(
            filter,
            {
                $set: body,
            }
        );
    };

    getCredential = async (filter = {}, projection = {}, options = {}) => {
        return IntegrationCredentialSchema.findOne(
            filter,
            projection,
            options
        );
    };

    getCredentials = async (filter = {}, projection = {}, options = {}) => {
        return IntegrationCredentialSchema.find(
            filter,
            projection,
            options
        );
    };
}

module.exports = IntegrationCredentialModel;
