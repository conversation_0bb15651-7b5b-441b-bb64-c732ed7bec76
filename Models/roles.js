const { roles: RoleSchema, modules: ModuleSchema } = require('../Database/Schemas')

const { VALUES } = require('../Configs/constants');

class RoleModal {

    async getCustomerRole(projection = {}, options = {}) {
        return RoleSchema.findOne({ name: "Customer", portal_type: VALUES.portals.CUSTOMER_APP }, projection, options);
    }

    async getRoleByFilter(filter, projection= {}, options = {}) {
        return RoleSchema.findOne(filter, projection, options);
    }

    async getRolesWithFilter(filter, projection= {}, options = {}) {
        return RoleSchema.find(filter, projection, options);
    }
    
    async findTenantPortalRole(body, headers) {
        
        //return RoleSchema.findOne({ portal_type: VALUES.portals.TENANT_PORTAL, tenant_id: body.tenantId, name: {$regex: new RegExp(`.*${body.name}.*`, 'i')}, branch_id: null });
        return RoleSchema.aggregate([
            {
                $match: {
                    portal_type: VALUES.portals.TENANT_PORTAL,
                    tenant_id: body.tenantId,
                    branch_id: null // TODO: need to check wether we exclude branch portal roles or not.
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    }
                }
            },
            {
                $match: {
                    lowerName: body.name.toLowerCase()
                }
            },
            {
                $count: "count"
            }
        ]);
    }

    async findBranchRoleNameExpect(body, headers) {
        
        // return RoleSchema.findOne({ portal_type: VALUES.portals.BRANCH_PORTAL, tenant_id: body.tenantId,_id: {$ne: body.roleId}, name: {$regex: new RegExp(`.*${body.name}.*`, 'i')}, branch_id: body.branchId,  })
        return RoleSchema.aggregate([
            {
                $match: {
                    portal_type: VALUES.portals.BRANCH_PORTAL,
                    tenant_id: body.tenantId,
                    branch_id: body.branchId,
                    _id: {$ne: body.roleId}
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    }
                }
            },
            {
                $match: {
                    lowerName: body.name.toLowerCase()
                }
            },
            {
                $count: "count"
            }
        ]);
    }
    
    async findTenantRoleNameExpect(body, headers) {
        
        // return RoleSchema.findOne({ portal_type: VALUES.portals.TENANT_PORTAL, tenant_id: body.tenantId,_id: {$ne: body.roleId}, name: {$regex: new RegExp(`.*${body.name}.*`, 'i')}, branch_id: null })
        return RoleSchema.aggregate([
            {
                $match: {
                    portal_type: VALUES.portals.TENANT_PORTAL,
                    tenant_id: body.tenantId,
                    branch_id: null,
                    _id: {$ne: body.roleId}
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    }
                }
            },
            {
                $match: {
                    lowerName: body.name.toLowerCase()
                }
            },
            {
                $count: "count"
            }
        ]);
    }

    
    async findSystemRoleExpect(body, headers) {
        
        // return RoleSchema.findOne({ portal_type: VALUES.portals.SYSTEM_PORTAL, _id: {$ne: body.roleId}, name: {$regex: new RegExp(`.*${body.name}.*`, 'i')}, });
        return RoleSchema.aggregate([
            {
                $match: {
                    portal_type: VALUES.portals.SYSTEM_PORTAL,
                    _id: {$ne: body.roleId}
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    }
                }
            },
            {
                $match: {
                    lowerName: body.name.toLowerCase()
                }
            },
            {
                $count: "count"
            }
        ]);
    }

    async findBranchPortalRole(body, headers) {
        if(body.name.includes(".")) {
            body.name.replace(".", "[.]")
        }
        // return RoleSchema.findOne({ portal_type: VALUES.portals.BRANCH_PORTAL, tenant_id: body.tenantId, name: {$regex: new RegExp(`.*${body.name}.*`, 'i')}, branch_id: body.branchId })
        return RoleSchema.aggregate([
            {
                $match: {
                    portal_type: VALUES.portals.BRANCH_PORTAL,
                    tenant_id: body.tenantId,
                    branch_id: body.branchId,
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    }
                }
            },
            {
                $match: {
                    lowerName: body.name.toLowerCase()
                }
            },
            {
                $count: "count"
            }
        ]);
    }

    async findSystemPortalRole(body, headers) {
       
        // return RoleSchema.findOne({ portal_type: VALUES.portals.SYSTEM_PORTAL, name: {$regex: new RegExp(`.*${body.name}.*`, 'i')} });
        return RoleSchema.aggregate([
            {
                $match: {
                    portal_type: VALUES.portals.SYSTEM_PORTAL,
                }
            },
            {
                $addFields: {
                    lowerName: {
                        $toLower: "$name"
                    }
                }
            },
            {
                $match: {
                    lowerName: body.name.toLowerCase()
                }
            },
            {
                $count: "count"
            }
        ]);
    }

    async getSystemPortalRoles(body, headers) {
        return RoleSchema.find({ portal_type: VALUES.portals.SYSTEM_PORTAL, is_active: true, is_deleted: false });
    }

    /**
     * if tenant_id is not provided then will give default tenant roles ( which are not custom )
     * @param {Number} tenant_id 
     * @param {Object} projection 
     * @param {Object} options 
     * @returns {Array}
     */
    async getTenantPortalRoleByTenantId(tenant_id, projection = {}, options = {}) {
        let query = {
            portal_type: VALUES.portals.TENANT_PORTAL, is_active: true, is_deleted: false
        }
        if(!tenant_id) {
            query['tenant_id'] = { $eq: null }
        } else {
            query['tenant_id'] = tenant_id
        }
        return RoleSchema.find(query, projection, options);
    }

    async getStandardBranchPortalRoles() {
        const query = {
            $or: 
            [
                // default BRANCH_PORTAL roles
                {portal_type: VALUES.portals.BRANCH_PORTAL, tenant_id: {$eq: null}, branch_id: {$eq: null}, is_active: true, is_deleted: false},
                // default SALES_APP roles
                {portal_type: VALUES.portals.SALES_APP, tenant_id: {$eq: null}, branch_id: {$eq: null}, is_active: true, is_deleted: false}
            ]
        };
        return RoleSchema.find(query);
    }

    async getSalesAppRoles() {
        return RoleSchema.find({ portal_type: VALUES.portals.SALES_APP, is_active: true, is_deleted: false, branch_id: { $eq: null } });
    }

    async getStandardSalesAppRoles() {
        return RoleSchema.find({ portal_type: VALUES.portals.SALES_APP, is_active: true, is_deleted: false });
    }

    async getCustomerPortalRole() {
        return RoleSchema.find({ portal_type: VALUES.portals.CUSTOMER_APP, is_active: true, is_deleted: false });
    }

    // TODO: go through once again for tent roles module development
    async getTenantAndBranchPortal(body, headers) {
        const query = {
            $or: 
            [
                // tenantId portal roles // TODO: uncomment below line to get custom tenant id related roles
                // {portal_type: VALUES.portals.TENANT_PORTAL, tenant_id: body?.tenantId, is_active: true, is_deleted: false}, 
                // default TENANT_PORTAL
                {portal_type: VALUES.portals.TENANT_PORTAL, tenant_id: {$eq: null}, branch_id: {$eq: null}, is_active: true, is_deleted: false},
                // default BRANCH_PORTAL roles
                {portal_type: VALUES.portals.BRANCH_PORTAL, tenant_id: {$eq: null}, branch_id: {$eq: null}, is_active: true, is_deleted: false},
                // default SALES_APP roles
                    { portal_type: VALUES.portals.SALES_APP, tenant_id: { $eq: null }, branch_id: { $eq: null }, is_active: true, is_deleted: false },
                    { portal_type: VALUES.portals.SUPERVISOR_APP, tenant_id: { $eq: null }, branch_id: { $eq: null }, is_active: true, is_deleted: false },
            ]
        };
        return RoleSchema.find(query);
    }
    async addRole(body, headers) {
        let permission;
        let portalModules = await ModuleSchema.find({ portal_type: body.portalType }).lean();
        permission = portalModules.reduce((preVal, curVal) => {
            preVal[curVal.name] = curVal.default_actions;
            return preVal
        }, {});
        // switch (body.portalType) {
        //     case VALUES.portals.SYSTEM_PORTAL:
        //         permission = VALUES.MODULES.SYSTEM_PORTAL.reduce((preVal, curVal) => {
        //             preVal[curVal] = { view: false, edit: false, delete: false, create: false };
        //             return preVal
        //         }, {});
                
                
        //         break;
        
        //     case VALUES.portals.TENANT_PORTAL:
        //         permission = VALUES.MODULES.TENANT_PORTAL.reduce((preVal, curVal) => {
        //             preVal[curVal] = { view: false, edit: false, delete: false, create: false };
        //             return preVal
        //         }, {});
        // }

        return RoleSchema({
            name: body.name,
            description: body.description,
            portal_type: body.portalType,
            tenant_id: body.tenantId,
            branch_id: body.branchId,
            is_custom: true,
            is_editable: true,
            is_deleteable: true,
            // permission: body.permission,
            is_deleted: false,
            is_active: true,
            permission,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
        });
    }

    async getRoleFromRoleID(role_id) {
        return RoleSchema.findById(role_id);
    }

    // async addModule(body) {
    //     const module = ModuleSchema({
    //         name: body.name,
    //         portal_type: body.portalType,
    //     });

    //     await module.save();
    // }

    async getPortalModules(portal_type) {
        return ModuleSchema.find({portal_type})
    }
}

module.exports = RoleModal;