const { CustomerVerificationSchema } = require("../Database/Schemas")

class CustomerVerificationModel {

    async deleteCustomerVerifications(user_role_id, mobile_number) {
        return CustomerVerificationSchema.deleteMany(
            {
                user_role_id,
                mobile_number
            }
        )
    }

    async addCustomerVerification(req, otp) {
        const { countryCode, mobileNumber } = req.body

        return CustomerVerificationSchema({
            user_role_id: req.headers.userroleid,
            country_code: countryCode,
            mobile_number: mobileNumber,
            otp,
        })
    }

    async findCustomerVerification(user_role_id, mobile_number) {
        return CustomerVerificationSchema.findOne(
            {
                user_role_id,
                mobile_number
            }
        )
    }
}

module.exports = CustomerVerificationModel
