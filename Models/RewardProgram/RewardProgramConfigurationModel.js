const RewardProgramConfigurationSchema = require("../../Database/Schemas/RewardProgram/RewardProgramConfigurationSchema")

module.exports = class {

    findOrCreateConfiguration = async (tenantId, userId, lean = true) => {
        const configuration = await this.findConfigurationByTenantId(
            tenantId,
            undefined,
            {
                lean
            }
        )

        if (configuration)
            return configuration

        return await this.#addConfiguration(tenantId, userId)
    }

    #addConfiguration = (tenantId, userId) => {
        return new RewardProgramConfigurationSchema(
            {
                tenant_id: tenantId,
                created_by: userId,
                updated_by: userId,
            }
        ).save()
    }

    save = (obj, userId) => {
        obj.updated_by = userId
        return obj.save()
    }

    findConfigurationByTenantId = (tenant_id, projection, options) => {
        return RewardProgramConfigurationSchema.findOne(
            {
                tenant_id
            },
            projection,
            options ?? {
                lean: true
            }
        )
    }

}
