const RewardProgramSchema = require("../../Database/Schemas/RewardProgram/RewardProgramSchema")

const {
    ENTITY_STATUS
} = require('../../Configs/constants');

module.exports = class {

    addRewardProgram = (body, userId) => {
        return new RewardProgramSchema(
            {
                ...body,
                created_by: userId,
                updated_by: userId,
            }
        ).save()
    }

    saveRewardProgram = (obj, userId) => {
        obj.updated_by = userId
        return obj.save()
    }

    findRewardProgramById = (id, projection, options) => {
        return RewardProgramSchema.findById(
            id,
            projection,
            options
        )
    }

    findRewardProgramWithPagination = async (filter) => {
        const {
            perPage,
            page,
        } = filter

        const match = this.#getMatchCondition(filter)

        const [list, count] = await Promise.all([
            RewardProgramSchema.find(
                match,
                `
                    -created_by
                    -updated_by
                    -__v
                `,
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: { created_at: -1 },
                    lean: true
                }
            ),
            RewardProgramSchema.countDocuments(match)
        ])

        return { list, count }
    }

    findRewardProgram = (filter, projection, options) => {
        return RewardProgramSchema.find(
            this.#getMatchCondition(filter),
            projection,
            options ?? {
                lean: true
            }
        )
    }

    #getMatchCondition = (filter) => {
        const {
            tenantId,
            status,
            searchKey
        } = filter

        const match = {
            tenant_id: tenantId,
        }

        switch (status) {
            case ENTITY_STATUS.ACTIVE: {
                match.is_active = true
                break
            }
            case ENTITY_STATUS.INACTIVE: {
                match.is_active = false
                break
            }
        }

        if (searchKey) {
            match.$or = [
                { "name": new RegExp(searchKey, "i") },
                { "secondary_language_name": new RegExp(searchKey, "i") },
            ]
        }

        return match
    }

}
