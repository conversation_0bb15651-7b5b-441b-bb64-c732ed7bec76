const RewardProgramProductClaimSchema = require("../../Database/Schemas/RewardProgram/RewardProgramProductClaimSchema")

const RewardProgramProductClaimCounterModel = new (require("./RewardProgramProductClaimCounterModel"))()

module.exports = class {

    addClaim = async (body, claimCoins, userId, session) => {
        const {
            tenantId,
            rewardProductId,
            rewardProgramId,
            rewardProgramMemberId,
            customerUserRoleId,
        } = body

        const claimCounter = await RewardProgramProductClaimCounterModel.findOrCreateClaimCounter(tenantId, session)
        const claimNumber = claimCounter.counter += 1

        const newClaim = new RewardProgramProductClaimSchema(
            {
                tenant_id: tenantId,
                reward_program_product_id: rewardProductId,
                reward_program_id: rewardProgramId,
                reward_program_member_id: rewardProgramMemberId,
                customer_user_role_id: customerUserRoleId,
                claim_number: claimNumber,
                unique_claim_number: tenantId + "_" + claimNumber,
                claim_coins: claimCoins,
                created_by: userId,
                updated_by: userId,
            }
        )

        await newClaim.save({ session })
        await RewardProgramProductClaimCounterModel.save(claimCounter, session)

        return newClaim
    }

    updateClaims = (filter, updateFields, userId, options) => {
        return RewardProgramProductClaimSchema.updateMany(
            this.#getMatchCondition(filter),
            {
                ...updateFields,
                updated_by: userId
            },
            options ?? {
                lean: true
            }
        )
    }

    findClaim = (filter, projection, options) => {
        return RewardProgramProductClaimSchema.findOne(
            this.#getMatchCondition(filter),
            projection,
            options ?? {
                lean: true
            }
        )
    }

    findClaims = (filter, projection, options) => {
        return RewardProgramProductClaimSchema.find(
            this.#getMatchCondition(filter),
            projection,
            options
        )
    }

    findClaimsWithPagination = async (filter) => {
        const {
            perPage,
            page,
        } = filter

        const match = this.#getMatchCondition(filter)

        const [list, count] = await Promise.all([
            RewardProgramProductClaimSchema.find(
                match,
                `
                    -created_by
                    -updated_by
                    -__v
                `,
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: { created_at: -1 },
                    lean: true,
                    populate: [
                        {
                            path: "reward_program_product_id",
                            select: "name secondary_language_name item_number"
                        },
                        {
                            path: "reward_program_member_id",
                            select: "member_id"
                        },
                    ]
                }
            ),
            RewardProgramProductClaimSchema.countDocuments(match)
        ])

        return { list, count }
    }

    #getMatchCondition = (filter) => {
        const {
            tenantId,
            claimStatus,
            claimIds,
            rewardProgramMemberIds,
            customerUserRoleIds,
            searchKey
        } = filter

        const match = {
            tenant_id: tenantId
        }

        if (claimStatus) {
            if (Array.isArray(claimStatus)) {
                match.status = {
                    $in: claimStatus
                }
            }
            else {
                match.status = claimStatus
            }
        }

        if (claimIds) {
            match._id = {
                $in: claimIds
            }
        }

        if (rewardProgramMemberIds) {
            match.reward_program_member_id = {
                $in: rewardProgramMemberIds
            }
        }

        if (customerUserRoleIds) {
            match.customer_user_role_id = {
                $in: customerUserRoleIds
            }
        }

        if (searchKey) {
            match.$or = [
                { "claim_number": new RegExp(searchKey, "i") },
            ]
        }

        return match
    }

}
