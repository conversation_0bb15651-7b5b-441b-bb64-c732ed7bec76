const RewardProgramProductClaimCounterSchema = require("../../Database/Schemas/RewardProgram/RewardProgramProductClaimCounterSchema")

module.exports = class {

    findOrCreateClaimCounter = async (tenantId, session) => {
        const counter = await this.#findClaimCounterById(tenantId, session)

        if (counter)
            return counter

        return await this.#addClaimCounter(tenantId, session)
    }

    #addClaimCounter = (tenantId, session) => {
        return new RewardProgramProductClaimCounterSchema(
            {
                _id: tenantId,
            }
        ).save({ session })
    }

    save = (obj, session) => {
        return obj.save({ session })
    }

    #findClaimCounterById = (id, session) => {
        return RewardProgramProductClaimCounterSchema.findById(
            id,
            undefined,
            { session }
        )
    }

}
