const RewardProgramMemberSchema = require("../../Database/Schemas/RewardProgram/RewardProgramMemberSchema")

const RewardProgramMemberCounterModel = new (require("./RewardProgramMemberCounterModel"))()

const {
    REWARD_PROGRAM,
    ENTITY_STATUS
} = require("../../Configs/constants");

const { logRewardProgram } = require("../../Utils/logHelper");

module.exports = class {

    addMembers = async (
        customerUserRoleIds,
        rewardProgramId,
        tenantId,
        userId,
        session
    ) => {
        const memberCounter = await RewardProgramMemberCounterModel.findOrCreateMemberCounter(tenantId, session)

        const rawData = customerUserRoleIds.map(customersUserRoleId => {
            const memberId = memberCounter.counter += 1

            return {
                member_id: memberId,
                unique_member_id: tenantId + "_" + memberId,
                tenant_id: tenantId,
                customer_user_role_id: customersUserRoleId,
                reward_program_id: rewardProgramId,
                is_enrolled: true,
                created_by: userId,
                updated_by: userId,
            }
        })

        await RewardProgramMemberSchema.create(
            rawData,
            {
                session,
                ordered: true
            }
        )

        await RewardProgramMemberCounterModel.save(
            memberCounter,
            session
        )
    }

    updateMemberById = (_id, updateObject, options) => {
        return RewardProgramMemberSchema.updateOne(
            {
                _id
            },
            updateObject,
            options
        )
    }

    updateMembers = (
        filter,
        updateFields,
        userId,
        options
    ) => {
        return RewardProgramMemberSchema.updateMany(
            this.#getMatchCondition(filter),
            {
                ...updateFields,
                updated_by: userId
            },
            options
        )
    }

    getMemberCountByFilter = (filter) => {
        return RewardProgramMemberSchema.countDocuments(
            this.#getMatchCondition(filter)
        )
    }

    getMemberCountByMembership = async (filter) => {
        const {
            tenantId,
            rewardProgramId
        } = filter

        const $match = {
            is_enrolled: true,
            tenant_id: tenantId
        }

        if (rewardProgramId) {
            $match.reward_program_id = new mongoose.Types.ObjectId(rewardProgramId)
        }

        const data = await RewardProgramMemberSchema.aggregate([
            {
                $match
            },
            {
                $group: {
                    _id: "$membership",
                    count: {
                        $sum: 1
                    }
                }
            },
        ])

        // TODO: Have one loop on `data` and fetch counts
        return {
            classic: data.find(d => { return d._id === REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.CLASSIC })?.count ?? 0,
            vip: data.find(d => { return d._id === REWARD_PROGRAM.MEMBER.MEMBERSHIP_TYPE.VIP })?.count ?? 0,
        }

    }

    findMemberById = (id, projection, options) => {
        return RewardProgramMemberSchema.findById(
            id,
            projection,
            options
        )
    }

    findMemberByFilter = (filter, projection, options) => {
        return RewardProgramMemberSchema.findOne(
            this.#getMatchCondition(filter),
            projection,
            options ?? {
                lean: true
            }
        )
    }

    findMembersByFilter = (filter, projection, options) => {
        return RewardProgramMemberSchema.find(
            this.#getMatchCondition(filter),
            projection,
            options ?? {
                lean: true
            }
        )
    }

    findMembersWithPagination = async (filter) => {
        const {
            perPage,
            page,
        } = filter

        const match = this.#getMatchCondition(filter)

        const [list, count] = await Promise.all([
            RewardProgramMemberSchema.find(
                match,
                {
                    membership: 1,
                    coins: 1,
                    vip_points: 1,
                    is_enrolled: 1,
                    member_id: 1,
                },
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: { created_at: -1 },
                    lean: true,
                    populate: [
                        {
                            path: "customer_user_role_id",
                            select: "customer_legal_name external_id"
                        },
                        {
                            path: "reward_program_id",
                            select: "name"
                        }
                    ]
                }
            ),
            RewardProgramMemberSchema.countDocuments(match)
        ])

        return { list, count }
    }

    membersCallBackWithPagination = async (filter, callBack) => {
        const limit = 50

        const rewardMemberCount = await this.getMemberCountByFilter(filter)
        const totalMembersPages = Math.ceil(rewardMemberCount / limit)

        logRewardProgram(`For this rewardProgramId (${filter.rewardProgramId}), found ${rewardMemberCount} rewardMembers`, {
            tenantId: filter.tenantId,
            totalPages: totalMembersPages,
        })

        for (let currentPage = 1; currentPage <= totalMembersPages; currentPage++) {
            const memberList = await this.findMembersByFilter(
                filter,
                undefined,
                {
                    sort: {
                        created_at: 1,
                    },
                    skip: limit * (currentPage - 1),
                    limit,
                    populate: [
                        {
                            path: "customer_user_role_id",
                            select: "customer_legal_name external_id customer_payment_term_info"
                        },
                        {
                            path: "reward_program_id",
                            select: "milestones"
                        },
                    ]
                },
            );

            logRewardProgram(`Page ${currentPage} of rewardProgramId (${filter.rewardProgramId}). Found ${memberList.length} rewardMembers`, {
                tenantId: filter.tenantId,
                memberList,
            })

            for (let i = 0; i < memberList.length; i++) {
                try {
                    await callBack(memberList[i])
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }
    }

    membersListBackWithPagination = async (filter) => {
        const limit = 50;

        try {
            const rewardMemberCount = await this.getMemberCountByFilter(filter);

            if (rewardMemberCount === 0) {
                return [];
            }

            const totalMembersPages = Math.ceil(rewardMemberCount / limit);

            const pagePromises = Array.from({ length: totalMembersPages }, (_, index) => {
                const currentPage = index + 1;
                const skip = limit * (currentPage - 1);

                return this.findMembersByFilter(
                    filter,
                    undefined,
                    {
                        sort: { created_at: 1 },
                        skip,
                        limit,
                        populate: [
                            {
                                path: "customer_user_role_id",
                                select: "customer_legal_name external_id customer_payment_term_info"
                            },
                            {
                                path: "reward_program_id",
                                select: "milestones"
                            },
                        ]
                    }
                );
            });
	
            const results = await Promise.all(pagePromises);

            return results.flat();

        } catch (error) {
            console.error('Error in membersListBackWithPagination:', error);
            throw error;
        }
    };

    #getMatchCondition = (filter) => {
        const {
            customerUserRoleIds,
            rewardProgramId,
            customerUserRoleId,
            rewardProgramMemberId,
            rewardProgramMemberIds,
            tenantId,
            status,
            searchKey
        } = filter

        const match = {
            tenant_id: tenantId
        }

        if (rewardProgramId) {
            match.reward_program_id = rewardProgramId
        }

        if (customerUserRoleIds) {
            match.customer_user_role_id = {
                $in: customerUserRoleIds
            }
        }

        if (customerUserRoleId) {
            match.customer_user_role_id = customerUserRoleId
        }

        if (rewardProgramMemberId) {
            match._id = rewardProgramMemberId
        }

        if (rewardProgramMemberIds) {
            match._id = {
                $in: rewardProgramMemberIds
            }
        }

        if (searchKey) {
            match.$or = [
                { "member_id": new RegExp(searchKey, "i") },
            ]
        }

        switch (status) {
            case ENTITY_STATUS.ACTIVE: {
                match.is_enrolled = true
                break
            }
            case ENTITY_STATUS.INACTIVE: {
                match.is_enrolled = false
                break
            }
        }

        return match
    }

    getStatisticsInfoFormPointsLog = (pointLog) => {
        const {
            point_type,
            log_type,
            calculations,
            points
        } = pointLog

        const pointKeys = {
            [REWARD_PROGRAM.POINT.TYPE.COINS]: "coins_statistics",
            [REWARD_PROGRAM.POINT.TYPE.VIP_POINTS]: "vip_points_statistics",
        }

        const statisticsKeyInfo = {
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.PURCHASE]: "purchase_invoice",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.CANCELLATION]: "cancellation_invoice",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.CREDIT_NOTES.RETURNS]: "returns_credit_notes",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.CREDIT_NOTES.DISCOUNT]: "discount_credit_notes",
            [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.DAILY_ACCESS]: "daily_access",
            [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MEMBER_SCAN]: "member_scan",
            [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MANUAL]: "manual",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES]: "milestones",
            [REWARD_PROGRAM.POINT.LOG_TYPE.VIP.UPGRADE]: "vip_upgrade",
            [REWARD_PROGRAM.POINT.LOG_TYPE.VIP.RENEW]: "vip_renew",
            [REWARD_PROGRAM.POINT.LOG_TYPE.EXPIRED.COINS]: "expired_coins",
            [REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.CLAIM]: "reward_claim",
            [REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND]: "reward_claim_refund",
        }

        const calculationKeyInfo = {
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["0_30_DAYS"]]: "0_30_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["31_60_DAYS"]]: "31_60_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["61_90_DAYS"]]: "61_90_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["91_120_DAYS"]]: "91_120_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["120_DAYS_ABOVE"]]: "120_days_above_payment",
        }

        const statisticsInfos = []

        if (calculations?.length) {
            calculations.forEach(calculation => {
                statisticsInfos.push({
                    pointKey: pointKeys[point_type],
                    key: calculationKeyInfo[calculation.type],
                    points: calculation.points
                })
            })
        }
        else {
            statisticsInfos.push({
                pointKey: pointKeys[point_type],
                key: statisticsKeyInfo[log_type],
                points: points
            })
        }

        return statisticsInfos
    }

}
