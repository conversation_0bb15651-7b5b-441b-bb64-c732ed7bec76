const RewardProgramMemberExpiryPointSchema = require("../../Database/Schemas/RewardProgram/RewardProgramMemberExpiryPointSchema")

module.exports = class {

    upsertExpiryPoints = (filter, update, options) => {
        return RewardProgramMemberExpiryPointSchema.findOneAndUpdate(
            filter,
            update,
            {
                "new": true,
                "upsert": true,
                "runValidators": true,
                ...options
            }
        )
    }

    findExpiryPointsByFilter = (filter, projection, options) => {
        return RewardProgramMemberExpiryPointSchema
            .findOne(
                filter,
                projection,
                options
            )
    }

    deleteExpiryPoints = (
        filter,
        options
    ) => {
        return RewardProgramMemberExpiryPointSchema.deleteMany(
            this.#getMatchCondition(filter),
            options
        )
    }

    destroyExpiryPoints = async (
        points,
        point_type,
        reward_program_member_id,
        tenant_id,
        session
    ) => {
        // TODO: Optimize this code logic
        const expiryPointInfo = await RewardProgramMemberExpiryPointSchema.findOne(
            {
                reward_program_member_id,
                tenant_id,
                point_type
            },
            undefined,
            {
                sort: {
                    expiry_year: 1,
                    expiry_month: 1,
                },
                session
            }
        )

        if (!expiryPointInfo) {
            return
        }

        const pendingPoints = points - expiryPointInfo.points

        if (pendingPoints === 0) {
            await expiryPointInfo.deleteOne({ session })
        }
        else if (pendingPoints >= 0) {
            await expiryPointInfo.deleteOne({ session })

            await this.destroyExpiryPoints(
                pendingPoints,
                point_type,
                reward_program_member_id,
                tenant_id,
                session
            )
        }
        else {
            expiryPointInfo.points -= points
            await expiryPointInfo.save({ session })
        }
    }

    expiryPointsCallBackWithPagination = async (filter, callBack) => {
        const limit = 50

        const count = await RewardProgramMemberExpiryPointSchema.countDocuments(filter)
        const totalPages = Math.ceil(count / limit)

        for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
            const list = await RewardProgramMemberExpiryPointSchema.find(
                filter,
                undefined,
                {
                    sort: {
                        created_at: 1,
                    },
                    skip: limit * (currentPage - 1),
                    limit
                },
            );

            for (let i = 0; i < list.length; i++) {
                try {
                    await callBack(list[i])
                }
                catch (error) {
                    logger.error(error)
                }
            }
        }
    }

    #getMatchCondition = (filter) => {
        const {
            customerUserRoleIds,
            rewardProgramId,
            tenantId,
        } = filter

        const match = {
            tenant_id: tenantId
        }

        if (rewardProgramId) {
            match.reward_program_id = rewardProgramId
        }

        if (customerUserRoleIds) {
            match.customer_user_role_id = {
                $in: customerUserRoleIds
            }
        }

        return match
    }

}
