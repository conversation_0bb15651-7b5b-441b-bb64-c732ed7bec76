const RewardProgramMemberCounterSchema = require("../../Database/Schemas/RewardProgram/RewardProgramMemberCounterSchema")

module.exports = class {

    findOrCreateMemberCounter = async (tenantId, session) => {
        const counter = await this.#findMemberCounterById(tenantId, session)

        if (counter)
            return counter

        return await this.#addMemberCounter(tenantId, session)
    }

    #addMemberCounter = (tenantId, session) => {
        return new RewardProgramMemberCounterSchema(
            {
                _id: tenantId,
            }
        ).save({ session })
    }

    save = (obj, session) => {
        return obj.save({ session })
    }

    #findMemberCounterById = (
        id,
        session
    ) => {
        return RewardProgramMemberCounterSchema.findById(
            id,
            undefined,
            { session }
        )
    }

}
