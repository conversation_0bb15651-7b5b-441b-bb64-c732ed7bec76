const RewardProgramProductTagSchema = require("../../Database/Schemas/RewardProgram/RewardProgramProductTagSchema")
const {
    ENTITY_UPDATE_STATUS,
    ENTITY_STATUS
} = require("../../Configs/constants")

module.exports = class {

    addTags = (
        tags,
        tenantId,
        session
    ) => {
        return RewardProgramProductTagSchema.insertMany(
            tags.map(tag => {
                return {
                    name: tag,
                    tenant_id: tenantId
                }
            }),
            {
                session
            }
        )
    }

    updateTagsByIds = (
        ids,
        updateFields,
        userId,
        session
    ) => {
        return RewardProgramProductTagSchema.updateMany(
            {
                _id: {
                    $in: ids
                }
            },
            {
                ...updateFields,
                updated_by: userId
            },
            {
                session
            }
        )
    }

    deleteTags = (
        filter,
        session
    ) => {
        return RewardProgramProductTagSchema.deleteMany(
            filter,
            { session }
        )
    }

    // TODO: Needed optimization for this function
    getUpdatedTagsUsingRewardProductTags = async (
        tags,
        rewardProductTags = [],
        tenantId,
        session,
        userId,
        rewardUpdateStatus,
    ) => {
        const tagLists = await this.findOrCreateTags(
            tags,
            tenantId,
            session
        )
        const updatedTagIds = tagLists.map(tag => {
            return tag._id
        })
        const newTagIds = new Set()
        const removedTagIds = new Set()
        const nonUpdatedTagIds = new Set()

        const allTagIds = [
            ...updatedTagIds,
            ...rewardProductTags
        ]

        allTagIds.forEach(tagId => {
            const tagExist = rewardProductTags.some(id => {
                return tagId.toString() === id.toString()
            })

            const tagRemains = updatedTagIds.some(id => {
                return tagId.toString() === id.toString()
            })

            if (tagRemains) {
                if (tagExist) {
                    nonUpdatedTagIds.add(tagId)
                }
                else {
                    newTagIds.add(tagId)
                }
            }
            else {
                if (tagExist) {
                    removedTagIds.add(tagId)
                }
                else {
                    nonUpdatedTagIds.add(tagId)
                }
            }
        })

        const updateTagCount = async (tags, count, forceRemove) => {
            await this.updateTagsByIds(
                tags,
                {
                    "$inc": {
                        attached_count: count
                    }
                },
                userId,
                session
            )

            if (count < 0) {
                await this.deleteTags(
                    {
                        _id: {
                            $in: tags
                        },
                        attached_count: {
                            $lt: 0
                        }
                    },
                    session
                )
            }
        }

        const countState = {
            [ENTITY_UPDATE_STATUS.ACTIVE] : {
                new: 1,
                removed: 0,
                nonUpdated: 1,
            },
            [ENTITY_UPDATE_STATUS.INACTIVE]: {
                new: 0,
                removed: -1,
                nonUpdated: -1,
            },
            [ENTITY_UPDATE_STATUS.NOT_CHANGED_ACTIVE]: {
                new: 1,
                removed: -1,
                nonUpdated: 0,
            },
            [ENTITY_UPDATE_STATUS.NOT_CHANGED_INACTIVE]: {
                new: 0,
                removed: 0,
                nonUpdated: 0,
            },
        }

        const count = countState[rewardUpdateStatus]
        if (
            newTagIds.size &&
            count.new !== 0
        ) {
            await updateTagCount(Array.from(newTagIds), count.new)
        }
        if (
            removedTagIds.size &&
            count.removed !== 0
        ) {
            await updateTagCount(Array.from(removedTagIds), count.removed)
        }
        if (
            nonUpdatedTagIds.size &&
            count.nonUpdated !== 0
        ) {
            await updateTagCount(Array.from(nonUpdatedTagIds), count.nonUpdated)
        }

        return updatedTagIds
    }

    findOrCreateTags = async (
        tags,
        tenantId,
        session
    ) => {
        const existTagList = await this.findTagsByNames(
            tags,
            tenantId,
            undefined,
            {
                session,
                lean: true
            }
        )
        const notCreatedTag = new Set()

        tags.forEach(tagName => {
            const tagExist = existTagList.some(tag => tag.name === tagName)
            if (!tagExist) {
                notCreatedTag.add(tagName)
            }
        });

        if (notCreatedTag.size) {
            const newTags = await this.addTags(
                Array.from(notCreatedTag),
                tenantId,
                session
            )
            return [
                ...existTagList,
                ...newTags
            ]
        }

        return existTagList
    }

    findTagsByNames = (tags, tenantId, projection, options) => {
        return RewardProgramProductTagSchema.find(
            {
                name: {
                    $in: tags
                },
                tenant_id: tenantId
            },
            projection,
            options ?? {
                lean: true
            }
        )
    }

    findTagsWithPagination = async (filter) => {
        const {
            perPage,
            page,
            tenantId,
            status,
            searchKey,
        } = filter

        const match = {
            tenant_id: tenantId
        }

        switch (status) {
            case ENTITY_STATUS.ACTIVE: {
                match.attached_count = {
                    $gt: 0
                }
                break
            }
        }

        if (searchKey) {
            match.name = new RegExp(searchKey, "i")
        }

        const [list, count] = await Promise.all([
            RewardProgramProductTagSchema.find(
                match,
                `
                    -created_by
                    -updated_by
                    -__v
                `,
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: {
                        created_at: -1,
                        name: 1
                    },
                    lean: true
                }
            ),
            RewardProgramProductTagSchema.countDocuments(match)
        ])

        return { list, count }
    }

}
