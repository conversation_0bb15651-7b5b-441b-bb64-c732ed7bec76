const RewardProgramProductSchema = require("../../Database/Schemas/RewardProgram/RewardProgramProductSchema")

const {
    ENTITY_STATUS
} = require("../../Configs/constants");

module.exports = class {

    addProduct = (
        body,
        userId,
        session
    ) => {
        const {
            tenantId,
            productVariantId,
            name,
            secondaryLanguageName,
            itemNumber,
            requiredCoins,
            inventory,
            tags,
            type,
            imageName,
            isFeatured,
            isActive,
        } = body

        return new RewardProgramProductSchema(
            {
                tenant_id: tenantId,
                product_variant_id: productVariantId,
                name: name,
                secondary_language_name: secondaryLanguageName,
                item_number: itemNumber,
                unique_item_number: `${tenantId}_${itemNumber}`,
                required_coins: requiredCoins,
                inventory: inventory,
                tags: tags,
                type: type,
                image_name: imageName,
                is_featured: isFeatured,
                is_active: isActive,
                created_by: userId,
                updated_by: userId,
            }
        ).save({
            session
        })
    }

    updateProducts = (filter, updateFields, userId, session) => {
        return RewardProgramProductSchema.updateMany(
            this.#getMatchCondition(filter),
            {
                ...updateFields,
                updated_by: userId,
            },
            {
                session
            }
        )
    }

    save = (obj, userId, session) => {
        obj.updated_by = userId
        return obj.save({ session })
    }

    findProducts = (filter, projection, options) => {
        return RewardProgramProductSchema.find(
            filter,
            projection,
            options
        )
    }

    findProductsById = (id, projection, options) => {
        return RewardProgramProductSchema.findById(
            id,
            projection,
            options
        )
    }

    findProductsByItemNumber = (itemNumber, tenantId, projection, options) => {
        return RewardProgramProductSchema.findOne(
            {
                unique_item_number: `${tenantId}_${itemNumber}`,
                tenant_id: tenantId
            },
            projection,
            options
        )
    }

    findProductsWithFilter = (filter, projection, options) => {
        return RewardProgramProductSchema.find(
            this.#getMatchCondition(filter),
            projection,
            options
        )
    }

    findProductsWithPagination = async (filter) => {
        const {
            perPage,
            page,
        } = filter

        const match = this.#getMatchCondition(filter)

        const [list, count] = await Promise.all([
            RewardProgramProductSchema.find(
                match,
                `
                    -created_by
                    -updated_by
                    -__v
                `,
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: {
                        required_coins: 1,
                        created_at: -1,
                        name: 1
                    },
                    lean: true,
                    populate: [
                        {
                            path: "tags",
                            select: "name"
                        }
                    ]
                }
            ),
            RewardProgramProductSchema.countDocuments(match)
        ])

        return { list, count }
    }

    #getMatchCondition = (filter) => {
        const {
            tenantId,
            rewardProductIds,
            searchKey,
            status,
            onlyFeatured,
            tagIds,
            availableCoins
        } = filter

        const match = {
            tenant_id: tenantId,
            is_deleted: false
        }

        if (rewardProductIds) {
            match._id = {
                $in: rewardProductIds
            }
        }

        if (searchKey) {
            match.$or = [
                { "item_number": new RegExp(searchKey, "i") },
                { "name": new RegExp(searchKey, "i") },
                { "secondary_language_name": new RegExp(searchKey, "i") },
            ]
        }

        if (onlyFeatured) {
            match.is_featured = true
        }

        if (tagIds) {
            match.tags = {
                $in: tagIds
            }
        }

        if (typeof availableCoins !== "undefined") {
            match.required_coins = {
                $lte: availableCoins
            }
        }

        switch (status) {
            case ENTITY_STATUS.ACTIVE: {
                match.is_active = true
                break
            }
            case ENTITY_STATUS.INACTIVE: {
                match.is_active = false
                break
            }
        }

        return match
    }

}
