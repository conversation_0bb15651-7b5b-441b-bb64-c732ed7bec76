const RewardProgramPointReportSchema = require("../../Database/Schemas/RewardProgram/RewardProgramPointReportSchema")

const {
    DURATION_PERIOD_OPTIONS,
    REWARD_PROGRAM
} = require('../../Configs/constants');

module.exports = class {

    upsertReport = (filter, update, options) => {
        return RewardProgramPointReportSchema.findOneAndUpdate(
            filter,
            update,
            {
                "upsert": true,
                "runValidators": true,
                ...options
            }
        )
    }

    findLastReportByDate = (filter, projection) => {
        const {
            tenantId,
            rewardProgramId,
            lastDate,
        } = filter

        const match = {
            tenant_id: tenantId,
            date: {
                $lt: lastDate,
            }
        }

        if (rewardProgramId) {
            match.reward_program_id = rewardProgramId
        }

        return RewardProgramPointReportSchema.findOne(
            match,
            projection,
            {
                sort: {
                    date: -1
                },
                lean: true,
            }
        )
    }

    getReportInfoFormPointsLog = (pointLog) => {
        const {
            point_type,
            entry_type,
            log_type,
            points,
            calculations
        } = pointLog

        const distributionKeyInfo = {
            [REWARD_PROGRAM.POINT.ENTRY_TYPE.CLAIMED]: "claimed",
            [REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED]: "distributed",
            [REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED]: "expired",
        }

        const statisticsKeyInfo = {
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.PURCHASE]: "purchase_invoice",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.INVOICE.CANCELLATION]: "cancellation_invoice",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.CREDIT_NOTES.RETURNS]: "returns_credit_notes",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SAP.CREDIT_NOTES.DISCOUNT]: "discount_credit_notes",
            [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.DAILY_ACCESS]: "daily_access",
            [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MEMBER_SCAN]: "member_scan",
            [REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MANUAL]: "manual",
            [REWARD_PROGRAM.POINT.LOG_TYPE.SYSTEM.MILESTONES]: "milestones",
            [REWARD_PROGRAM.POINT.LOG_TYPE.VIP.UPGRADE]: "vip_upgrade",
            [REWARD_PROGRAM.POINT.LOG_TYPE.VIP.RENEW]: "vip_renew",
            [REWARD_PROGRAM.POINT.LOG_TYPE.EXPIRED.COINS]: "expired_coins",
            [REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.CLAIM]: "reward_claim",
            [REWARD_PROGRAM.POINT.LOG_TYPE.REWARD.REFUND]: "reward_claim_refund",
        }

        const calculationKeyInfo = {
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["0_30_DAYS"]]: "0_30_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["31_60_DAYS"]]: "31_60_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["61_90_DAYS"]]: "61_90_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["91_120_DAYS"]]: "91_120_days_payment",
            [REWARD_PROGRAM.POINT.CALCULATION_TYPE.SAP.PAYMENT["120_DAYS_ABOVE"]]: "120_days_above_payment",
        }

        const reportInfos = []

        reportInfos.push({
            key: this.getDistributionKeyFromPointType(point_type) + "." + distributionKeyInfo[entry_type],
            points: points
        })

        if (calculations?.length) {
            calculations.forEach(calculation => {
                reportInfos.push({
                    key: this.getStatisticsKeyFromPointType(point_type) + "." + calculationKeyInfo[calculation.type],
                    points: calculation.points
                })
            })
        }
        else {
            reportInfos.push({
                key: this.getStatisticsKeyFromPointType(point_type) + "." + statisticsKeyInfo[log_type],
                points: points
            })
        }

        return reportInfos
    }

    getDistributionKeyFromPointType = (pointType) => {
        const keyInfo = {
            [REWARD_PROGRAM.POINT.TYPE.COINS]: "coins_distribution",
            [REWARD_PROGRAM.POINT.TYPE.VIP_POINTS]: "vip_points_distribution",
        }

        return keyInfo[pointType]
    }

    getStatisticsKeyFromPointType = (pointType) => {
        const keys = {
            [REWARD_PROGRAM.POINT.TYPE.COINS]: "coins_statistics",
            [REWARD_PROGRAM.POINT.TYPE.VIP_POINTS]: "vip_points_statistics",
        }

        return keys[pointType]
    }

    getStatisticsByYear = async (filter) => {
        const {
            year,
            rewardProgramId,
            tenantId,
            pointType,
            timezone
        } = filter

        const allKeys = [
            "purchase_invoice",
            "cancellation_invoice",
            "0_30_days_payment",
            "31_60_days_payment",
            "61_90_days_payment",
            "91_120_days_payment",
            "120_days_above_payment",
            "returns_credit_notes",
            "discount_credit_notes",
            "daily_access",
            "member_scan",
            "manual",
            "milestones",
            "vip_upgrade",
            "vip_renew",
            "expired_coins",
            "reward_claim",
            "reward_claim_refund",
        ]

        const key = this.getStatisticsKeyFromPointType(pointType)
        const date = new Date(year, 1, 1)

        const $group = allKeys.reduce((data, name) => {
            data[name] = {
                $sum: "$" + key + "." + name
            }

            return data
        }, {
            _id: null
        })

        const $match = {
            tenant_id: tenantId,
            date: {
                $gte: moment.tz(date, timezone).startOf('year').toDate(),
                $lte: moment.tz(date, timezone).endOf('year').toDate(),
            }
        }

        if (rewardProgramId) {
            $match.reward_program_id = new mongoose.Types.ObjectId(rewardProgramId)
        }

        const data = await RewardProgramPointReportSchema.aggregate([
            {
                $match
            },
            {
                $project: {
                    _id: 0,
                    [key]: 1,
                }
            },
            {
                $group
            },
        ])

        return allKeys.reduce((value, name) => {
            value[name] = data[0]?.[name] ?? 0
            return value
        }, {})
    }

    getDashboardHistoricDistributions = async (filter) => {
        const {
            rewardProgramId,
            tenantId,
            pointType,
            durationPeriod,
            timezone
        } = filter

        const key = this.getDistributionKeyFromPointType(pointType)

        const convertedDate = moment.tz(undefined, timezone)
        const endDate = convertedDate.toDate()

        let startDate
        let groupId

        switch (durationPeriod) {
            case DURATION_PERIOD_OPTIONS.DAY: {
                startDate = convertedDate.subtract(6, "days").toDate()
                groupId = "$date"
                break
            }
            case DURATION_PERIOD_OPTIONS.WEEK: {
                startDate = convertedDate.subtract(6, "week").toDate()
                groupId = {
                    $week: "$date"
                }
                break
            }
            case DURATION_PERIOD_OPTIONS.MONTH: {
                startDate = convertedDate.subtract(11, "months").toDate()
                groupId = {
                    $month: "$date"
                }
                break
            }
        }

        const $match = {
            tenant_id: tenantId,
            date: {
                $gte: startDate,
                $lte: endDate,
            }
        }

        if (rewardProgramId) {
            $match.reward_program_id = new mongoose.Types.ObjectId(rewardProgramId)
        }

        const reports = await RewardProgramPointReportSchema.aggregate([
            {
                $match
            },
            {
                $group: {
                    _id: groupId,
                    date: {
                        $first: "$date",
                    },
                    distributed: {
                        $sum: "$" + key + ".distributed"
                    },
                    expired: {
                        $sum: "$" + key + ".expired"
                    },
                    claimed: {
                        $sum: "$" + key + ".claimed"
                    },
                }
            }
        ])

        return {
            reports,
            startDate,
            endDate,
        }
    }

    getDistributionByYear = async (filter) => {
        const {
            year,
            rewardProgramId,
            tenantId,
            pointType,
            timezone
        } = filter

        const key = this.getDistributionKeyFromPointType(pointType)
        const date = new Date(year, 1, 1)

        const $match = {
            tenant_id: tenantId,
            date: {
                $gte: moment.tz(date, timezone).startOf('year').toDate(),
                $lte: moment.tz(date, timezone).endOf('year').toDate(),
            }
        }

        if (rewardProgramId) {
            $match.reward_program_id = new mongoose.Types.ObjectId(rewardProgramId)
        }

        const data = await RewardProgramPointReportSchema.aggregate([
            {
                $match
            },
            {
                $project: {
                    _id: 0,
                    [key]: 1,
                }
            },
            {
                $group: {
                    _id: null,
                    distributed: {
                        $sum: "$" + key + ".distributed"
                    },
                    expired: {
                        $sum: "$" + key + ".expired"
                    },
                    claimed: {
                        $sum: "$" + key + ".claimed"
                    },
                }
            },
        ])

        return {
            distributed: data[0]?.distributed ?? 0,
            expired: data[0]?.expired ?? 0,
            claimed: data[0]?.claimed ?? 0,
        }
    }

    getOpeningBalanceByYear = async (filter) => {
        const {
            year,
            rewardProgramId,
            tenantId,
            pointType,
            timezone
        } = filter

        const key = this.getDistributionKeyFromPointType(pointType)
        const $match = {
            tenant_id: tenantId,
            date: {
                $lt: moment.tz(new Date(year, 1, 1), timezone).startOf('year').toDate(),
            }
        }

        if (rewardProgramId) {
            $match.reward_program_id = rewardProgramId
        }

        const data = await RewardProgramPointReportSchema.aggregate([
            {
                $match
            },
            {
                $sort: { date: -1 }
            },
            {
                $project: {
                    _id: 0,
                    reward_program_id: 1,
                    [key]: 1,
                }
            },
            {
                $group: {
                    _id: "$reward_program_id",
                    lastReport: { $first: "$$ROOT" }
                }
            },
            {
                $group: {
                    _id: null,
                    totalClosingBalance: { $sum: "$lastReport." + key + ".closing_balance" }
                }
            },
        ])

        return data[0]?.totalClosingBalance ?? 0
    }

}
