const ScheduledNotificationSchema = require("../../Database/Schemas/Notification/ScheduledNotificationSchema");

module.exports = class {

    scheduleNotification = (body, userId) => {
        const {
            tenantId,
            notificationType,
            title,
            message,
            imageName,
            scheduleAt,
            priceListIds,
            data
        } = body

        return new ScheduledNotificationSchema({
            tenant_id: tenantId,
            notification_type: notificationType,
            title: title,
            message: message,
            image_name: imageName,
            schedule_at: scheduleAt,
            price_list_ids: priceListIds,
            data: data,
            created_by: userId,
            updated_by: userId,
        }).save()
    }

    save = (obj, userId) => {
        obj.updated_by = userId
        return obj.save()
    }

    getScheduledNotificationByFilter = (filter) => {
        const {
            scheduledNotificationId,
            tenantId,
        } = filter

        return ScheduledNotificationSchema.findOne(
            {
                _id: scheduledNotificationId,
                tenant_id: tenantId,
            }
        )
    }

    listScheduledNotifications = (
        filter,
        projection,
        options
    ) => {
        return ScheduledNotificationSchema.find(filter, projection, options)
    }

}
