const NotificationSchema = require("../../Database/Schemas/Notification/NotificationSchema")

const { VALUES } = require("../../Configs/constants")

module.exports = class {

    addNotifications = (body, session) => {
        const {
            tenantId,
            userRoleIds,
            title,
            secondaryLanguageTitle,
            message,
            secondaryLanguageMessage,
            centerMessage,
            centerSecondaryLanguageMessage,
            type,
            threadId,
            payloadData,
        } = body

        const rawData = userRoleIds.map(userRoleId => {
            return {
                tenant_id: tenantId,
                user_role_id: userRoleId,
                title: title,
                secondary_language_title: secondaryLanguageTitle,
                message: message,
                secondary_language_message: secondaryLanguageMessage,
                center_message: centerMessage,
                center_secondary_language_message: centerSecondaryLanguageMessage,
                type: type,
                thread_id: threadId,
                payload_data: payloadData,
            }
        })

        return this.bulkCreateNotifications(rawData, session)
    }

    bulkCreateNotifications = (docs = []) => {
        if (!docs.length) {
            return
        }

        return NotificationSchema.insertMany(docs)
    }

    async updateNotificationsByFilter(filter, updateObject, options) {
        return NotificationSchema.updateMany(
            this.#getMatchCondition(filter),
            updateObject,
            options
        );
    }

    getUnreadNotificationsCountByUserRoleIds = async (
        userRolesIds,
        session
    ) => {
        const data = await NotificationSchema.aggregate(
            [
                {
                    $match: {
                        user_role_id: {
                            $in: userRolesIds
                        },
                        is_read: false
                    }
                },
                {
                    $group: {
                        _id: "$user_role_id",
                        count: {
                            $sum: 1
                        }
                    }
                }
            ],
            {
                session
            }
        )

        return data.reduce((count, value) => {
            count[value._id] = value.count ?? 0
            return count
        }, {})
    }

    findNotificationsWithPagination = async (filter) => {
        const {
            perPage,
            page,
        } = filter

        const match = this.#getMatchCondition(filter)

        const [list, count] = await Promise.all([
            NotificationSchema.find(
                match,
                undefined,
                {
                    limit: perPage,
                    skip: perPage * (page - 1),
                    sort: { created_at: -1 },
                    lean: true,
                }
            ),
            NotificationSchema.countDocuments(match)
        ])

        return { list, count }
    }

    countNotificationsByFilter = (filter) => {
        return NotificationSchema.countDocuments(
            this.#getMatchCondition(filter)
        )
    }

    #getMatchCondition = (filter) => {
        const {
            tenantId,
            userRoleId,
            unreadOnly
        } = filter

        const match = {
            tenant_id: tenantId,
            user_role_id: userRoleId,
        }

        if (unreadOnly) {
            match.is_read = false
        }

        return match
    }

}
