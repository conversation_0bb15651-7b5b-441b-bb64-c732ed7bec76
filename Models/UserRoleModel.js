const UserRolesSchema = require("../Database/Schemas/user_roles");
const InternalServiceModal = new (require("./InternalServiceModal"))();
const SAPServiceModel = new (require("./SAPServiceModel"))();
const IntegrationCredentialModel = new (require("./IntegrationCredentialModel"))();

const {
    MONGODB,
    ORDER_STATUS_TYPES,
    INTEGRATION_CHANNELS,
} = require("../Configs/constants")

module.exports = class {

    performChangeStream = async (event) => {        
        const {
            updatedFields = {}
        } = event?.updateDescription || {};

        const {
            _id,
            customer_catalog_mode,
            tenant_id,
            external_id
        } = event?.fullDocument || {}

        const updatedCustomerCatalogMode = async () => {
            logger.info(`Catalog mode updated to '${customer_catalog_mode}' for customer role id: ${_id?.toString()} in tenant: ${tenant_id}`);

            const key = customer_catalog_mode ? "customer_catalog_mode_enabled_at" : "customer_catalog_mode_disabled_at"
            await UserRolesSchema.updateOne(
                {
                    _id,
                    tenant_id
                },
                {
                    [key]: new Date()
                }
            )
        }

        const updateCustomerOrderPreApprovedFlag = async () => {
            try {
                const sapIntegrationCredentials = await IntegrationCredentialModel.getCredential(
                    {
                        tenant_id,
                        name: INTEGRATION_CHANNELS.SAP_SERVICE
                    },
                    "configurations is_active",
                    {
                        lean: true
                    }
                )

                if ((sapIntegrationCredentials?.is_active ?? false)) {
                    const pre_approved = await SAPServiceModel.checkPreApproved(
                        external_id,
                        tenant_id,
                        sapIntegrationCredentials
                    )

                    const updateOrderBody = {
                        filter: {
                            customer_user_role_id: _id,
                            tenant_id: tenant_id,
                            order_status: {
                                $in: [
                                    ORDER_STATUS_TYPES.RECEIVED,
                                    ORDER_STATUS_TYPES.RELEASED,
                                    ORDER_STATUS_TYPES.PENDING,
                                    ORDER_STATUS_TYPES.ON_HOLD,
                                ]
                            }
                        },
                        updateFields: {
                            pre_approved,
                            external_id
                        }
                    }

                    await InternalServiceModal.updateOrders(updateOrderBody)
                }
            }
            catch (error) {
                logger.error(error)
            }
        }

        switch (event?.operationType) {
            case MONGODB.OPERATION_TYPE.INSERT: {
                if (typeof customer_catalog_mode !== "undefined") {
                    await updatedCustomerCatalogMode()
                }

                break
            }
            case MONGODB.OPERATION_TYPE.UPDATE: {
                if (typeof updatedFields?.customer_catalog_mode !== "undefined") {
                    await updatedCustomerCatalogMode()
                }

                if (typeof updatedFields?.external_id !== "undefined") {
                    await updateCustomerOrderPreApprovedFlag()
                }

                const customer = {
                    tenant_id,
                    customer_id: _id,
                    customer_name: updatedFields?.customer_name,
                    external_id: updatedFields?.external_id,
                    legal_name: updatedFields?.customer_legal_name,
                }

                if (
                    customer.customer_name ||
                    customer.external_id ||
                    customer.legal_name
                ) {
                    await InternalServiceModal.updateAssignedStandCustomerDetails(customer)
                }

                const salesPerson = {
                    tenant_id,
                    customer_id: _id,
                    sales_person_user_role_id: updatedFields?.sales_person_id
                }

                if (
                    salesPerson.sales_person_user_role_id
                ) {
                    await InternalServiceModal.updateAssignStandSalesPersonUserRoleId(salesPerson)
                }

                break
            }
        }
    }

}
