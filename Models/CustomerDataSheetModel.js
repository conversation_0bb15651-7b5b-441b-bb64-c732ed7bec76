const difference = require("lodash.difference")

const CommonModel = new (require("./common"))()

const {
    FALSELY_VALUES,
    DATA_SHEET,
    REGEX,
    BOOLEAN
} = require("../Configs/constants")

const { removeAllWhitespace } = require("../Utils/helpers")
const { default: Axios } = require("axios")

class CustomerDataSheetModel {
    // Getting a tenantPortalModel instance from the base class to this derived class.
    constructor(modelInstance) {
        this.tenantPortalModel = modelInstance
    }

    checkValidColumns = (operationType, fields, excelTitles, validation) => {
        if (operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
            var validColumns = Object.keys(fields)
            const missingColumns = difference(validColumns, excelTitles)

            if (missingColumns.length) {
                validation.push(`Missing column(s): ${missingColumns.join(" | ")}`)
            }
        }
    }

    checkAddCustomerLimit = (operationType, tenantInfo, activeCustomers, validation) => {
        if (operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
            const tenantAdvanceLimit = tenantInfo.advance_limit.find(data => data.key === "NUMBER_OF_CUSTOMERS")
            const allowCustomers = tenantAdvanceLimit.allowance

            if (allowCustomers <= activeCustomers) {
                validation.push(`Max customer limit (${allowCustomers}) is exceeded`)
            }
        }
    }

    checkValidCustomerId = async (customerInfo, customerRole, validation) => {
        if (customerInfo) {
            if (customerInfo.is_deleted || !customerInfo.customer_id || customerInfo.role_id?.toString() !== customerRole?._id?.toString()) {
                validation.push("Customer not found")
            }
        }
    }

    checkValidCustomerLegalName = async (customer_legal_name, customerInfo, validation) => {
        if (!customer_legal_name) {
            validation.push("Missing customer legal name")
        }
        else {
            if (customerInfo) {
                if (customerInfo.customer_legal_name !== customer_legal_name && customerInfo.is_payment_enabled) {
                    validation.push("Can't update legal name as payment is enabled")
                }
            }
        }
    }

    checkValidExternalId = async (tenantId, external_id, customerInfo, operationType, validation,) => {
        external_id = String(external_id).trim()

        if (FALSELY_VALUES.includes(external_id)) {
            external_id = ""
        }

        const handleExternalId = async () => {
            const existingExternalId = await this.tenantPortalModel.findUserProfileWithFilter(
                {
                    "unique_external_id": `${tenantId}_${external_id}`,
                    "is_deleted": false,
                },
                "_id",
                { lean: true },
            )

            if (existingExternalId) {
                validation.push("External id already exists")
            }
        }

        if (operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
            if (external_id) {
                await handleExternalId()
            }
        }
        else if (
            [
                DATA_SHEET.OPERATION_TYPE.UPDATE,
                DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS
            ].includes(operationType)
        ) {
            if (external_id && customerInfo) {
                if (customerInfo.external_id !== external_id) {
                    if (customerInfo.is_payment_enabled) {
                        validation.push("Can't update external_id as payment is enabled")
                    }
                    else {
                        await handleExternalId()
                    }
                }
            }
        }
    }

    checkValidCustomerEmail = (customer_email, validation) => {
        // Extracting the customer email value if exists, from the formula object
        if (typeof customer_email === "string") {
            customer_email = removeAllWhitespace(customer_email)
        }
        else if (typeof customer_email === "object") {
            if (customer_email.text) {
                customer_email = removeAllWhitespace(customer_email.text)
            }
        }
        const validEmail = customer_email.match(REGEX.EMAIL_VALID)

        if (!validEmail) {
            validation.push("Provided email is invalid")
        }
    }

    checkValidSalesPerson = (excelData, salesPersons, validation) => {
        const arraySalesPersons = []

        for (let index = 0; index < salesPersons.length; index++) {
            const salesPerson = {
                "salesPersonId": salesPersons[index]._id,
                "name":
                    salesPersons[index].user_id.first_name +
                    " " +
                    salesPersons[index].user_id.last_name,

                "mobileNumber": salesPersons[index].user_id.mobile_number,
                "email": salesPersons[index].user_id.email,
                "needToAppendMobileNumber": false,
            }
            arraySalesPersons.push(salesPerson)
        }

        for (let index = 0; index < arraySalesPersons.length; index++) {
            const newArray = arraySalesPersons.filter(item =>
                item.name === arraySalesPersons[index].name &&
                (item.mobileNumber != arraySalesPersons[index].mobileNumber)
            )

            if (newArray.length > 0) {
                arraySalesPersons[index].needToAppendMobileNumber = true
            }

            if (arraySalesPersons[index].needToAppendMobileNumber) {
                arraySalesPersons[index]["salespersonName"] =
                    arraySalesPersons[index].name +
                    " " +
                    `(${arraySalesPersons[index].mobileNumber})`
            }
            else {
                arraySalesPersons[index]["salespersonName"] = arraySalesPersons[index].name
            }
        }

        const assignSalesPerson = arraySalesPersons.find(item => item.salespersonName == excelData.sales_person)

        if (assignSalesPerson) {
            excelData.sales_person = assignSalesPerson.salesPersonId
        }
        else {
            const salesPersonInfo = excelData.sales_person?.split(" ")
            const assignSalesPerson = salesPersonInfo.slice(0, -1).join(" ")

            const assignSalesPersonNumber = excelData.sales_person?.substring(
                excelData.sales_person.indexOf("(") + 1,
                excelData.sales_person.lastIndexOf(")")
            )

            const assignSalesPersonName = arraySalesPersons.find(item =>
                item.salespersonName == assignSalesPerson &&
                item.mobileNumber == assignSalesPersonNumber
            )

            if (assignSalesPersonName) {
                excelData.sales_person = assignSalesPersonName.salesPersonId
            }
            else {
                validation.push("Sales person is inactive/not found")
            }
        }
    }

    checkValidPriceList = (excelData, tenantMasterPriceList, validation) => {
        const assignPrice = tenantMasterPriceList.find(item => item.price_name == excelData.price_list)

        if (assignPrice) {
            excelData.price_list = assignPrice._id
        }
        else {
            validation.push("Price is inactive/not found")
        }
    }

    checkValidIsActive(excelData, validation) {
        const active = String(excelData.is_active)?.toUpperCase()
        if (
            !active ||
            !Object.values(BOOLEAN).includes(active)
        ) {
            validation.push("Please provide 'Active' value (case insensitive) 'TRUE' or 'FALSE'")
        }
    }

    checkValidCatalogMode(excelData, validation) {
        const active = String(excelData.catalog_mode)?.toUpperCase()
        if (
            !active ||
            !Object.values(BOOLEAN).includes(active)
        ) {
            validation.push("Please provide 'Catalog Mode' value (case insensitive) 'TRUE' or 'FALSE'")
        }
    }

    checkValidCustomerAppAccess(excelData, validation) {
        const active = String(excelData.customer_app_access)?.toUpperCase()
        if (
            !active ||
            !Object.values(BOOLEAN).includes(active)
        ) {
            validation.push("Please provide 'Customer App Access' value (case insensitive) 'TRUE' or 'FALSE'")
        }
    }

    checkValidPreferredLanguage = (excelData, country, tenantAppSetting, validation) => {
        if (excelData.preferred_language === "English") {
            excelData.preferred_language = "en"
        }
        else if (excelData.preferred_language === country.secondary_language_name) {
            excelData.preferred_language = country.secondary_language_code
        }
        else if (!excelData.preferred_language) {
            excelData.preferred_language = tenantAppSetting.preferred_language
        }
        else {
            validation.push("Preferred language is inactive/not found")
        }
    }

    checkValidShippingRegion = async (excelData, country, region, validation) => {
        if (
            region &&
            (region.country_id.equals(country._id)) &&
            region.is_active &&
            !region.is_deleted
        ) {
            excelData.shipping_region = region._id
        }
        else {
            validation.push("Shipping region is inactive/not found")
        }
    }

    checkValidShippingCity = async (excelData, country, region, city, validation) => {
        if (!region) {
            if ("shipping_region" in excelData) {
                if (!excelData.shipping_region) {
                    if (
                        city &&
                        (city.country_id.equals(country._id)) &&
                        city.is_active &&
                        !city.is_deleted
                    ) {
                        validation.push("Can't update shipping city as shipping region is empty")
                    }
                    else {
                        validation.push("Shipping city is inactive/not found")
                    }
                    return
                }
            }
            else {
                const userRoleInfo = await this.tenantPortalModel.findCustomerWithCustomerId(excelData?.customer_id)

                region = await CommonModel.getRegionById(userRoleInfo?.shipping_region_id)

                if (!(
                    region &&
                    (region.country_id.equals(country._id)) &&
                    region.is_active &&
                    !region.is_deleted)
                ) {
                    if (
                        city &&
                        (city.country_id.equals(country._id)) &&
                        city.is_active &&
                        !city.is_deleted
                    ) {
                        validation.push("Can't update shipping city as shipping region is inactive/not found")
                    }
                    else {
                        validation.push("Shipping city is inactive/not found")
                    }
                    return
                }
            }
        }

        if (
            city &&
            (city.country_id.equals(country._id)) &&
            city.is_active &&
            !city.is_deleted
        ) {
            if (region) {
                if (city.region_id.equals(region._id)) {
                    excelData.shipping_city = city._id
                }
                else {
                    validation.push("Shipping Region & City are mismatched")
                }
            }
            else {
                validation.push("Can't update shipping city as shipping region is inactive/not found")
            }
        }
        else {
            validation.push("Shipping city is inactive/not found")
        }
    }

    checkValidation = async (req, excelData, restData) => {
        try {
            var validation = []
            let { tenantId } = req.body
            tenantId = +tenantId

            const {
                tenantInfo,
                salesPersons,
                activeCustomers,
                tenantAppSetting,
                tenantMasterPriceList,
                customerRole,
                country,
                operationType,
                fields,
                excelTitles,
            } = restData || {}

            this.checkValidColumns(operationType, fields, excelTitles, validation)

            this.checkAddCustomerLimit(operationType, tenantInfo, activeCustomers, validation)

            let customerInfo
            if (
                [
                    DATA_SHEET.OPERATION_TYPE.UPDATE,
                    DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS
                ].includes(operationType)
            ) {
                if (excelData.customer_id) {
                    customerInfo = await this.tenantPortalModel.findCustomerWithCustomerId(excelData.customer_id)
                    if (!customerInfo) {
                        validation.push("Customer not found")
                    }
                }
                else {
                    validation.push("Missing customer id")
                }
            }

            await this.checkValidCustomerId(customerInfo, customerRole, validation)

            if ("external_id" in excelData) {
                await this.checkValidExternalId(
                    tenantId,
                    excelData.external_id,
                    customerInfo,
                    operationType,
                    validation,
                )
            }

            if ("customer_name" in excelData && !excelData.customer_name) {
                validation.push("Missing customer name")
            }

            if ("customer_legal_name" in excelData) {
                await this.checkValidCustomerLegalName(
                    excelData.customer_legal_name,
                    customerInfo,
                    validation
                )
            }

            if ("customer_first_name" in excelData && !excelData.customer_first_name) {
                validation.push("Missing customer first name")
            }

            if ("customer_mobile_number" in excelData && !excelData.customer_mobile_number) {
                validation.push("Missing customer mobile number")
            }

            if ("customer_email" in excelData && excelData.customer_email) {
                this.checkValidCustomerEmail(excelData.customer_email, validation)
            }

            if ("sales_person" in excelData) {
                this.checkValidSalesPerson(excelData, salesPersons, validation)
            }

            if ("price_list" in excelData) {
                this.checkValidPriceList(excelData, tenantMasterPriceList, validation)
            }

            if ("preferred_language" in excelData) {
                this.checkValidPreferredLanguage(excelData, country, tenantAppSetting, validation)
            }

            if ("is_active" in excelData) {
                this.checkValidIsActive(excelData, validation)
            }

            if ("catalog_mode" in excelData) {
                this.checkValidCatalogMode(excelData, validation)
            }

            if ("customer_app_access" in excelData) {
                this.checkValidCustomerAppAccess(excelData, validation)
            }

            delete excelData.shipping_region; // clear this fields, for update case
            delete excelData.shipping_city; // clear this fields, for update case
            delete excelData.shipping_address; // clear this fields, for update case

            const deleteShippingAddParams = ['', 0, undefined, null];
            if (("gps_coordinates_latitude" in excelData && "gps_coordinates_longitude" in excelData)
                && (deleteShippingAddParams.includes(excelData.gps_coordinates_latitude) && deleteShippingAddParams.includes(excelData.gps_coordinates_longitude))) { // to remove the shipping details from customer
                excelData.shipping_region = undefined;
                excelData.shipping_city = undefined;
                excelData.shipping_address = undefined;
            } else if (
                (("gps_coordinates_latitude" in excelData) && ("gps_coordinates_longitude" in excelData))
                && (!isNaN(excelData.gps_coordinates_latitude) && !isNaN(excelData.gps_coordinates_longitude)) // invalid coordinate numbers found

            ) {
                const {
                    shippingAddress,
                    countryName,
                    regionName,
                    cityName
                } = await this.getShippingDetails(excelData);
                if (!countryName || country.name !== countryName) {
                    validation.push('Invalid country found from coordinates');
                }

                excelData.shipping_region = regionName
                excelData.shipping_city = cityName;
                excelData.shipping_address = shippingAddress;
                if ("shipping_region" in excelData || "shipping_city" in excelData) {
                    // const country = await CommonModel.findCountryById(tenantInfo.country) // 
                    let region
                    let city

                    if (excelData.shipping_region) {
                        region = await CommonModel.findRegionByName(excelData.shipping_region)
                    }

                    if (excelData.shipping_city) {
                        city = await CommonModel.findCityByName(excelData.shipping_city)
                    }

                    if (
                        "shipping_region" in excelData
                    ) {
                        this.checkValidShippingRegion(excelData, country, region, validation)
                    }

                    if (
                        "shipping_city" in excelData
                    ) {
                        await this.checkValidShippingCity(excelData, country, region, city, validation)
                    }
                }
            } else if (
                (!("gps_coordinates_latitude" in excelData) && ("gps_coordinates_longitude" in excelData))
            ) {
                validation.push('latitude is missing');
            } else if (("gps_coordinates_latitude" in excelData) && !("gps_coordinates_longitude" in excelData)) {
                validation.push('longitude is missing');
            } else if (
                ("gps_coordinates_latitude" in excelData) && ("gps_coordinates_longitude" in excelData)
                && (isNaN(excelData.gps_coordinates_latitude) || isNaN(excelData.gps_coordinates_longitude))
            ) {
                validation.push('invalid coordinate numbers found');
            }
        } catch (error) {
            validation.push(error.message)
        }
        return validation
    }

    async getShippingDetails(excelData) {
        const address = {
            shippingAddress: "",
            regionName: "",
            cityName: "",
            countryName: ""
        };

        const response = await Axios.get(`https://maps.googleapis.com/maps/api/geocode/json?latlng=${Number(excelData.gps_coordinates_latitude)},${Number(excelData.gps_coordinates_longitude)}&key=${process.env.MAP_STATIC_API_KEY}`);
        const result = response.data.results[0];
        address.shippingAddress = result.formatted_address;
        for (var i = 0; i < result.address_components.length; i++) {
            if (result.address_components[i].types[0] === 'locality') {
                //this is the object you are looking for City
                address.cityName = result.address_components[i].long_name;
            }
            if (result.address_components[i].types[0] === 'administrative_area_level_1') {
                //this is the object you are looking for State
                address.regionName = result.address_components[i].long_name;
            }
            if (result.address_components[i].types[0] === 'country') {
                //this is the object you are looking for
                address.countryName = result.address_components[i].long_name;
            }
        }
        return address;
    }

    updateCustomer = async (userRoleInfo, excelData, region, city, tenantInfo) => {
        const {
            sales_person,
            customer_app_access,
            preferred_language,
            external_id,
            customer_name,
            customer_legal_name,
            shipping_address,
            shipping_mobile_number,
            price_list,
            catalog_mode,
            customer_email,
            customer_first_name,
            customer_last_name,
            gps_coordinates_longitude,
            gps_coordinates_latitude,
            is_active
        } = excelData

        if ("external_id" in excelData) {
            userRoleInfo["external_id"] = external_id

            if (external_id) {
                userRoleInfo["unique_external_id"] = `${tenantInfo._id}_${external_id}`
            }
            else {
                userRoleInfo["unique_external_id"] = null
            }
        }

        if ("customer_name" in excelData) {
            userRoleInfo["customer_name"] = customer_name
        }

        if ("customer_legal_name" in excelData) {
            userRoleInfo["customer_legal_name"] = customer_legal_name
        }

        if ("sales_person" in excelData) {
            userRoleInfo["sales_person_id"] = sales_person
        }

        if ("price_list" in excelData) {
            userRoleInfo["price_list_id"] = price_list
        }

        if ("customer_first_name" in excelData) {
            userRoleInfo["customer_first_name"] = customer_first_name
        }

        if ("customer_last_name" in excelData) {
            userRoleInfo["customer_last_name"] = customer_last_name
        }

        if ("customer_email" in excelData) {
            userRoleInfo["customer_email"] = customer_email
        }

        if ("customer_app_access" in excelData) {
            userRoleInfo["customer_app_access"] =
                customer_app_access === true
                    ? true
                    : false
        }

        if ("catalog_mode" in excelData) {
            userRoleInfo["customer_catalog_mode"] =
                catalog_mode === true
                    ? true
                    : false
        }

        if ("preferred_language" in excelData) {
            userRoleInfo["preferred_language"] = preferred_language
        }

        if ("shipping_address" in excelData) {
            userRoleInfo["shipping_address"] = shipping_address
        }

        if ("shipping_region" in excelData) {
            userRoleInfo["shipping_region_id"] = region?._id || null
        }

        if ("shipping_city" in excelData) {
            userRoleInfo["shipping_city_id"] = city?._id || null
        }

        if ("shipping_mobile_number" in excelData) {
            userRoleInfo["shipping_country_code"] =
                shipping_mobile_number === ""
                    ? ""
                    : tenantInfo.country_code

            userRoleInfo["shipping_mobile_number"] = shipping_mobile_number
        }

        if ("is_active" in excelData) {
            userRoleInfo["is_active"] =
                is_active === true
                    ? true
                    : false
        }

        if (
            "gps_coordinates_longitude" in excelData ||
            "gps_coordinates_latitude" in excelData
        ) {
            let longitude = userRoleInfo.gps_coordinates.longitude
            let latitude = userRoleInfo.gps_coordinates.latitude

            if ("gps_coordinates_longitude" in excelData) {
                longitude = gps_coordinates_longitude || 0
            }

            if ("gps_coordinates_latitude" in excelData) {
                latitude = gps_coordinates_latitude || 0
            }
            userRoleInfo["gps_coordinates"] = {
                longitude,
                latitude
            }
        }

        await userRoleInfo.save()
    }
}

module.exports = CustomerDataSheetModel
