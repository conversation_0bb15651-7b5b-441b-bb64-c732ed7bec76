const { ADD_TENANT_SCREENS, VALUES, ENTITY_STATUS } = require('../Configs/constants')
const { master_notification_types: MasterNotificationType, tenant_services: TenantServiceSchema, 
    master_tenant_advance: MasterTenantAdvance, tenants: TenantSchema, tenant_branch: TenantBranchSchema, 
    tenant_notification_configurations: TenantNotificationConfSchema, roles: RoleSchema, user_roles: UserRoleSchema,
    user_sessions: UserSessionSchema, users: UserSchema, warehouses: WarehouseSchema } = require('../Database/Schemas');
const { httpService, httpSalesPersonService } = require('../Utils/helpers');

const TenantPortalModal = new (require("../Models/tenantPortal"))();
const RoleModal = new (require("../Models/roles"));
class SystemPortalModel {

    getTenantDefaultNotificationType = async (filter = {}, projection = {}, options = {}) => {
        return MasterNotificationType.find(filter, projection, options);
    }

    async getTenantDefaultServices(filter = {}) {
        return TenantServiceSchema.find(filter);
    }

    async getTenantDefaultAdvanceLimits(filter = {}) {
        return MasterTenantAdvance.find(filter);
    }
    async createNewTenant(body, headers) {
        // const today = moment();
        // const tenantStatus = (moment(body.advanced?.subscription?.from) < today) && (moment(body.advanced?.subscription?.to) > today)
        return new TenantSchema({
            name: body.details.tenantName,
            legal_name: body.details?.legalName,
            street_name: body.details?.streetName,
            country: body.details?.country,
            timezone: body.details?.timezone,
            region: body.details?.region,
            city: body.details?.city,
            country_code: body.details?.countryCode,
            primary_contact_id: body.details?.primaryContactId,
            is_deleted: false,
            is_active: body.isActive || false,
            services: body.services.serviceInfo,
            enable_sms_services: body.services?.enableSMSServices || false,
            has_custom_sender_id: body.services?.hasCustomSenderId || false,
            last_branch_id: 0,
            
            advance_limit: body.advanced?.limits,
            subscription_start_date: body.advanced?.subscription?.from,
            subscription_end_date: body.advanced?.subscription?.to,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            linked_tenant_id: body.advanced?.linkedTenants || []
        })
    }

    async defaultTaxMaster(tenantId, request) {
        const data = {
            tenantId,
            // headers: request.headers
        }
        const authResponse = await httpService(request).post("configuration/tax", data)
        const details = authResponse?.["data"]?.["data"]
        return details;
    }

    async defaultVisitConfiguration(tenantId, request) {
        const data = {
            tenantId,
            // headers: request.headers
        }
        const authResponse = await httpSalesPersonService(request).post("defaultVisitConfiguration", data)
        const details = authResponse?.["data"]?.["data"]
        return details;
    }


    async addTenantBranches(tenantDetails, headers, branches) {
        branches.forEach(b => {
            b.tenant_id = tenantDetails._id;
            b.created_by = headers.userDetails._id;
            b.updated_by = headers.userDetails._id;
            b.is_deleted = false;
            b.is_active = true;
            b.warehouse_counter = 1;
        })
        return TenantBranchSchema.insertMany(branches);
    }

    async addInitWareHouses(tenant_id, branchIds, headers) {
        const warehouses = [];
        branchIds.forEach(branch_id => {
            warehouses.push({
                tenant_id,
                branch_id,
                is_active: true,
                is_deleted: false,
                warehouse_code: "WH01",
                created_by: headers.userDetails._id,
                updated_by: headers.userDetails._id
            })
        });

        return WarehouseSchema.insertMany(warehouses);
    }

    async addWareHouse(tenant_id, branch_id, warehouse_code, headers) {
        return WarehouseSchema({
            tenant_id,
            branch_id,
            is_active: true,
            is_deleted: false,
            warehouse_code,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
        })
    }

    async setTenantNotificationConfig(tenantDetails, headers, tenantNotificationConfigArr, ) {
        return new TenantNotificationConfSchema({
            tenant_id: tenantDetails._id,
            notifications: tenantNotificationConfigArr,
            is_default: true,
            is_deleted: false,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
        })
    }

    // async getTenantNotificationSettings(tenant_id) {
    //     return UserRoleSchema.findOne({ tenant_id }, null, { notifications: 1 });
    // }

    async getTenantOwnerRole() {
        return RoleSchema.findOne({ name: "Tenant Owner", portal_type: VALUES.portals.TENANT_PORTAL })
    }

    async getTenantOwnerProfile(tenant_id, tenantOwnerRoleId) {
        return UserRoleSchema.findOne({ tenant_id, role_id: tenantOwnerRoleId });
    }

    async updateTenantUserRolesStatusByTenantId(tenant_id, is_active) {
        return UserRoleSchema.updateMany({ tenant_id }, { is_active })
    }

    assignTenantOwnerRole = async (
        user_id,
        role_id,
        tenant_id,
        roleStatus = false,
        notifications,
        headers
    ) => {
        let masterNotifications = notifications || []

        if (!masterNotifications.length) {
            masterNotifications = await this.getTenantDefaultNotificationType(
                {},
                {
                    key: 1,
                    default_value: 1,
                },
                {
                    lean: true
                }
            )

            masterNotifications.forEach(notification => {
                notification.notification_type = notification.key

                Object.keys(notification.default_value).forEach(key => {
                    notification[key] = true
                })
                delete notification.key
            })
        }

        return UserRoleSchema({
            user_id,
            is_active: roleStatus,
            is_deleted: false,
            tenant_id,
            role_id,
            allow_price_change: true,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            notifications: masterNotifications,
            allow_all_permission: true,
            collection_name: "users"
        })
    }

    async getTenantDetailsById(_id) {
        return TenantSchema.findById(_id)
        .populate({ path: "primary_contact_id", select: "_id first_name last_name email country_code mobile_number is_active is_deleted" })
        .populate("country").populate("city").populate("region")
        .populate({ path: 'linked_tenant_id', select: '_id name legal_name street_name primary_contact_id is_active country region city' });
    }

    async getTenantWithFilter(filter, projection, options) {
        return TenantSchema.findOne(filter, projection, options);
    }

    async getTenantsById(_id, projection = "", options = {}, populateArray) {
        let query = TenantSchema.findById(_id, projection, options)

        if (populateArray) {
            query = query.populate(populateArray)
        }
        return query
    }

	async getTenantById(_id, projection = {}, options = {}) {
        return TenantSchema.findById(_id, projection, options);
    }

    async getTenants(filter = {}, projection = {}, options = {}) {
        return TenantSchema.find(filter, projection, options)
    }

    async getTenantWithOwner(tenant_id) {
        return TenantSchema.findById(tenant_id).populate("primary_contact_id");
    }

    async getTenantUsersById(tenant_id) {
        return UserRoleSchema.find({ tenant_id });
    }

    async expireTenantUserSessions(tenantUsersRole) {
        return UserSessionSchema.updateMany({user_role_id: { $in: tenantUsersRole }, status: VALUES.sessionStatus.ACTIVE}, { status: VALUES.sessionStatus.CLOSE, end_time: new Date(), });
    }

    async expireSessionsByUserRoleId(user_role_id) {
        return UserSessionSchema.updateMany({ user_role_id, status: VALUES.sessionStatus.ACTIVE }, { status: VALUES.sessionStatus.CLOSE, end_time: new Date(), })
    }

    async expireSessionByUserId(user_id) {
        return UserSessionSchema.updateMany({ user_id, status: VALUES.sessionStatus.ACTIVE }, { status: VALUES.sessionStatus.CLOSE, end_time: new Date(), })
    }

    async deleteTenantProfiles(tenant_id) {
        return UserRoleSchema.updateMany({ tenant_id }, { is_deleted: true });
    }

    async deleteSystemPortalProfiles(user_id) {
        if (!user_id) {
            return
        }
        let systemPortalRoles = await RoleModal.getSystemPortalRoles();
        systemPortalRoles = systemPortalRoles.map(spr => spr._id);
        return UserRoleSchema.updateMany({ user_id, role_id: { $in: systemPortalRoles } }, { is_deleted: true });
    }

    async deleteTenantBranches(tenant_id) {
        return TenantBranchSchema.updateMany({ tenant_id }, { is_deleted: true });
    }

    async AddTenantBranch(body, headers, tenant) {
        return new TenantBranchSchema({
            name: body.name,
            branch_id: tenant.last_branch_id < 9 ? `B0${tenant.last_branch_id}` : `B${tenant.last_branch_id}`,
            tenant_id: tenant._id,
            created_by : headers.userDetails._id,
            updated_by : headers.userDetails._id,
            is_deleted : false,
            is_active : true,
            is_default: false,
        });
    }

    async getBranchById(body, headers) {
        return TenantBranchSchema.findById(body.branchId);
    }

    async expireBranchUserSession(userRoleIds) {

       return UserSessionSchema.updateMany({user_role_id: {$in: userRoleIds}, status: VALUES.sessionStatus.ACTIVE }, { status: VALUES.sessionStatus.CLOSE, end_time: new Date() })
    }

    async getBranchUserRole(branch_id) {
        return UserRoleSchema.find({ branch_id });
    }

    async markDeleteBranchUserRoles(branch_id) {
        return UserRoleSchema.updateMany({ branch_id }, { is_deleted: true });
    }

    async getTenantOwnerNotifications(tenant_id, role_id) {
        return UserRoleSchema.findOne({ tenant_id, role_id }, { notifications: 1 });
    }

    async getTenantBranchesByTenantId(tenant_id) {
        return TenantBranchSchema.find({ tenant_id, is_active: true, is_deleted: false })
    }

    async getTenantBranches(filter, projection, options) {
        return TenantBranchSchema.find(filter, projection, options)
    }

    async getBranchesWithWareHouse(tenant_id) {
        const pipeline = [];
        pipeline.push(
        {
            $match: {
                is_deleted: false,
                is_active: true,
                tenant_id: Number(tenant_id)
            },
        },
        {
            $lookup: {
                from: "warehouses",
                localField: "_id",
                foreignField: "branch_id",
                as: "warehouses",
                pipeline: [
                    {
                        $project: {
                            "_id": 1,
                            "warehouse_code": 1
                        }
                    }
                ]
            }
        },
        {
            $project: {
                _id: 1,
                warehouses: 1,
                name: 1,
                is_active: 1,
                branch_id: 1,
                is_default: 1,
                tenant_id: 1
            }
        }
        )
        return TenantBranchSchema.aggregate(pipeline)
    }

    async addLinkedTenant(tenant_id, linked_tenant_id) {
        return TenantSchema.updateOne({ _id: tenant_id }, { $addToSet: { linked_tenant_id : linked_tenant_id} })
    }

    async removeLinedTenant(tenant_id, linked_tenant_id) {
        return TenantSchema.updateOne({ _id: tenant_id }, { $pull: { linked_tenant_id : linked_tenant_id} })
    }

    //TODO: need to change logic for account manager role's tenant listing API
    async getTenantsList(searchKey, offset, perPage, sessionDetails) {
        const $match = {
            is_deleted: false
        }

        const aggregateArray = [
            {
                $match,
            },
            {
                $sort: {
                    _id: -1,
                }
            },
            {
                $lookup: {
                    from: "users",
                    localField: "primary_contact_id",
                    foreignField: "_id",
                    as: "primary_contact"
                },
            },
            {
                $addFields: {
                    primary_contact: { $first: "$primary_contact" },
                },
            },
        ]

        if (searchKey) {
            const query = [
                { _idString: { $regex: new RegExp(searchKey) } },
                { name: { $regex: new RegExp(searchKey, 'i') } },
                { "primary_contact.first_name": { $regex: new RegExp(searchKey, 'i') } },
                { "primary_contact.last_name": { $regex: new RegExp(searchKey, 'i') } },
                { mobileNumberStr: { $regex: new RegExp(`.*${searchKey}.*`) } }
            ];

            aggregateArray.push(
                {
                    $addFields: {
                        _idString: {
                            $toString: { $toLong: "$_id" }
                        },
                        mobileNumberStr: {
                            $toString: { $toLong: "$primary_contact.mobile_number" }
                        }
                    }
                },
                {
                    $match: { $or: query },
                },
            )
        }

        aggregateArray.push(
            {
                $project: {
                    _id: 1,
                    name: 1,
                    is_active: 1,
                    primary_contact: {
                        _id: 1,
                        first_name: 1,
                        last_name: 1,
                        mobile_number: 1,
                        country_code: 1
                    },
                }
            },
            {
                $facet: {
                    tenants: [{ $skip: offset }, { $limit: perPage }],
                    totalCount: [
                        {
                            $count: 'count'
                        }
                    ]
                }
            },
            {
                $unwind: "$totalCount"
            }
        )

        //TODO: need to check for the session details changes
        // switch (sessionDetails?.user_role_id?.role_id?.name.toLowerCase()) {
        switch (sessionDetails?.role_id?.name.toLowerCase()) {
            case "super admin":
            case "system owner":
                break;

            default:
                if (sessionDetails?.assigned_tenants?.length) {
                    $match["_id"] = {
                        $in: sessionDetails.assigned_tenants
                    }
                }
                break;
        }

        return TenantSchema.aggregate(aggregateArray);
    }

    async searchTenants(searchKey = "") {
        if(!searchKey) {
            return TenantSchema.find({ is_active: true, is_deleted: false }, { name: 1, _id: 1 }, {limit: 5})
        } else {
            const query = [
                { _idString: { $regex: new RegExp(searchKey) } },
                { name : { $regex : new RegExp(searchKey, 'i') } },
            ];
            return TenantSchema.aggregate([
                {
                    $match: {
                        is_active: true,
                        is_deleted: false,
                    }
                },
                {
                    $addFields: {
                        _idString: {
                            $toString: { $toLong: "$_id" }
                        }
                    },
                },
                {
                    $match: { $or: query },
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                    }
                }
            ])
        }
    }

	async getSystemUsers(query, defaultRoles) {
		let { perPage = 10, page = 1, searchKey = "", roleId = "", status = "" } = query;
		page = parseInt(page);
		perPage = parseInt(perPage);
		const offset = perPage * (page - 1);
		const pipeline = [];

        const roles = roleId !== '' ? [new mongoose.Types.ObjectId(roleId)] : defaultRoles;

        const $match = {
            role_id: {
                $in: roles
            },
            // list only valid profiles of the user
            is_deleted: false
        }

        if (status) {
            $match["is_active"] = status === ENTITY_STATUS.ACTIVE ? true : false
        }

		pipeline.push(
			{
				$match
			}, {
				$lookup: {
					from: 'users', 
					localField: 'user_id', 
					foreignField: '_id', 
					as: 'user',
					pipeline: [{
						$match: {
							is_deleted: false
						}
					}]
				}
			},
			{
				$addFields: {
					user: { $first: "$user" }
				},
			}, {
				$lookup: {
					from: 'roles', 
					localField: 'role_id', 
					foreignField: '_id', 
					as: 'role'
				}
			}, {
				$addFields: {
					role: { $first: "$role" },
					mobileNumberStr: {
                        $toString: { $toLong: "$user.mobile_number" }
                    }
				},
			},
        );

        if (searchKey) {
            pipeline.push({
                $match: {
					$or: [
						{ "user.first_name": new RegExp(searchKey, "i") },
						{ "user.last_name": new RegExp(searchKey, "i") },
						{ "user.email": new RegExp(searchKey, "i") },
						{ "mobileNumberStr": { $regex: new RegExp(`.*${searchKey}.*`) }}
					]
				}
            })
        }

		pipeline.push(
			{
				$project: {
					_id: 1,
					role_id: 1,
					is_deleted: 1,
                    is_active: 1,
					// full_name: { $concat: [ "$user.first_name", " ", "$user.last_name" ] },
					user_id: '$user._id',
					first_name: '$user.first_name',
					last_name: '$user.last_name',
					email: '$user.email',
					mobile_number: '$user.mobile_number',
					is_user_active: '$user.is_active',
					user_role: '$role.name',
                    country_code: '$user.country_code'
				}
			},
			{
				$facet:{
					list: [{ $skip: offset}, {$limit: perPage}],
					count: [{ $count: "count" }]
				}
			},
			{
				$unwind: "$count"
			}
		);
		
		const data = await UserRoleSchema.aggregate(pipeline);

		const list = data.length ? data[0].list : [];
		const count = data.length ? data[0].count.count : 0;
		return { list, count };

	}

    async getUserById(user_id, projection = {}, options = {}) {
        return UserSchema.findById(user_id, projection, options)
    }

    async systemPortalRoleInfo(user_id, roles, projection = { role_id: 1, _id: 1, user_id: 1, assigned_tenants: 1, profile_pic: 1 }) {
        //TODO: need to check weather we need to check for deleted user role or not
        return UserRoleSchema.findOne({ user_id, role_id: { $in: roles }}, projection ).populate({ path: 'role_id', select: 'name is_active is_deleted'});
    }

    async getUserRoleWithRoleInfo(user_role_id) {
        return UserRoleSchema.findById(user_role_id).populate({path: 'role_id'})
    }

    async addAccountManagerUserRole(user_id, role_id, assigned_tenants, isActive, headers) {
        return UserRoleSchema({
            user_id,
            role_id,
            assigned_tenants,
            is_active: isActive,
            is_deleted: false,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            collection_name : "users",
        })
    }

    async addSuperAdminUserRole(user_id, role_id, isActive, headers) {
        return UserRoleSchema({
            user_id,
            role_id,
            is_active: isActive,
            is_deleted: false,
            allow_all_permission: true,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            collection_name : "users",
        })
    }

    async markSessionAsRoleChanged(user_role_id ) {
        return UserSessionSchema.updateMany({user_role_id, status: VALUES.sessionStatus.ACTIVE}, { has_role_changed: true })
    }

    async deleteAllUserProfiles(user_id, headers) {
        return UserRoleSchema.updateMany({ user_id }, { is_deleted: true, updated_by: headers.userDetails._id });
    }

    async recoverSystemPortalUserProfile(user_id, roles, body, headers) {

        let userRole = await UserRoleSchema.findOne({ user_id, role_id: { $in: roles } });
        if(!userRole) {
            userRole = UserRoleSchema({
                is_deleted: false,
                is_active: body.isActive || true,
                role_id: body.roleId,
                user_id,
                created_by: headers.userDetails._id,
                updated_by: headers.userDetails._id,
                assigned_tenants: body.assignedTenants || undefined
            });
        } else {
            userRole.is_deleted = false,
            userRole.role_id = body.roleId,
            userRole.updated_by = headers.userDetails._id,
            userRole.is_active = body.isActive || true
            userRole.assigned_tenants = body.assignedTenants || undefined
        }
        userRole.collection_name = "users";

        return userRole;
    }

    async closeAllActiveUserSessions(user_id) {
        return UserSessionSchema.updateMany({ user_id, status: VALUES.sessionStatus.ACTIVE }, { status: VALUES.sessionStatus.CLOSE, end_time: new Date() });
    }

    async removeAssignedTenant(user_role_id, tenantId) {
        return UserRoleSchema.updateOne({ _id: user_role_id }, { $pull: { assigned_tenants: tenantId } })
    }
    
    async addAssignedTenant(user_role_id, tenantId) {
        return UserRoleSchema.updateOne({_id: user_role_id}, { $addToSet: { assigned_tenants : tenantId} });
    }

    async getAssignedTenantsWithTenantInfo(body, assigned_tenants) {
        let { page = 1, perPage = 10, searchKey = "" } = body;
        page = parseInt(page);
        perPage = parseInt(perPage);
        const offset = perPage * (page - 1);

        const pipeline = [
            {
                $match: {
                    _id: { $in: assigned_tenants },
                    is_deleted: false
                }
            },
        ]

        if (searchKey) {
            pipeline.push(
                {
                    $addFields: {
                        _idString: {
                            $toString: { $toLong: "$_id" }
                        }
                    }
                },
                {
                    $match: {
                        $or: [
                            { _idString: { $regex: new RegExp(searchKey) } },
                            { name: { $regex: new RegExp(searchKey, 'i') } },
                        ]
                    }
                }
            )
        }

        pipeline.push(
            {
                $project: {
                    _id: 1,
                    name: 1,
                    is_active: 1,
                }
            },
            {
                $facet: {
                    list: [{ $skip: offset }, { $limit: perPage }],
                    count: [{ $count: "count" }]
                }
            },
            {
                $unwind: "$count"
            }
        )
        const data = await TenantSchema.aggregate(pipeline);

        const list = data.length ? data[0].list : [];
        const count = data.length ? data[0].count.count : 0;
        return { list, count };
    }

    async updateSystemUserRoleStatus(users, roles, status = ENTITY_STATUS.ACTIVE, headers) {
        return UserRoleSchema.updateMany({ user_id: {$in: users}, role_id: {$in: roles} }, 
            { 
                is_active: status === ENTITY_STATUS.ACTIVE ? true : false ,updated_by: headers.userDetails._id 
            });
    }

    async updateSystemUserStatus(users, status = ENTITY_STATUS.ACTIVE, headers) {
        return UserSchema.updateMany({ _id: {$in: users} }, 
            {
                is_active: status === ENTITY_STATUS.ACTIVE ? true : false ,updated_by: headers.userDetails._id 
            });
    }

    async tenantCount() {
        return TenantSchema.countDocuments({ is_deleted: false, is_active: true })
    }

    async userCount(tenantId) {
        if (tenantId) {
            return UserRoleSchema.countDocuments({ tenant_id: tenantId, collection_name: "users", is_deleted: false, is_active: true })
        }
        return UserSchema.countDocuments({ is_deleted: false, is_active: true })
    }

    async customerCount(tenantId) {
        if (tenantId) {
            return UserRoleSchema.countDocuments({ tenant_id: tenantId, collection_name: "tenant_customers", is_deleted: false, is_active: true })
        }
        const customerRole = await TenantPortalModal.getCustomerRole();
        return UserRoleSchema.countDocuments({ role_id: customerRole._id, is_deleted: false, is_active: true })
    }

    async productCount(req) {
        if (req.query.tenantId) {
            const url = VALUES.internalServiceBaseURL + "dashboard/productCount?tenantId=" + req.query.tenantId
            const authResponse = await httpService(req).get(url)
            const details = authResponse?.["data"]?.["data"]
            return details
        }
        const url = VALUES.internalServiceBaseURL + "dashboard/productCount"
        const authResponse = await httpService(req).get(url)
        const details = authResponse?.["data"]?.["data"]
        return details
    }

    async orderCount(req) {
        const url = "dashboard/orderCount"
        const authResponse = await httpService(req).get(url)
        const details = authResponse?.["data"]?.["data"]

        return details
    }
}

module.exports = SystemPortalModel;
