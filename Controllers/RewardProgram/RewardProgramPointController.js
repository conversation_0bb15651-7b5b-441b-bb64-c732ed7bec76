const fs = require('fs').promises;
const path = require('path');

const RewardProgramMemberModel = new (require("../../Models/RewardProgram/RewardProgramMemberModel"))();
const RewardProgramPointsLogModel = new (require("../../Models/RewardProgram/RewardProgramPointsLogModel"))();
const RewardProgramMemberExpiryPointsModel = new (require("../../Models/RewardProgram/RewardProgramMemberExpiryPointsModel"))();
const RewardProgramModel = new (require("../../Models/RewardProgram/RewardProgramModel"))();
const RewardProgramNotificationModel = new (require("../../Models/RewardProgram/RewardProgramNotificationModel"))();
const IntegrationCredentialModel = new (require('../../Models/IntegrationCredentialModel'))();

const CommonModel = new (require("../../Models/common"))();
const SAPServiceModel = new (require("../../Models/SAPServiceModel"))();
const RewardProgramController = new (require("./RewardProgramController"))()

const EncryptionHandler = new (require("../../Configs/encrypt"))();

const sendFailureEmailSAPServer = require("../../Middleware/SendFailureEmail").SAPServer

const {
    STATUS_CODES,
    SAP_SERVICE,
    REWARD_PROGRAM,
    ENTITY_STATUS,
    QR_CODE_TYPE,
    INTEGRATION_CHANNELS
} = require('../../Configs/constants');

const {
    getQrCodeBase64,
} = require("../../Utils/helpers");

const { logRewardProgram } = require('../../Utils/logHelper');

module.exports = class {

    listPointsLogs = async (req, res) => {
        try {
            const data = await RewardProgramPointsLogModel.findPointsLogsWithPagination(req.query)
            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addDailyAccessPoints = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleId,
                timezone
            } = req.body

            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    tenantId,
                    customerUserRoleId,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    membership: 1,
                    reward_program_id: 1,
                    customer_user_role_id: 1,
                    coins: 1,
                    vip_points: 1,
                    coins_statistics: 1,
                    vip_points_statistics: 1,
                },
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: "milestones classic_member_coin_rules vip_member_coin_rules"
                        },
                    ]
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            const coinRuleKey = REWARD_PROGRAM.MEMBER.COIN_RULES_CONFIGURATION_KEY[memberInfo.membership]

            const logType = REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.DAILY_ACCESS
            const entryType = REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED
            const pointType = REWARD_PROGRAM.POINT.TYPE.COINS

            const todayDate = momentTimezone.tz(timezone)
            const startDate = todayDate.startOf("day").format();
            const endDate = todayDate.endOf("day").format();

            const pointsLogs = await RewardProgramPointsLogModel.findPointsLogsByFilter({
                tenant_id: tenantId,
                customer_user_role_id: customerUserRoleId,
                log_type: logType,
                entry_type: entryType,
                point_type: pointType,
                created_at: {
                    $gte: new Date(startDate),
                    $lte: new Date(endDate),
                },
            })

            if (pointsLogs.length)
                return res.handler.conflict("daily_app_access_points_limit_exceeded")

            const dailyAccessPoints = memberInfo.reward_program_id[coinRuleKey]?.action_types.daily_access
            if (!dailyAccessPoints)
                return res.handler.notFound("reward_program_configuration_not_found")

            await RewardProgramPointsLogModel.addPointsLog(
                {
                    rewardProgramId: memberInfo.reward_program_id._id,
                    rewardProgramMemberId: memberInfo._id,
                    customerUserRoleId: memberInfo.customer_user_role_id,
                    points: dailyAccessPoints,
                    tenantId,
                    logType,
                    entryType,
                    pointType,
                },
                memberInfo,
                req.headers.userDetails._id,
            )

            return res.handler.success(undefined, {
                points: dailyAccessPoints
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getQrCodeForMemberScan = async (req, res) => {
        try {
            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    status: ENTITY_STATUS.ACTIVE,
                    ...req.query,
                },
                {
                    tenant_id: 1,
                    member_id: 1,
                    customer_user_role_id: 1
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            const qrInfo = QR_CODE_TYPE.REWARD_PROGRAM_MEMBER
                + "_" + memberInfo.tenant_id
                + "_" + memberInfo.member_id
                + "_" + memberInfo.customer_user_role_id
                + "_" + new Date()

            const encryptedText = await EncryptionHandler.encrypt(qrInfo)
            const {
                status = "",
                data = "",
                error,
            } = encryptedText

            if (status === STATUS_CODES.SERVER_ERROR) {
                return res.handler.badRequest("unable_to_generate_qr_code", undefined, error)
            }

            const qrCode = await getQrCodeBase64(data)
            return res.handler.success(undefined, qrCode)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    scanQrCodeForMemberScan = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleId,
                qrInfo,
                timezone
            } = req.body

            const decryptedQr = EncryptionHandler.decrypt(qrInfo);
            const {
                status = "",
                data = "",
                error,
            } = decryptedQr || {};

            if (status === STATUS_CODES.SERVER_ERROR) {
                return res.handler.badRequest("unable_to_parse_qr", undefined, error)
            }

            const [
                type,
                tenant_id,
                member_id,
                customer_user_role_id,
                date
            ] = data.split("_")

            if (
                !type ||
                !tenant_id ||
                !member_id ||
                !customer_user_role_id ||
                !date
            ) {
                return res.handler.badRequest("invalid_reward_member_qr")
            }

            if (type !== QR_CODE_TYPE.REWARD_PROGRAM_MEMBER)
                return res.handler.badRequest("qr_code_mismatched_with_type")

            if (tenantId !== parseInt(tenant_id))
                return res.handler.badRequest("qr_code_mismatched_with_tenant")

            if (customerUserRoleId !== customer_user_role_id)
                return res.handler.badRequest("qr_code_mismatched_with_customer")


            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    tenantId,
                    customerUserRoleId,
                    memberId: member_id,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    membership: 1,
                    reward_program_id: 1,
                    customer_user_role_id: 1,
                    coins: 1,
                    vip_points: 1,
                    coins_statistics: 1,
                    vip_points_statistics: 1,
                },
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: "milestones classic_member_coin_rules vip_member_coin_rules"
                        },
                    ]
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            const coinRuleKey = REWARD_PROGRAM.MEMBER.COIN_RULES_CONFIGURATION_KEY[memberInfo.membership]

            const memberScanRule = memberInfo.reward_program_id[coinRuleKey]?.action_types.member_scan
            if (!memberScanRule)
                return res.handler.notFound("reward_program_configuration_not_found")

            const scanTimeDifference = new Date() - new Date(date)
            const scanTimeLimit = (memberScanRule.scan_duration_limit + 20) * 1000
            if (scanTimeDifference > scanTimeLimit) {
                return res.handler.badRequest("reward_member_qr_expired")
            }


            const logType = REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MEMBER_SCAN
            const entryType = REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED
            const pointType = REWARD_PROGRAM.POINT.TYPE.COINS

            const todayDate = momentTimezone.tz(timezone)
            const startDate = todayDate.startOf("day").format();
            const endDate = todayDate.endOf("day").format();

            const pointsLogs = await RewardProgramPointsLogModel.findPointsLogsByFilter({
                tenant_id: tenantId,
                customer_user_role_id: customerUserRoleId,
                log_type: logType,
                entry_type: entryType,
                point_type: pointType,
                created_at: {
                    $gte: new Date(startDate),
                    $lte: new Date(endDate),
                },
            })

            if (pointsLogs.length)
                return res.handler.conflict("daily_member_scan_points_limit_exceeded")

            const pointLog = await RewardProgramPointsLogModel.addPointsLog(
                {
                    rewardProgramId: memberInfo.reward_program_id._id,
                    rewardProgramMemberId: memberInfo._id,
                    customerUserRoleId: memberInfo.customer_user_role_id,
                    points: memberScanRule.coins,
                    tenantId,
                    logType,
                    entryType,
                    pointType,
                },
                memberInfo,
                req.headers.userDetails._id,
            )

            return res.handler.success(undefined, {
                type: QR_CODE_TYPE.REWARD_PROGRAM_MEMBER
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addManualPoints = async (req, res) => {
        try {
            const {
                tenantId,
                customerUserRoleId,
                rewardProgramMemberId
            } = req.body

            const memberInfo = await RewardProgramMemberModel.findMemberByFilter(
                {
                    tenantId,
                    customerUserRoleId,
                    rewardProgramMemberId,
                    status: ENTITY_STATUS.ACTIVE
                },
                {
                    membership: 1,
                    coins: 1,
                    vip_points: 1,
                    coins_statistics: 1,
                    vip_points_statistics: 1,
                },
                {
                    populate: [
                        {
                            path: "reward_program_id",
                            select: "milestones"
                        },
                    ]
                }
            )

            if (!memberInfo)
                return res.handler.notFound("reward_program_member_not_found")

            await RewardProgramPointsLogModel.addPointsLog(
                {
                    logType: REWARD_PROGRAM.POINT.LOG_TYPE.ACTION_TYPE.MANUAL,
                    entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                    ...req.body,
                },
                memberInfo,
                req.headers.userDetails._id,
            )

            return res.handler.success()
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    calculateSAPPoints = async (req, res) => {
        try {
            const {
                paymentAmount,
                aging,
                reward
            } = req.body

            let remainingAmount = paymentAmount

            const calculations = [
                {
                    days: "91_120",
                    aging: aging.balance_91_120_days,
                    rewardPerSAR: reward.payment["91_120_days"] / reward.baseAmount,
                },
                {
                    days: "61_90",
                    aging: aging.balance_61_90_days,
                    rewardPerSAR: reward.payment["61_90_days"] / reward.baseAmount,
                },
                {
                    days: "31_60",
                    aging: aging.balance_31_60_days,
                    rewardPerSAR: reward.payment["31_60_days"] / reward.baseAmount,
                },
                {
                    days: "0_30",
                    aging: aging.balance_0_30_days,
                    rewardPerSAR: reward.payment["0_30_days"] / reward.baseAmount,
                }
            ]

            for (let i = 0; i < calculations.length; i++) {
                if (remainingAmount <= 0) {
                    break;
                }

                const calculation = calculations[i];

                if (
                    (calculations.length - 1) == i
                    || calculation.aging >= remainingAmount
                ) {
                    calculation.amountUsed = remainingAmount
                    calculation.earnPoints = remainingAmount * calculation.rewardPerSAR
                    calculation.remainingAmount = 0
                    break;
                }

                calculation.amountUsed = calculation.aging
                calculation.earnPoints = calculation.aging * calculation.rewardPerSAR

                remainingAmount = remainingAmount - calculation.aging
                calculation.remainingAmount = remainingAmount
            }

            const totalPointsEarn = calculations.reduce((sum, a) => sum + (a.earnPoints ?? 0), 0)

            return res.handler.success(undefined, {
                paymentAmount,
                totalPointsEarn,
                calculations
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    checkSAPPoints = async (req, res) => {
        try {
            const {
                customerExternalId,
                date,
                membership,
                rewardProgramId,
                configurations
            } = req.query

            const rewardProgram = await RewardProgramModel.findRewardProgramById(
                rewardProgramId,
                {
                    _id: 0,
                    base_amount: 1,
                    classic_member_coin_rules: 1,
                    vip_member_coin_rules: 1,
                    vip_points_rules: 1,
                }
            )

            if (!rewardProgram)
                return res.handler.notFound("reward_program_configuration_not_found")

            const {
                points,
                errors,
                statementsSummeryWithDate,
                statementsInfo,
            } = await this.getPointCalculationsByCustomerInfo(
                customerExternalId,
                date,
                date,
                membership,
                rewardProgram,
                configurations
            )

            return res.handler.success(undefined, {
                points,
                errors,
                statementsSummeryWithDate,
                statements: statementsInfo?.statements ?? [],
                rewardProgram,
            })
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    addPointsThroughSAP = async () => {
        try {
            var profiler = logger.startTimer()
            logger.info("Started: Add reward points through SAP cron job")

            const date = parseInt(moment().subtract(1, "day").format("YYYYMMDD"))
            logRewardProgram(`For date (${date}), started add reward points through SAP cron job`)

            const addPointsByRewardProgram = async (
                rewardProgram,
                sapIntegrationCredentials,
                timezone
            ) => {
                const rewardProgramMembersFilter = {
                    rewardProgramId: rewardProgram._id,
                    tenantId: rewardProgram.tenant_id,
                    status: ENTITY_STATUS.ACTIVE
                }

                await RewardProgramMemberModel.membersCallBackWithPagination(
                    rewardProgramMembersFilter,
                    this.addPointsByRewardMember(
                        date,
                        date,
                        rewardProgram,
                        sapIntegrationCredentials,
                        timezone
                    )
                )
            }

            await RewardProgramController.rewardProgramsCallBackBasedTenants(addPointsByRewardProgram, true)
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "ADD REWARD POINTS THROUGH SAP JOB RUN TIME" })
            logger.info(`Completed: Add reward points through SAP cron job\n`)
        }
    }

    addPointsByRewardMember = (
        fromDate,
        toDate,
        rewardProgram,
        sapIntegrationCredentials,
        timezone
    ) => async (memberInfo) => {
        const customerExternalId = memberInfo.customer_user_role_id.external_id
        const {
            points,
            errors,
        } = await this.getPointCalculationsByCustomerInfo(
            customerExternalId,
            fromDate,
            toDate,
            memberInfo.membership,
            rewardProgram,
            sapIntegrationCredentials,
            memberInfo._id,
        )

        logRewardProgram(`For this rewardProgramId (${rewardProgram._id}) & memberId (${memberInfo._id}), found below points`, {
            points,
            errors,
            memberId: memberInfo._id,
            customerExternalId,
        })

        for (let i = 0; i < points.length; i++) {
            try {
                const point = points[i];

                const logDate =
                    timezone
                        ? moment.tz(point.log_date, "YYYY/MM/DD", timezone).startOf('day').format()
                        : moment(point.log_date, "YYYY/MM/DD").startOf('day').format();

                await RewardProgramPointsLogModel.addPointsLog(
                    {
                        tenantId: rewardProgram.tenant_id,
                        customerUserRoleId: memberInfo.customer_user_role_id._id,
                        rewardProgramMemberId: memberInfo._id,
                        rewardProgramId: rewardProgram._id,
                        entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                        pointType: point.point_type,
                        logType: point.log_type,
                        logDate: logDate,
                        calculations: point.calculations,
                        points: point.points,
                        amount: point.amount
                    },
                    memberInfo
                )
            }
            catch (error) {
                logger.error(error)
            }
        }

        if (errors.length) {
            await sendFailureEmailSAPServer(
                "Found some errors on statements from SAP",
                rewardProgram.tenant_id,
                {
                    customerExternalId,
                    errors
                }
            )
        }
    }

    expireRewardProgramMemberCoins = async () => {
        try {
            var profiler = logger.startTimer()
            logger.info("Started: Expire reward program member coins cron job")

            const expireDate = moment().subtract(1, "months")
            const year = parseInt(expireDate.format("YYYY"))
            const month = parseInt(expireDate.format("MM"))

            const expirePointsByRewardProgram = async (
                rewardProgram,
            ) => {
                const filter = {
                    reward_program_id: rewardProgram._id,
                    tenant_id: rewardProgram.tenant_id,
                    expiry_year: year,
                    expiry_month: month
                }

                const expirePointsOneByOne = async (expirePointInfo) => {
                    await CommonModel.transactionCallback(async (session) => {
                        const memberInfo = await RewardProgramMemberModel.findMemberById(
                            expirePointInfo.reward_program_member_id,
                            {
                                membership: 1,
                                coins: 1,
                                vip_points: 1,
                                coins_statistics: 1,
                                vip_points_statistics: 1,
                            },
                            {
                                session,
                                populate: [
                                    {
                                        path: "reward_program_id",
                                        select: "milestones"
                                    },
                                ]
                            }
                        )

                        const pointLog = await RewardProgramPointsLogModel.addPointsLog(
                            {
                                tenantId: rewardProgram.tenant_id,
                                customerUserRoleId: expirePointInfo.customer_user_role_id,
                                rewardProgramMemberId: expirePointInfo.reward_program_member_id,
                                rewardProgramId: rewardProgram._id,
                                pointType: REWARD_PROGRAM.POINT.TYPE.COINS,
                                entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.EXPIRED,
                                logType: REWARD_PROGRAM.POINT.LOG_TYPE.EXPIRED.COINS,
                                points: expirePointInfo.points,
                            },
                            memberInfo,
                            undefined,
                            session
                        )

                        await expirePointInfo.deleteOne({ session })
                    })
                }

                await RewardProgramMemberExpiryPointsModel.expiryPointsCallBackWithPagination(filter, expirePointsOneByOne) // TODO: Optimize this code logic, so that we can process in bulk
            }

            // TODO: Add timezone if needed
            await RewardProgramController.rewardProgramsCallBackBasedTenants(expirePointsByRewardProgram)
        }
        catch (error) {
            logger.error(error)
        }
        finally {
            profiler.done({ message: "EXPIRE REWARD PROGRAM MEMBER COINS" })
            logger.info(`Completed: Expire reward program member coins cron job\n`)
        }
    }

    getPointCalculationsByCustomerInfo = async (
        customerExternalId,
        fromDate,
        toDate,
        membership,
        rewardProgram,
        sapIntegrationCredentials,
        memberId,
    ) => {
        let points = []
        let errors = []

        if (!customerExternalId) {
            logRewardProgram(`For this rewardProgramId (${rewardProgram._id}) & memberId (${memberId}), no customer external id found`, {
                date: fromDate,
            })

            return {
                points,
                errors,
            }
        }

        const statementsInfo = await SAPServiceModel.getStatements(
            {
                customerExternalId,
                fromDate,
                toDate,
            },
            sapIntegrationCredentials
        )

        if (!statementsInfo) {
            logRewardProgram(`For this rewardProgramId (${rewardProgram._id}) & memberId (${memberId}), from SAP no statements info found`, {
                date: fromDate,
                customerExternalId,
            })

            return {
                points,
                errors,
            }
        }
        else {
            logRewardProgram(`For this rewardProgramId (${rewardProgram._id}) & memberId (${memberId}), from SAP statements info`, {
                date: fromDate,
                customerExternalId,
                statementsInfo,
            })
        }

        const statementsSummeryWithDate = SAPServiceModel.generateStatementsSummery(statementsInfo.statements)
        const statementsDates = Object.keys(statementsSummeryWithDate)

        logRewardProgram(`For this rewardProgramId (${rewardProgram._id}), & memberId (${memberId}) found below statements dates`, {
            tenantId: rewardProgram.tenant_id,
            date: fromDate,
            customerExternalId,
            statementsSummeryWithDate,
            statementsDates,
        })

        for (let i = 0; i < statementsDates.length; i++) {
            const statementsDate = statementsDates[i]
            const agingDate = parseInt(moment(statementsDate, "YYYY/MM/DD").subtract(1, "day").format("YYYYMMDD"))
            const statementsSummery = statementsSummeryWithDate[statementsDate]

            let aging

            if ((statementsSummery[SAP_SERVICE.DOCUMENT_TYPE.PAYMENT]?.credit_amount ?? 0) > 0) {
                aging = await SAPServiceModel.getBalance(
                    {
                        customerExternalId,
                        agingDate,
                    },
                    sapIntegrationCredentials
                )

                aging.date = agingDate
            }

            logRewardProgram(`For this rewardProgramId (${rewardProgram._id}), memberId (${memberId}) & statementsDate (${statementsDate}) found below aging info`, {
                tenantId: rewardProgram.tenant_id,
                date: fromDate,
                customerExternalId,
                aging,
                statementsDate,
                agingDate,
                statementsSummery,
            })

            RewardProgramPointsLogModel.calculatePointsFromStatementSummery(
                points,
                errors,
                statementsSummery,
                statementsDate,
                aging,
                membership,
                rewardProgram,

                // Below params are for debugging purpose
                memberId,
                fromDate,
                customerExternalId,
            )
        }

        return {
            points,
            errors,
            statementsInfo,
            statementsSummeryWithDate,
        }
    }

    fetchPointsThroughSAP = async (req, res) => {
        try {
            const {
                tenantId,
                rewardProgramId,
                fromDate,
                toDate
            } = req.body

            const TENANT_ID = tenantId;
            const REWARD_PROGRAM_ID = rewardProgramId;
            const FROM_DATE = fromDate;
            const TO_DATE = toDate;
            const BATCH_SIZE = 10;

            const [sapIntegrationCredentials, rewardProgram] = await Promise.all([
                IntegrationCredentialModel.getCredential(
                    {
                        name: INTEGRATION_CHANNELS.SAP_SERVICE,
                        tenant_id: TENANT_ID,
                        is_active: true,
                        "configurations.is_reward_program_enabled": true
                    },
                    "tenant_id configurations",
                    { lean: true }
                ),
                RewardProgramModel.findRewardProgramById(REWARD_PROGRAM_ID)
            ]);

            if (!sapIntegrationCredentials) {
                return res.handler.badRequest("SAP integration credentials not found");
            }

            if (!rewardProgram) {
                return res.handler.badRequest("Reward program not found");
            }

            const membersListBackWithPagination = await RewardProgramMemberModel.membersListBackWithPagination({
                rewardProgramId: rewardProgram._id,
                tenantId: rewardProgram.tenant_id,
                status: ENTITY_STATUS.ACTIVE
            });

            if (!membersListBackWithPagination?.length) {
                return res.handler.success(undefined, {
                    sapIntegrationCredentials,
                    rewardProgram,
                    membersListBackWithPagination: [],
                    pointsList: [],
                    processedMembers: 0
                });
            }

            const pointsList = [];
            const errors = [];
            let processedMembers = 0;

            for (let i = 0; i < membersListBackWithPagination.length; i += BATCH_SIZE) {
                const batch = membersListBackWithPagination.slice(i, i + BATCH_SIZE);

                const batchPromises = batch.map(async (memberInfo) => {
                    try {
                        const customerExternalId = memberInfo.customer_user_role_id?.external_id;
                        if (!customerExternalId) {
                            return { memberInfo, points: [], errors: ['Missing external_id'] };
                        }

                        const result = await this.getPointCalculationsByCustomerInfo(
                            customerExternalId,
                            FROM_DATE,
                            TO_DATE,
                            memberInfo.membership,
                            rewardProgram,
                            sapIntegrationCredentials,
                            memberInfo._id,
                        );

                        return {
                            memberInfo,
                            points: result.points || [],
                            errors: result.errors || []
                        };
                    } catch (error) {
                        logger.error(`Error processing member ${memberInfo._id}:`, error);
                        return {
                            memberInfo,
                            points: [],
                            errors: [error.message]
                        };
                    }
                });

                const batchResults = await Promise.allSettled(batchPromises);

                batchResults.forEach((result, batchIndex) => {
                    if (result.status === 'fulfilled') {
                        const { memberInfo, points, errors: memberErrors } = result.value;

                        if (memberErrors.length > 0) {
                            errors.push({
                                memberId: memberInfo._id,
                                customerExternalId: memberInfo.customer_user_role_id?.external_id,
                                errors: memberErrors
                            });
                        }

                        points.forEach(point => {
                            try {
                                const pointData = {
                                    tenantId: rewardProgram.tenant_id,
                                    customerUserRoleId: memberInfo.customer_user_role_id._id,
                                    rewardProgramMemberId: memberInfo._id,
                                    rewardProgramId: rewardProgram._id,
                                    entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                    pointType: point.point_type,
                                    logType: point.log_type,
                                    logDate: point.log_date,
                                    calculations: point.calculations,
                                    points: point.points,
                                    amount: point.amount
                                };
                                pointsList.push({ pointData, memberInfo });
                            } catch (pointError) {
                                logger.error(`Error processing point for member ${memberInfo._id}:`, pointError);
                                errors.push({
                                    memberId: memberInfo._id,
                                    pointError: pointError.message
                                });
                            }
                        });

                        processedMembers++;
                    } else {
                        logger.error(`Batch processing failed for member at index ${i + batchIndex}:`, result.reason);
                        errors.push({
                            batchIndex: i + batchIndex,
                            error: result.reason?.message || 'Unknown batch processing error'
                        });
                    }
                });

                if (i + BATCH_SIZE < membersListBackWithPagination.length) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            logger.info(`Processed ${processedMembers}/${membersListBackWithPagination.length} members`);
            logger.info(`Generated ${pointsList.length} points entries`);

            if (errors.length > 0) {
                logger.warn(`${errors.length} errors occurred during processing:`, errors);
            }

            let fileName = null;
            let filePath = null;

            if (pointsList.length > 0) {
                try {
                    const rewardProgramShortId = REWARD_PROGRAM_ID.slice(-8);
                    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);

                    fileName = `points_tenant${TENANT_ID}_rp${rewardProgramShortId}_${FROM_DATE}_${TO_DATE}_${timestamp}.json`;
                    filePath = path.join(__dirname, '../../Doc', fileName);

                    const docsDir = path.join(__dirname, '../../Doc');
                    await fs.mkdir(docsDir, { recursive: true });

                    const fileData = {
                        metadata: {
                            tenantId: TENANT_ID,
                            rewardProgramId: REWARD_PROGRAM_ID,
                            fromDate: FROM_DATE,
                            toDate: TO_DATE,
                            generatedAt: new Date().toISOString(),
                            totalPoints: pointsList.length,
                            processedMembers: processedMembers,
                            totalMembers: membersListBackWithPagination.length,
                            hasErrors: errors.length > 0
                        },
                        pointsList: pointsList,
                        errors: errors.length > 0 ? errors : undefined
                    };

                    await fs.writeFile(filePath, JSON.stringify(fileData, null, 2), 'utf8');

                    logger.info(`Points list saved to file: ${fileName}`);
                    logger.info(`File path: ${filePath}`);

                } catch (fileError) {
                    logger.error('Error saving points list to file:', fileError);
                }
            }

            return res.handler.success(undefined, {
                sapIntegrationCredentials,
                rewardProgram,
                membersListBackWithPagination,
                pointsList,
                processedMembers,
                totalMembers: membersListBackWithPagination.length,
                errors: errors.length > 0 ? errors : undefined,
                file: fileName ? {
                    fileName: fileName,
                    filePath: filePath,
                    saved: true
                } : {
                    saved: false,
                    reason: 'No points to save'
                }
            });

        } catch (error) {
            logger.error('Error in verifyPointsThroughSAP:', error);
            return res.handler.serverError(error);
        }
    };


    normalizeDate = async (date) => {
        if (!date) return null;
        if (date.$date) return new Date(date.$date);
        if (typeof date === 'string') return new Date(date);
        return date;
    }

    compareCalculations = async (calc1, calc2) => {
        if (!calc1 || !calc2) return false;
        if (calc1.length !== calc2.length) return false;

        const sortedCalc1 = [...calc1].sort((a, b) => a.type.localeCompare(b.type));
        const sortedCalc2 = [...calc2].sort((a, b) => a.type.localeCompare(b.type));

        for (let i = 0; i < sortedCalc1.length; i++) {
            const item1 = sortedCalc1[i];
            const item2 = sortedCalc2[i];

            if (item1.type !== item2.type ||
                item1.aging !== item2.aging ||
                item1.points !== item2.points ||
                item1.amountUsed !== item2.amountUsed) {
                return false;
            }
        }
        return true;
    }

    normalizeId = async (id) => {
        if (!id) return null;
        if (typeof id === 'string') return id;
        if (id.$oid) return id.$oid;
        if (id.toString) return id.toString();
        return id;
    }

    comparePointDataWithDB = async (pointData, dbData) => {
        if (!pointData || !dbData) return false;

        if (pointData.tenant_id !== dbData.tenant_id) return false;
        if (this.normalizeId(pointData.customer_user_role_id) !== this.normalizeId(dbData.customer_user_role_id)) return false;
        if (this.normalizeId(pointData.reward_program_member_id) !== this.normalizeId(dbData.reward_program_member_id)) return false;
        if (this.normalizeId(pointData.reward_program_id) !== this.normalizeId(dbData.reward_program_id)) return false;
        if (pointData.entry_type !== dbData.entry_type) return false;
        if (pointData.point_type !== dbData.point_type) return false;
        if (pointData.log_type !== dbData.log_type) return false;
        if (pointData.points !== dbData.points) return false;
        if (pointData.amount !== dbData.amount) return false;

        const pointDate = this.normalizeDate(pointData.log_date);
        const dbDate = this.normalizeDate(dbData.log_date);
        if (pointDate.getTime() !== dbDate.getTime()) return false;

        if (!this.compareCalculations(pointData.calculations, dbData.calculations)) return false;

        return true;
    }

    checkExactEntryExists = async (pointData) => {
        try {
            const potentialMatch = await RewardProgramPointsLogModel.findOneLogsByOldPointsData({
                tenant_id: pointData.tenant_id,
                customer_user_role_id: pointData.customer_user_role_id,
                reward_program_member_id: pointData.reward_program_member_id,
                reward_program_id: pointData.reward_program_id,
                log_date: pointData.log_date
            });

            if (!potentialMatch) {
                return false;
            }

            if (this.comparePointDataWithDB(pointData, potentialMatch)) {
                return true;
            }

            return false;
        } catch (error) {
            console.error('Error checking exact entry existence:', error);
            throw error;
        }
    };

    verifyPointsThroughSAP = async (req, res) => {
        try {
            const {
                fileName
            } = req.body
            const filePath = path.join(process.cwd(), 'Doc', fileName);

            try {
                const fileContent = await fs.readFile(filePath, 'utf8');
                const fileData = JSON.parse(fileContent);
                const notEnterPointList = [];

                let processedCount = 0;
                let foundExactMatches = 0;

                for (const pointsInfo of fileData.pointsList) {
                    try {
                        const point = pointsInfo.pointData
                        const memberInfo = pointsInfo.memberInfo
                        const dateObj = new Date(point.logDate.replace(/\//g, '-'));

                        if (isNaN(dateObj.getTime())) {
                            console.warn(`Invalid date format for point: ${point.logDate}`);
                            continue;
                        }
                        const pointData = {
                            tenant_id: point.tenantId,
                            customer_user_role_id: point.customerUserRoleId,
                            reward_program_member_id: point.rewardProgramMemberId,
                            reward_program_id: point.rewardProgramId,
                            entry_type: point.entryType,
                            point_type: point.pointType,
                            log_type: point.logType,
                            log_date: dateObj,
                            calculations: point.calculations,
                            points: point.points,
                            amount: point.amount
                        };


                        const exactEntryExists = await this.checkExactEntryExists(pointData);

                        if (!exactEntryExists) {
                            const clonedDate = new Date(dateObj.getTime()); // clone the dateObj
                            const logDate = moment.tz(clonedDate, "Asia/Riyadh").startOf('day').format();
                            notEnterPointList.push({
                                data: pointData,
                                date: dateObj.toISOString(),
                                originalLogDate: point.logDate,
                                points: point.points,
                                amount: point.amount
                            });
                            await RewardProgramPointsLogModel.addPointsLog(
                                {
                                    tenantId: point.tenantId,
                                    customerUserRoleId: point.customerUserRoleId,
                                    rewardProgramMemberId: point.rewardProgramMemberId,
                                    rewardProgramId: point.rewardProgramId,
                                    entryType: REWARD_PROGRAM.POINT.ENTRY_TYPE.DISTRIBUTED,
                                    pointType: point.pointType,
                                    logType: point.logType,
                                    logDate: logDate,
                                    calculations: point.calculations,
                                    points: point.points,
                                    amount: point.amount
                                },
                                memberInfo
                            )
                            console.log(`Missing/Different entry found for date: ${point.logDate}, points: ${point.points}`);
                        } else {
                            foundExactMatches++;
                        }

                        processedCount++;

                        if (processedCount % 100 === 0) {
                            console.log(`Processed ${processedCount}/${fileData.pointsList.length} entries. Found ${foundExactMatches} exact matches, ${notEnterPointList.length} missing/different entries.`);
                        }

                    } catch (pointError) {
                        console.error(`Error processing point entry at index ${processedCount}:`, pointError);
                    }
                }

                return res.handler.success(undefined, {
                    summary: {
                        totalEntries: fileData.pointsList.length,
                        processedEntries: processedCount,
                        exactMatches: foundExactMatches,
                        missingOrDifferentEntries: notEnterPointList.length
                    },
                    missingEntries: notEnterPointList,
                    allEntries: fileData.pointsList
                });

            } catch (fileError) {
                return res.handler.notFound('File not found or invalid JSON format');
            }
        } catch (error) {
            console.error('Server error in verifyPointsThroughSAP:', error);
            return res.handler.serverError(error);
        }
    }


}
