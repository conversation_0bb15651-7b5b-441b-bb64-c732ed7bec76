const InternalServiceModal = new (require("../Models/InternalServiceModal"))()
const TenantPortalModal = new (require("../Models/tenantPortal"))();
const RoleModal = new (require("../Models/roles"));
const CommonModal = new (require("../Models/common"));
const SystemPortalModal = new (require("../Models/systemPortal"))()
const HoldReasonModel = new (require('../Models/HoldReasonModel'))();
const IntegrationCredentialModel = new (require('../Models/IntegrationCredentialModel'))();
const SAPServiceModel = new (require('../Models/SAPServiceModel'))();

const OrderNotificationService = new (require("../Services/OrderNotificationService"))()

const {
    VALUES,
    INTEGRATION_CHANNELS,
    SAP_SERVICE,
} = require("../Configs/constants");

class InternalServiceController {

    async getTenantInfo(req, res) {
        try {
            let { _id, projection, options, populate } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (Array.isArray(populate)) {
                populate = populate.map(stringifiedObj => {
                    return typeof (stringifiedObj) === "string"
                        ? JSON.parse(stringifiedObj)
                        : stringifiedObj
                })
            }
            const tenant = await SystemPortalModal.getTenantsById(_id, projection, options, populate)

            return res.handler.success("tenant_get_success", tenant)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantsInfo(req, res) {
        try {
            let { filter, projection, options } = req.query
            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }
            const tenants = await SystemPortalModal.getTenants(filter, projection, options)

            return res.handler.success("tenant_get_success", tenants)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCountries(req, res) {
        try {
            let { filter, projection, options } = req.query
            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const countries = await CommonModal.findCountries(filter, projection, options)
            return res.handler.success(undefined, countries)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getSalespersons(req, res) {
        try {
            let { filter, projection, options } = req.query
            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const roleId = await TenantPortalModal.getSalesPersonRole()
            filter.role_id = roleId

            const users = await TenantPortalModal.getTenantUsers(filter, projection, options)

            return res.handler.success(null, users)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateTenantInfo(req, res) {
        try {
            const { _id, incObj } = req.body
            const tenant = await InternalServiceModal.updateTenantById(_id, incObj)

            if (tenant.modifiedCount) {
                return res.handler.success("tenant_update_success", tenant)
            }
            return res.handler.success("tenant_update_unsuccess", tenant)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantAppSettings(req, res) {
        try {
            let { _id, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            const tenantAppSetting = await TenantPortalModal.tenantAppSettingExist(_id, projection, options)

            return res.handler.success(null, tenantAppSetting)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTenantsAppSettings(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const tenantsAppSetting = await TenantPortalModal.tenantsAppSettingExist(
                filter,
                projection,
                options
            )
            return res.handler.success(null, tenantsAppSetting)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserRoleSettings(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const userRoleSettings = await TenantPortalModal.findUserRoleSettings(
                filter,
                projection,
                options
            )
            return res.handler.success(null, userRoleSettings)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserRoleSettingsCount(req, res) {
        try {
            let { filter } = req.query

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const count = await TenantPortalModal.getUserRoleSettingsCount(filter)
            return res.handler.success(null, count)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async findCurrentPrefix(req, res) {
        try {
            const body = req.query;
            let currentPrefix = await TenantPortalModal.findTenantOrderPrefix({ is_current: true, tenant_id: body.tenantId });
            if (!currentPrefix) {
                const prefix = `${body.tenantId}_`;
                currentPrefix = await TenantPortalModal.findTenantOrderPrefix({ _id: prefix });
                if (!currentPrefix) {
                    currentPrefix = await TenantPortalModal.createTenantPrefix();
                    currentPrefix._id = prefix;
                    currentPrefix.is_current = true;
                    currentPrefix.tenant_id = body.tenantId;
                    currentPrefix.created_by = req.headers.userDetails?._id;
                    currentPrefix.updated_by = req.headers.userDetails?._id;
                }
            }
            currentPrefix.counter += 1;
            await currentPrefix.save();
            // TenantPortalModal.createTenantPrefix
            return res.handler.success("success", currentPrefix);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async sendOrderEmails(req, res) {
        try {
            const {
                tenantId,
                customerRoleId,
                salesPersonRoleId,
                orderStatusTrack,
                orderStatus,
            } = req.body;

            const [tenantDetails, userRoles, roles, decimalPoints] = await Promise.all([
                OrderNotificationService.fetchTenantDetails(tenantId),
                OrderNotificationService.fetchUserRoles(salesPersonRoleId, customerRoleId),
                OrderNotificationService.fetchRequiredRoles(),
                TenantPortalModal.getAppSettingDecimalPoint(tenantId),
            ])

            const processedData = OrderNotificationService.processUserRolesAndRoles(userRoles, roles)
            const notificationConfig = OrderNotificationService.getNotificationConfig(orderStatus, orderStatusTrack);

            const notificationData = await OrderNotificationService.prepareNotificationData(
                processedData,
                req.body,
                notificationConfig,
                tenantDetails,
                decimalPoints,
            )

            await OrderNotificationService.sendNotification(notificationData, notificationConfig)
            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateBulkCustomers(req, res) {
        try {
            const { filter, updateFields } = req.body

            const updateResult = await TenantPortalModal.updateBulkCustomers(filter, updateFields, req.headers)
            if (updateResult.modifiedCount) {
                return res.handler.success("tenant_update_success", updateResult)
            }
            return res.handler.success("tenant_update_unsuccess", updateResult)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCustomer(req, res) {
        try {
            const { _id, projection, options, populate } = req.body

            const customer = await TenantPortalModal.getCustomer(_id, projection, options, populate)
            return res.handler.success(null, customer)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCustomers(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const customerRole = await TenantPortalModal.getCustomerRole();

            const customers = await TenantPortalModal.getTenantUsers(
                {
                    role_id: customerRole._id,
                    ...filter
                },
                projection,
                options
            )

            return res.handler.success(null, customers)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async checkCustomersPreApprovedStatus(req, res) {
        try {
            const {
                tenantId,
                externalIds
            } = req.query

            const sapIntegrationCredentials = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id: tenantId,
                    name: INTEGRATION_CHANNELS.SAP_SERVICE
                },
                "configurations is_active",
                {
                    lean: true
                }
            )

            if (!sapIntegrationCredentials) {
                return res.handler.forbidden('sap_integration_not_found')
            }

            if (!sapIntegrationCredentials.is_active) {
                return res.handler.notFound('sap_integration_not_active')
            }

            const tasks = externalIds.map(externalId => {
                return SAPServiceModel.checkPreApproved(
                    externalId,
                    tenantId,
                    sapIntegrationCredentials
                )
            })

            const results = await Promise.all(tasks)

            let data = {}

            for (let i = 0; i < results.length; i++) {
                data[externalIds[i]] = results[i]
            }

            return res.handler.success(undefined, data)
        }
        catch (error) {
            if (error.name === SAP_SERVICE.ERROR.API) {
                return res.handler.custom(
                    error.statuscode,
                    error.message,
                    error.data,
                    error
                )
            }
            else {
                return res.handler.serverError(error);
            }
        }
    }

    async getUsers(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const users = await TenantPortalModal.getTenantUsers(
                filter,
                projection,
                options
            )

            return res.handler.success(null, users)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getCustomersCount(req, res) {
        try {
            let { filter } = req.query

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const customerRole = await TenantPortalModal.getCustomerRole();

            const customersCount = await TenantPortalModal.getTenantUsersCount({
                role_id: customerRole._id,
                ...filter
            })

            return res.handler.success(null, customersCount)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantBranch(req, res) {
        try {
            const branches = await SystemPortalModal.getBranchesWithWareHouse(req.query.tenantId);
            return res.handler.success("tenant_update_unsuccess", branches)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantBranches(req, res) {
        try {
            let { filter, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const branches = await SystemPortalModal.getTenantBranches(
                filter,
                projection,
                options
            )
            return res.handler.success(undefined, branches)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async salesPersonsBySupervisorId(req, res) {
        try {
            const { supervisorUserRoleId, tenantId } = req.query;
            const [supervisorProfileInfo, salespersonRole] = await Promise.all(
                [
                    TenantPortalModal.findUserProfileWithFilter({ _id: supervisorUserRoleId }, { user_id: 1 }),
                    RoleModal.getRoleByFilter({ portal_type: VALUES.portals.SALES_APP }, { _id: 1 })
                ])
            const salesPersons = await TenantPortalModal.findUserProfilesWithFilter(
                {
                    role_id: salespersonRole._id,
                    supervisor_id: supervisorProfileInfo.user_id,
                    tenant_id: tenantId
                }, { _id: 1 });
            return res.handler.success(null, salesPersons)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getShippingLabel(req, res) {
        try {
            let { tenantId, projection, options } = req.query

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }
            const shippingLabel = await TenantPortalModal.shippingLabelExist(tenantId, projection, options)

            if (shippingLabel?.shipping_label_logo) {
                shippingLabel.shipping_label_logo =
                    VALUES.awsPublicBucketBaseURL +
                    "shipping-label" +
                    `/${tenantId}/` +
                    shippingLabel.shipping_label_logo
            }
            return res.handler.success(null, shippingLabel)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async checkOrderUsers(req, res) {
        try {
            let { tenantId, userRoleId, portalType, branchId } = req.query
            let validUser;

            switch (portalType) {
                case VALUES.portals.TENANT_PORTAL:
                    validUser = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, _id: userRoleId, is_deleted: false })
                    break;
                case VALUES.portals.BRANCH_PORTAL:
                    validUser = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, _id: userRoleId, branch_id: branchId, is_deleted: false })
                    break;
                default:
                    validUser = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, _id: userRoleId, is_deleted: false, is_active: true })
                    break;
            }

            validUser = validUser ? true : false;
            const countryInfo = await TenantPortalModal.getTenantByFilter({ _id: tenantId }, { country: 1 }, { populate: { path: 'country', select: { currency: 1, name: 1 } } })

            return res.handler.success(null, { validUser, countryInfo });
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateDataSheetFiles(req, res) {
        try {
            const { filter, updateFields } = req.body

            const updateResult = await TenantPortalModal.updateBulkDataSheetFiles(filter, updateFields, req.headers)

            if (updateResult.modifiedCount) {
                return res.handler.success("data_sheet_file_update_success", updateResult)
            }
            return res.handler.success("data_sheet_file_update_unsuccess", updateResult)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    getHoldReasonTemplate = async (req, res) => {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query

            if (typeof (projection) === "string") {
                projection = JSON.parse(projection)
            }

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const holdReason = await HoldReasonModel.getHoldReason(
                filter,
                projection,
                options
            )

            return res.handler.success(null, holdReason);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getIntegrationCredential = async (req, res) => {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query;

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const integrationCredential = await IntegrationCredentialModel.getCredential(
                filter,
                projection,
                options
            )

            return res.handler.success(null, integrationCredential);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getIntegrationCredentials = async (req, res) => {
        try {
            let {
                filter = {},
                projection = {},
                options = {},
            } = req.query;

            if (typeof (options) === "string") {
                options = JSON.parse(options)
            }

            if (typeof (filter) === "string") {
                filter = JSON.parse(filter)
            }

            const integrationCredentials = await IntegrationCredentialModel.getCredentials(
                filter,
                projection,
                options
            )

            return res.handler.success(null, integrationCredentials);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getRecentlyRestockedItems = async (req, res) => {
        try {
            let {
                sapIntegrationCredentials
            } = req.query

            if (typeof sapIntegrationCredentials === "string") {
                sapIntegrationCredentials = JSON.parse(sapIntegrationCredentials)
            }

            const items = await SAPServiceModel.getRecentlyRestockedItems(sapIntegrationCredentials)
            return res.handler.success(null, items)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = InternalServiceController
