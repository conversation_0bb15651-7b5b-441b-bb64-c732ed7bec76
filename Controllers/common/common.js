const CommonModal = new (require('../../Models/common'))();
const UserAuthModal = new (require("../../Models/auth"))();
const SystemPortalModel = new (require("../../Models/systemPortal"))();
const InternalServiceModal = new (require("../../Models/InternalServiceModal"))()

const encrypt = new (require("../../Configs/encrypt"))();
const FileUpload = require('../../Configs/awsUploader').S3Upload;
const fileUpload = new FileUpload();

const {
    VALUES,
    SIGNING_TYPE,
    ENTITY_STATUS,
    FILE_PATH,
    LISTING_TYPES,
    OBJECT_UPLOAD_SIGNATURE_TYPE,
    BUCKET_TYPE,
} = require("../../Configs/constants");

const {
    errorHandler
} = require('../../Utils/helpers');

class CommonController {

    async getCountryList(req, res) {
        try {
            const countryList = await CommonModal.getActiveCountries(req.query, req.headers);
            return res.handler.success(null, countryList);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addCountry(req, res) {
        try {
            const existingCountries = await CommonModal.findCountryCountWithName(req.body, req.headers);
            if (existingCountries?.length > 1) {
                return res.handler.conflict("validation_exists_country");
            }

            if (existingCountries?.length === 1 && existingCountries[0].is_deleted) {
                await CommonModal.restoreCountryById(existingCountries[0]._id);
                return res.handler.success("restored_country");
            } else if (existingCountries?.length === 1 && !existingCountries[0].is_deleted) {
                return res.handler.conflict("validation_exists_country");
            }

            const country = await CommonModal.addCountry(req.body, req.headers);
            await country.save();
            return res.handler.success("created_country")
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async editCountry(req, res) {
        try {
            const country = await CommonModal.findCountryById(req.body.countryId);
            if (!country || country?.is_deleted) {
                return res.handler.notFound("validation_not_found_country");
            }
            req.body.expectCountryId = req.body.countryId;
            const countryCount = await CommonModal.findCountryCountWithName(req.body, req.headers);
            if (countryCount.length > 0) {
                return res.handler.conflict("validation_exists_country");
            }
            const body = req.body;
            country.name = body.countryName;
            country.country_code = body.countryCode;
            country.timezone = body.timeZone;
            country.is_active = body.isActive;
            country.secondary_language_code = body.secondaryLanguage;
            country.vat = body?.vat || 0;
            country.updated_by = req.headers.userDetails._id;
            country.currency = body.currency;
            country.mobile_number_format = body.mobileNumberFormat;
            country.google_place_id = body.googlePlaceId;
            country.alpha_two_code = body.alphaTwoCode;
            country.secondary_language_name = body.secondaryLanguageName;
            await country.save();

            return res.handler.success("updated_edited");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeCountryStatus(req, res) {
        try {
            const country = await CommonModal.findCountryById(req.body.countryId, { _id: 1, is_deleted: 1 });
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country");
            }

            let message;
            if (req.body.status === ENTITY_STATUS.ACTIVE) {
                country.is_active = true;
                message = "updated_activated_country"
            } else {
                country.is_active = false;
                message = "updated_in_activated_country"
            }
            await country.save();
            return res.handler.success(message);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteCountry(req, res) {
        try {
            const country = await CommonModal.findCountryById(req.query.countryId, { _id: 1, is_deleted: 1 });
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country")
            }
            await Promise.all([
                CommonModal.deleteCityWithCountryId(req.query.countryId),
                CommonModal.deleteRegionWithCountryId(req.query.countryId),
                CommonModal.deleteCountryById(req.query.countryId)
            ]);
            return res.handler.success("deleted_country");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteRegion(req, res) {
        try {
            const country = await CommonModal.getRegionById(req.query.regionId, { _id: 1, is_deleted: 1 });
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country")
            }
            await Promise.all([
                CommonModal.deleteCitiesWithRegionId(req.query.regionId),
                CommonModal.deleteRegionById(req.query.regionId),
            ]);
            return res.handler.success("deleted_region");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async searchUsers(req, res) {
        try {
            let users;
            switch (req.query.type) {
                case SIGNING_TYPE.MOBILE:
                    users = await CommonModal.findUsersFromMobile(req.query.countryCode, req.query.mobileNumber);
                    break;
                case SIGNING_TYPE.EMAIL:
                    users = await CommonModal.findUsersFromEmail(req.query.email);
                    break;
            }
            return res.handler.success(null, users)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addRegion(req, res) {
        try {
            const existingRegions = await CommonModal.findExistingRegionWithName(req.body, req.headers);
            if (existingRegions?.length > 1) {
                return res.handler.conflict("validation_exists_region_name");
            }
            if (existingRegions?.length === 1 && existingRegions[0].is_deleted) {
                await CommonModal.restoreRegionById(existingRegions[0]._id);
                return res.handler.success("RESTORED.REGION");
            } else if (existingRegions?.length === 1 && !existingRegions[0].is_deleted) {
                return res.handler.conflict("validation_exists_region_name");
            }

            const existingRegionWithCode = await CommonModal.findExistingRegionWithCode(req.body, req.headers);
            if (existingRegionWithCode?.length > 0) {
                return res.handler.conflict("validation_exists_region_code");
            }

            const country = await CommonModal.findCountryById(req.body.countryId, { _id: 1, is_deleted: 1 });
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country")
            }
            const region = await CommonModal.addRegion(req.body, req.headers);
            await region.save();
            return res.handler.success("created_region", region);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addCity(req, res) {
        try {
            const existingCity = await CommonModal.findExistingCity(req.body, req.headers);
            if (existingCity?.length > 1) {
                return res.handler.conflict("validation_exists_city_name");
            }
            if (existingCity?.length === 1 && existingCity[0].is_deleted) {
                await CommonModal.restoreCityById(existingCity[0]._id);
                return res.handler.success("RESTORED.CITY");
            } else if (existingCity?.length === 1 && !existingCity[0].is_deleted) {
                return res.handler.conflict("validation_exists_city_name");
            }

            const [country, region] = await Promise.all(
                [
                    CommonModal.findCountryById(req.body.countryId, { _id: 1, is_deleted: 1 }),
                    CommonModal.getRegionById(req.body.regionId, { _id: 1, is_deleted: 1 })
                ]
            );
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country")
            }
            if (!region || region.is_deleted) {
                return res.handler.notFound("validation_not_found_region")
            }

            const city = await CommonModal.addCity(req.body, req.headers);
            await city.save();
            return res.handler.success("created_city", city);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async automaticAddCity(req, res) {
        try {
            const existingCountries = await CommonModal.findCountryCountWithName(req.body, req.headers);

            // Check country validation
            if (!existingCountries?.length) {
                return res.handler.badRequest("validation_not_found_country")
            }

            // Check region validation
            req.body.countryId = existingCountries[0]._id;
            req.body.name = req.body.regionName;
            req.body.isActive = true;

            const existingRegions = await CommonModal.findExistingRegionWithName(req.body, req.headers);

            // If region is not in the database then add it.
            if (!existingRegions?.length) {
                req.body.secondaryLanguageName = req.body.regionName;

                // Check regionCode validation
                if (req.body.regionCode) {
                    req.body.code = req.body.regionCode
                    const existingRegionWithCode = await CommonModal.findExistingRegionWithCode(req.body, req.headers);

                    if (existingRegionWithCode?.length > 0) {
                        return res.handler.conflict(
                            "validation_exists_region_code",
                            {
                                region_id: existingRegionWithCode[0]._id,
                            }
                        );
                    }
                }
                const region = await CommonModal.addRegion(req.body, req.headers);
                await region.save();
                req.body.regionId = region._id
            }
            else {
                // If region is in the database as deleted then restore it.
                if (existingRegions?.length === 1 && existingRegions[0].is_deleted) {
                    await CommonModal.restoreRegionById(existingRegions[0]._id);
                }
                req.body.regionId = existingRegions[0]._id;
            }

            // Check city validation. If city is not in the database then add it.
            req.body.name = req.body.cityName;

            const existingCity = await CommonModal.findExistingCity(req.body, req.headers);
            const cityId = existingCity?.[0]?._id
            const regionId = existingCity?.[0]?.region_id

            if (existingCity?.length > 1) {
                return res.handler.conflict(
                    "validation_exists_city_name",
                    {
                        _id: cityId,
                        region_id: regionId,
                    }
                );
            }
            else if (existingCity?.length === 1) {
                if (existingCity[0].is_deleted) {
                    await CommonModal.restoreCityById(cityId);

                    return res.handler.success(
                        "RESTORED.CITY",
                        {
                            _id: cityId,
                            region_id: regionId,
                        }
                    );
                }
                else {
                    return res.handler.conflict(
                        "validation_exists_city_name",
                        {
                            _id: cityId,
                            region_id: regionId,
                        }
                    );
                }
            }
            req.body.secondaryLanguageName = req.body.cityName;

            const city = await CommonModal.addCity(req.body, req.headers);
            await city.save();

            return res.handler.success("created_city", {
                _id: city._id,
                region_id: city.region_id,
            });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getRegions(req, res) {
        try {
            const regions = await CommonModal.getRegions(req.query, req.headers);
            return res.handler.success(null, regions);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async editRegion(req, res) {
        try {
            const region = await CommonModal.getRegionById(req.body.regionId);
            if (!region || region?.is_deleted) {
                return res.handler.notFound("validation_not_found_region");
            }
            const country = await CommonModal.findCountryById(req.body.countryId, { _id: 1, is_deleted: 1 });
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country")
            }
            // findExistingRegionWithName
            req.body.expectRegionId = req.body.regionId;
            const existingRegions = await CommonModal.findExistingRegionWithName(req.body, req.headers);
            if (existingRegions?.length > 0) {
                return res.handler.conflict("validation_exists_region_name");
            }

            const existingRegionWithCode = await CommonModal.findExistingRegionWithCode(req.body, req.headers);
            if (existingRegionWithCode?.length > 0) {
                return res.handler.conflict("validation_exists_region_code");
            }

            const body = req.body;
            if (region.country_id.toString() !== body.countryId) {
                await CommonModal.updateCountryOfCities(req.body.regionId, body.countryId);
            }
            region.country_id = body.countryId;
            region.name = body.name;
            region.is_active = body.isActive;
            region.code = body.code;
            region.secondary_language_code = body.secondaryLanguage;
            region.updated_by = req.headers.userDetails._id;
            region.secondary_language_name = body.secondaryLanguageName;
            await region.save();
            return res.handler.success("updated_edited");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeRegionStatus(req, res) {
        try {
            const region = await CommonModal.getRegionById(req.body.regionId);
            if (!region || region?.is_deleted) {
                return res.handler.notFound("validation_not_found_region");
            }

            let message;
            if (req.body.status === ENTITY_STATUS.ACTIVE) {
                region.is_active = true;
                message = "updated_activated_region"
            } else {
                region.is_active = false;
                message = "updated_in_activated_region"
            }

            await region.save();
            return res.handler.success(message);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCities(req, res) {
        try {
            const cities = await CommonModal.getCities(req.query, req.headers);
            return res.handler.success(null, cities);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async editCity(req, res) {
        try {
            const city = await CommonModal.getCityById(req.body.cityId);
            if (!city || city?.is_deleted) {
                return res.handler.notFound("validation_not_found_city");
            }

            const [country, region] = await Promise.all(
                [
                    CommonModal.findCountryById(req.body.countryId, { _id: 1, is_deleted: 1 }),
                    CommonModal.getRegionById(req.body.regionId, { _id: 1, is_deleted: 1 })
                ]
            );
            if (!country || country.is_deleted) {
                return res.handler.notFound("validation_not_found_country")
            }
            if (!region || region.is_deleted) {
                return res.handler.notFound("validation_not_found_region")
            }

            req.body.expectCityId = req.body.cityId
            const existingCities = await CommonModal.findExistingCity(req.body, req.headers);
            if (existingCities?.length > 0) {
                return res.handler.conflict("validation_exists_city_name");
            }

            const body = req.body;
            city.name = body.name;
            city.region_id = body.regionId;
            city.country_id = body.countryId;
            city.is_active = body.isActive;
            city.updated_by = req.headers.userDetails._id;
            city.secondary_language_name = body.secondaryLanguageName;
            await city.save();
            return res.handler.success("updated_edited");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeCitiesStatus(req, res) {
        try {
            await CommonModal.changeCitiesStatus(req.body, req.headers);
            let message;
            if ((req.body.cities.length === 1) && (req.body.status === ENTITY_STATUS.ACTIVE)) {
                message = "updated_activated_city";
            } else if ((req.body.cities.length === 1) && (req.body.status === ENTITY_STATUS.INACTIVE)) {
                message = "updated_in_activated_city";
            } else if ((req.body.cities.length > 1) && (req.body.status === ENTITY_STATUS.INACTIVE)) {
                message = "updated_in_activated_cities";
            } else if ((req.body.cities.length > 1) && (req.body.status === ENTITY_STATUS.ACTIVE)) {
                message = "updated_activated_cities";
            }

            return res.handler.success(message);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteCities(req, res) {
        try {
            await CommonModal.deleteCities(req.body, req.headers);
            if (req.body.cities.length === 1) {
                return res.handler.success("deleted_city");
            } else {
                return res.handler.success("deleted_cities");
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getLanguages(req, res) {
        try {
            const result = await CommonModal.getLanguages(req.query, req.headers);
            if (req.query.type === LISTING_TYPES.PAGINATION) {
                const enInfo = await CommonModal.getLanguageByCode("en");
                result.list.forEach(lang => {
                    lang['translation_percentage'] = ((lang['key_count'] / enInfo.key_count) * 100).toFixed('0');
                });
            }
            return res.handler.success(null, result)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeUserPassword(req, res) {
        try {
            let user = await UserAuthModal.findUserById(req.body.userId);
            if (!user || user.is_deleted) {
                return res.handler.notFound();
            }

            switch (req.body.type) {
                case VALUES.PASSWORD_RESET_TYPE.EMAIL_LINK:
                    const otp = encrypt.generateAuthToken()
                    await UserAuthModal.sendEMailVerificationLink(user.email, otp, user._id.toString(), "Reset Password");
                    user.forgot_password_otp = otp;
                    user.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                    user.otp_verification_time = new Date();
                    await user.save();
                    await SystemPortalModel.expireSessionByUserId(user._id)
                    return res.handler.success("process_password_reset_link");

                case VALUES.PASSWORD_RESET_TYPE.MANUAL:
                    await SystemPortalModel.expireSessionByUserId(user._id)
                    UserAuthModal.updateUserPassword(req.body.password, user);
                    return res.handler.success("updated_password");
            }

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeUserProfilePic(req, res) {
        try {
            const body = req.body;

            const userDetails = await SystemPortalModel.getUserById(body.userId);
            if (!userDetails || userDetails.is_deleted) {
                return res.handler.notFound("validation_not_found_user");
            }
            // delete old file
            if (userDetails.profile_pic) {
                await fileUpload.deleteFile(FILE_PATH.USER_PROFILE, userDetails.profile_pic);
                userDetails.profile_pic = undefined;
            }

            // upload new file, provided
            if (body.profilePic) {
                const fileName = await fileUpload.uploadFiles(FILE_PATH.USER_PROFILE, body.profilePic);
                userDetails.profile_pic = fileName.data[0].fileName;
            }
            await userDetails.save();

            if (userDetails.profile_pic) {
                userDetails.profile_pic = await fileUpload.getSignedUrl(FILE_PATH.USER_PROFILE, userDetails.profile_pic);
            }
            return res.handler.success("updated_edited", { profile_pic: userDetails.profile_pic });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUploadSignature(req, res) {
        try {
            const {
                fileName,
                type,
                standId,
                customerUserRoleId
            } = req.body

            const tenantId = Number(req.body.tenantId);

            try {
                if (type === OBJECT_UPLOAD_SIGNATURE_TYPE.STAND_IMAGES) {
                    await InternalServiceModal.validateStandUpload(
                        {
                            tenantId,
                            standId,
                            customerUserRoleId
                        },
                    );
                }
            }
            catch (error) {
                return errorHandler(error, res);
            }

            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);

            const pathName =
                type === OBJECT_UPLOAD_SIGNATURE_TYPE.SYSTEM_USER_PROFILE
                    ? FILE_PATH[type]
                    : FILE_PATH[type] + `/${tenantId}`

            const responseData = {
                "signedUrl": "",
            }

            if (Object.values(OBJECT_UPLOAD_SIGNATURE_TYPE).includes(type)) {
                if (Array.isArray(fileName)) {
                    const promises = []

                    for (let fName of fileName) {
                        promises.push(
                            publicS3.getPutObject(pathName, fName.trim())
                        )
                    }

                    const results = await Promise.allSettled(promises)
                    const signedUrl = {}

                    if (results.length) {
                        results.forEach(result => {
                            const { status, value } = result

                            if (status === "fulfilled") {
                                const firstIndexOf = value.indexOf("?")
                                const lastIndexOf = value.lastIndexOf("/", firstIndexOf)

                                const fileName = value.substring(lastIndexOf + 1, firstIndexOf)
                                signedUrl[fileName] = value
                            }
                        })
                        responseData["signedUrl"] = signedUrl
                    }
                }
                else {
                    responseData["signedUrl"] = await publicS3.getPutObject(pathName, fileName.trim())
                }
            }
            return res.handler.success("upload_url_get_success", responseData)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async addImage(req, res) {
        try {
            await CommonModal.addUpdateImage(req.body, req.headers)
            return res.handler.success("image_upload_success")
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    getProfileImageUrl = (tenantId) => {
        return tenantId
            ? `${FILE_PATH.S3_URL.USER_PROFILE}/${tenantId}/`
            : `${FILE_PATH.S3_URL.SYSTEM_USER_PROFILE}/`
    }

    getProfileThumbImageUrl = (tenantId) => {
        return tenantId
            ? `${FILE_PATH.S3_URL.COMPRESSED.USER_PROFILE_THUMBNAIL}/${tenantId}/`
            : `${FILE_PATH.S3_URL.COMPRESSED.SYSTEM_USER_PROFILE_THUMBNAIL}/`
    }

    async deleteImage(req, res) {
        try {
            const body = req.body;
            const userRoleId = body.imageName.split('.')[0];

            const userDetails = await SystemPortalModel.getUserRoleWithRoleInfo(userRoleId);
            const filePath = body.tenantId ? FILE_PATH.S3_URL.USER_PROFILE : FILE_PATH.S3_URL.SYSTEM_USER_PROFILE

            if (!userDetails || userDetails.is_deleted) {
                return res.handler.notFound("validation_not_found_user");
            }

            if (userDetails.profile_pic) {
                await fileUpload.deleteFile(filePath, userDetails.profile_pic);
                userDetails.profile_pic = undefined;
            }

            await userDetails.save();
            return res.handler.success("delete_files_success", { userDetails });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = CommonController
