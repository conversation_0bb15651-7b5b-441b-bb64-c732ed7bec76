const { VALUES, SIGNING_TYPE, ENTITY_STATUS, FILE_PATH, BUCKET_TYPE } = require("../Configs/constants");

const csvtoJson = require('csvtojson');
const LanguageModel = new (require("../Models/language"));
const path = require('path');
const fs = require("fs");
const fsPromises = require("fs/promises");
const { S3Upload } = require('../Configs/awsUploader');

class LanguageController {
    async addLanguage(req, res) {
        try {

            const existingLanguage = await LanguageModel.getExistingLanguageByCode(req.body.languageCode);
            if(existingLanguage.length > 0 && existingLanguage[0].count > 0) {
                return res.handler.conflict("VALIDATION.EXISTS.LANGUAGE_CODE")
            }

            const [masterJson, tabJson, webJson] = await Promise.all([
                LanguageModel.getMasterFileContent(),
                LanguageModel.getMasterFileContent("en", "tab"),
                LanguageModel.getMasterFileContent("en", "web"),
            ]);
            for (const key in masterJson) {
                masterJson[key].isTranslated = false
            }

            req.body.key_count = 0;
            // download and check for all keys should be present init
            // create new json file and save it on the bucket
            const masterFilePath = path.join(__dirname, `/../Assets/Jsons/master/${req.body.languageCode}.json`);
            const tabFilePath = path.join(__dirname, `/../Assets/Jsons/tab/${req.body.languageCode}.json`); 
            const webFilePath = path.join(__dirname, `/../Assets/Jsons/web/${req.body.languageCode}.json`);

            fs.writeFileSync(masterFilePath, JSON.stringify(masterJson));
            fs.writeFileSync(webFilePath, JSON.stringify(webJson));
            fs.writeFileSync(tabFilePath, JSON.stringify(tabJson));
            const publicS3 = new S3Upload(BUCKET_TYPE.LOCALES);
            const result = await Promise.all([
                publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${req.body.languageCode}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${req.body.languageCode}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${req.body.languageCode}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
            ]);


            // fs.unlinkSync(csvFilePath);

            const language = await LanguageModel.createLanguage(req.body, req.headers);
            await language.save()
            return res.handler.success("created_language");
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async editLanguage(req, res) {
        try {
            var { languageId, enableRtl, translationFile } = req.body;
            const existingLanguageId = await LanguageModel.getExistingLanguageById(languageId);
            let translatedKeys = 0;

            if(!existingLanguageId) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.LANGUAGE")
            }

            if(existingLanguageId.language_code === "en" && Array.isArray(translationFile)) {
                return res.handler.badRequest("can't_edit_en_lang");
            }
            if(Array.isArray(translationFile)) {
                const enMasterData = await LanguageModel.getMasterFileContent();
                const lngMasterData = await LanguageModel.getMasterFileContent(existingLanguageId.language_code)

                const csvFilePath = path.join(__dirname, "/../Assets/Images/Temp/Original/" + translationFile[0]);
                const JsonContent = await csvtoJson().fromFile(csvFilePath);
                const newLJsonContentObj = {};
    
                for (let i = 0; i < JsonContent.length; i++) {
                    let row = JsonContent[i];
                    if(!row["Website Translation"] && !row["Tablet Translation"] && !row["Common translation"]) {
                        return res.handler.badRequest("file_missing_translation")
                    }
                    newLJsonContentObj[row["FIELD_NAME"]] = {
                        web: row["Website Translation"],
                        tab: row["Tablet Translation"],
                        both: row["Common translation"],
                        isTranslated: lngMasterData[row["FIELD_NAME"]]?.isTranslated || false,
                    }
                    
                }
    
                const langMasterObj = {};
                const langTabObj = {};
                const langWebObj = {};
                for(let key in enMasterData) {
                    if(enMasterData[key].both) {
                        if(!newLJsonContentObj[key]?.both) {
                            return res.handler.badRequest("file_missing_common_translation");
                        }
                        if (!newLJsonContentObj[key]?.isTranslated && newLJsonContentObj[key]?.both !== enMasterData[key].both) {
                            translatedKeys += 1 ; 
                            newLJsonContentObj[key].isTranslated  = true
                        }
                        langMasterObj[key] = {
                            web: "",
                            tab: "",
                            both: newLJsonContentObj[key]?.both,
                            isTranslated: newLJsonContentObj[key]?.isTranslated
                        }
                        langTabObj[key] = newLJsonContentObj[key]?.both;
                        langWebObj[key] = newLJsonContentObj[key]?.both;
                    } else {
                        if(!newLJsonContentObj[key]?.web && !newLJsonContentObj[key]?.tab) {
                            return res.handler.badRequest("file_missing_web_or_tab_translation");
                        }
                        if(!newLJsonContentObj[key]?.isTranslated && newLJsonContentObj[key]?.web !== enMasterData[key].web) {
                            translatedKeys++
                            newLJsonContentObj[key].isTranslated  = true
                        } else if(!newLJsonContentObj[key]?.isTranslated && newLJsonContentObj[key]?.tab !== enMasterData[key].tab) {
                            translatedKeys++
                            newLJsonContentObj[key].isTranslated  = true
                        }
    
                        langMasterObj[key] = {
                            web: newLJsonContentObj[key]?.web || "",
                            tab: newLJsonContentObj[key]?.tab || "",
                            both: "",
                            isTranslated: newLJsonContentObj[key]?.isTranslated
                        }
                        langTabObj[key] = newLJsonContentObj[key]?.tab || "";
                        langWebObj[key] = newLJsonContentObj[key]?.web || "";
                    }
                }
    
                const masterFilePath = path.join(__dirname, `/../Assets/Jsons/master/${existingLanguageId.language_code}.json`);
                const tabFilePath = path.join(__dirname, `/../Assets/Jsons/tab/${existingLanguageId.language_code}.json`); 
                const webFilePath = path.join(__dirname, `/../Assets/Jsons/web/${existingLanguageId.language_code}.json`);
    
                fs.writeFileSync(masterFilePath, JSON.stringify(langMasterObj));
                fs.writeFileSync(webFilePath, JSON.stringify(langWebObj));
                fs.writeFileSync(tabFilePath, JSON.stringify(langTabObj));
                const publicS3 = new S3Upload(BUCKET_TYPE.LOCALES);
                const result = await Promise.all([
                    publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${existingLanguageId.language_code}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                    publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${existingLanguageId.language_code}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                    publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${existingLanguageId.language_code}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
                ]);
                fs.unlinkSync(csvFilePath);
                const enKeys = Object.keys(enMasterData).length;
                if (existingLanguageId.key_count !== 0 && translatedKeys > 0) {
                    existingLanguageId.key_count = enKeys < (existingLanguageId.key_count + translatedKeys) ? enKeys : translatedKeys;
                } else if (existingLanguageId.key_count === 0 && translatedKeys) {
                    existingLanguageId.key_count = enKeys < translatedKeys ? enKeys : translatedKeys;
                }
            }


            existingLanguageId.enable_rtl = enableRtl === "true" ? true : false;
            existingLanguageId.updated_by = req.headers.userDetails._id;
            await existingLanguageId.save();

            return res.handler.success("updated_edited");
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async uploadInitEnFile(req, res) {
        try {
            const publicS3 = new S3Upload(BUCKET_TYPE.LOCALES);
            // const body = {
            //     languageCode: "en",
            //     key_count: counter,
            //     name: "English",
            // }
            // const language = await LanguageModel.createLanguage(body, req.headers);
            // await language.save()
            // const result = await publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`en.json`], path.join(__dirname, `../Assets/Jsons/master/en.json`), {"CacheControl": "max-age=0"} );
            // const result1 = await publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`en.json`], path.join(__dirname, `../Assets/Jsons/tab/en.json`), {"CacheControl": "max-age=0"} );
            // const result2 = await publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`en.json`], path.join(__dirname, `../Assets/Jsons/web/en.json`), {"CacheControl": "max-age=0"} );
            return res.handler.success(null);
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async sampleCsv(req, res) {
        try {
            const result = await LanguageModel.getMasterFileContent();
            let newArray = []
            for( let row in result){
                if (result[row].both) {
                    newArray.push({
                        FIELD_NAME:row,
                        type:"both",
                        web: "",
                        tab: "",
                        both: result[row].both,
                        isTranslated: false
                    })
                } else if (result[row].tab && result[row].web) {
                    newArray.push({
                        FIELD_NAME:row,
                        type:"web-tab",
                        web: result[row].web,
                        tab: result[row].tab,
                        both: "",
                        isTranslated: false
                    })
                } else if (result[row].tab && !result[row].web) {
                    newArray.push({
                        FIELD_NAME:row,
                        type:"tab",
                        web: "",
                        tab: result[row].tab,
                        both: "",
                        isTranslated: false
                    })
                } else if (result[row].web && !result[row].tab) {
                    newArray.push({
                        FIELD_NAME:row,
                        type:"web",
                        web: result[row].web,
                        tab: "",
                        both: "",
                        isTranslated: false
                    })
                } else {
                    newArray.push({
                        FIELD_NAME:row,
                        type:"both",
                        web: "",
                        tab: "",
                        both: "",
                        isTranslated: false
                    })
                } 
            }

            return res.handler.success(null, newArray);
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async addKey(req, res) {
        try {
            const body = req.body;
            let addedMasterObject = {};
            let tabValue = "";
            let webValue = "";
            const [masterEnData, tabEnData, webEnData] = await Promise.all([
                LanguageModel.getMasterFileContent("en", "master"),
                LanguageModel.getMasterFileContent("en", "tab"),
                LanguageModel.getMasterFileContent("en", "web"),
            ]);
            if(masterEnData[body.key]) {
                return res.handler.conflict("validation_exists_lang_key");
            } else {
                masterEnData[body.key] = {
                    web: "",
                    tab: "",
                    both: "",
                    isTranslated: true
                }

                switch (body.type) {

                    case "web-tab":
                        masterEnData[body.key].tab = body.tab;
                        masterEnData[body.key].web = body.web;
                        tabEnData[body.key] = body.tab;
                        webEnData[body.key] = body.web;
                        tabValue = body.tab;
                        webValue = body.web;
                        break;

                    case "both":
                        masterEnData[body.key].both = body.both;
                        tabEnData[body.key] = body.both;
                        webEnData[body.key] = body.both;
                        tabValue = body.both;
                        webValue = body.both;
                        break;
                }
                addedMasterObject = { ...masterEnData[body.key], isTranslated: false };
            }

            const languages = await LanguageModel.getAllLanguages();
            const publicS3 = new S3Upload(BUCKET_TYPE.LOCALES);
            const fileUploadPromises = [];
            for (let i = 0; i < languages.length; i++) {
                let lang = languages[i];
                let masterFilePath = path.join(__dirname, `/../Assets/Jsons/master/${lang.language_code}.json`);
                let tabFilePath = path.join(__dirname, `/../Assets/Jsons/tab/${lang.language_code}.json`); 
                let webFilePath = path.join(__dirname, `/../Assets/Jsons/web/${lang.language_code}.json`);
                if(lang.language_code === "en") {
                    fs.writeFileSync(masterFilePath, JSON.stringify(masterEnData));
                    fs.writeFileSync(webFilePath, JSON.stringify(webEnData));
                    fs.writeFileSync(tabFilePath, JSON.stringify(tabEnData));
                    fileUploadPromises.push(
                        publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${lang.language_code}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${lang.language_code}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${lang.language_code}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
                        LanguageModel.updateEngLanguageKeyCountByOne(), // to increment translated key count in english
                    )
                } else {
                    let [masterJson, tabJson, webJson] = await Promise.all([
                        LanguageModel.getMasterFileContent(lang.language_code, "master"),
                        LanguageModel.getMasterFileContent(lang.language_code, "tab"),
                        LanguageModel.getMasterFileContent(lang.language_code, "web"),
                    ]);
                    masterJson[body.key] = addedMasterObject;
                    tabJson[body.key] = tabValue;
                    webJson[body.key] = webValue;
                    fs.writeFileSync(masterFilePath, JSON.stringify(masterJson));
                    fs.writeFileSync(webFilePath, JSON.stringify(webJson));
                    fs.writeFileSync(tabFilePath, JSON.stringify(tabJson));
                    fileUploadPromises.push(
                        publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${lang.language_code}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${lang.language_code}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${lang.language_code}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
                    )
                }
            }

            await Promise.all(fileUploadPromises);
            const dirMaster = path.join(__dirname + "/../Assets/Jsons/master");
            const dirWeb = path.join(__dirname + "/../Assets/Jsons/web");
            const dirTab = path.join(__dirname + "/../Assets/Jsons/tab");
            // delete all json file from the json folders
            let [masterFiles, WebFiles, tabFiles] = await Promise.all([fsPromises.readdir(dirMaster), fsPromises.readdir(dirWeb), fsPromises.readdir(dirTab)]);
            masterFiles = masterFiles.filter(f => f !== ".gitKeep");
            WebFiles = WebFiles.filter(f => f !== ".gitKeep");
            tabFiles = tabFiles.filter(f => f !== ".gitKeep");
            if(Array.isArray(masterFiles)){
                for (const file of masterFiles) {
                    fs.unlinkSync(path.join(dirMaster, file));
                }
            }
            if(Array.isArray(WebFiles)) {
                for (const file of WebFiles) {
                    fs.unlinkSync(path.join(dirWeb, file));
                }
            }
            if(Array.isArray(tabFiles)) {
                for (const file of tabFiles) {
                    fs.unlinkSync(path.join(dirTab, file));
                }
            }
            return res.handler.success("created_lang_key");
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async editLanguageKey(req, res) {
        try {
            var { languageId, languageCode, key, type, both, web, tab, hasTypeChanged } = req.body;
            const fileUpload = [];
            const publicS3 = new S3Upload(BUCKET_TYPE.LOCALES);
            let hasTranslated = false;
            if(type === "both" && !both) {
                return res.handler.badRequest("provide_both_param");
            }
            if(type === "web-tab" && (!web || !tab)) {
                return res.handler.badRequest("provide_web_tab_param");
            }
            const existingLanguageId = await LanguageModel.getExistingLanguageById(languageId);
            if (!existingLanguageId) {
                return res.handler.conflict("VALIDATION.NOT_FOUND.LANGUAGE")
            }

            if (existingLanguageId.language_code !== languageCode) {
                return res.handler.conflict("VALIDATION.INVALID.LANGUAGE")
            }

            if(hasTypeChanged && existingLanguageId.language_code === "en") {
                // update keys of all languages according to new type
                const languages = await LanguageModel.getAllLanguages();
                for (let i = 0; i < languages.length; i++) {
                    let lang = languages[i];
                    let [masterJson, tabJson, webJson] = await Promise.all([
                        LanguageModel.getMasterFileContent(lang.language_code, "master"),
                        LanguageModel.getMasterFileContent(lang.language_code, "tab"),
                        LanguageModel.getMasterFileContent(lang.language_code, "web"),
                    ]);

                    let masterFilePath = path.join(__dirname, `/../Assets/Jsons/master/${lang.language_code}.json`);
                    let tabFilePath = path.join(__dirname, `/../Assets/Jsons/tab/${lang.language_code}.json`); 
                    let webFilePath = path.join(__dirname, `/../Assets/Jsons/web/${lang.language_code}.json`);

                    if(lang.language_code === "en") {
                        switch (type) {
                            case "web-tab":
                                masterJson[key] = {
                                    web: web,
                                    tab: tab,
                                    both: "",
                                    isTranslated: true
                                }
                                tabJson[key] = tab;
                                webJson[key] = web
                                break;
                            case "both":
                                masterJson[key] = {
                                    web: "",
                                    tab: "",
                                    both: both,
                                    isTranslated: true
                                };
                                tabJson[key] = both;
                                webJson[key] = both;
                                break;
                        }
                    } else {
                        switch (type) {
                            case "web-tab":
                                // type is changed form both to web-tab
                                // type is changed form web-tab to both
                                tabJson[key] = masterJson[key]?.both || "";
                                webJson[key] = masterJson[key]?.both || "";
                                masterJson[key] = {
                                    web: masterJson[key]?.both || "",
                                    tab: masterJson[key]?.both || "",
                                    both: "",
                                    isTranslated: masterJson[key]?.isTranslated
                                };
                                break;
                            case "both":
                                // type is changed form web-tab to both
                                masterJson[key] = {
                                    web: "",
                                    tab: "",
                                    both: masterJson[key]?.web,
                                    isTranslated: masterJson[key]?.isTranslated
                                }
                                tabJson[key] = masterJson[key]?.web || "";
                                webJson[key] = masterJson[key]?.web || "";
                                break;
                        }
                    }
                    fs.writeFileSync(masterFilePath, JSON.stringify(masterJson));
                    fs.writeFileSync(webFilePath, JSON.stringify(webJson));
                    fs.writeFileSync(tabFilePath, JSON.stringify(tabJson));
                    fileUpload.push(
                        publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${lang.language_code}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${lang.language_code}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${lang.language_code}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
                    )
                }

            } else {
                // update the provided language with its key
                const filesArrayPromise = []
                if(existingLanguageId.language_code !== "en") {
                    filesArrayPromise.push(
                        LanguageModel.getMasterFileContent(existingLanguageId.language_code, "master"),
                        LanguageModel.getMasterFileContent(existingLanguageId.language_code, "tab"),
                        LanguageModel.getMasterFileContent(existingLanguageId.language_code, "web"),
                        LanguageModel.getMasterFileContent("en", "master"),
                        LanguageModel.getMasterFileContent("en", "tab"),
                        LanguageModel.getMasterFileContent("en", "web"),
                    )
                } else {
                    filesArrayPromise.push(
                        LanguageModel.getMasterFileContent(existingLanguageId.language_code, "master"),
                        LanguageModel.getMasterFileContent(existingLanguageId.language_code, "tab"),
                        LanguageModel.getMasterFileContent(existingLanguageId.language_code, "web"),
                    )
                }
                const [lngMaster, lngTablet, lngWeb, enMaster, enTab, enWeb] = await Promise.all(filesArrayPromise);
    
                if (!lngMaster[key]) {
                    return res.handler.conflict("validation_not_found_key_missing");
                } else {
                    switch (type) {
                        case "both":
                            // this language was same as english text before editing then increment the count 
                            if(existingLanguageId.language_code !== "en" && (!lngMaster[key]?.isTranslated) && (lngMaster[key]?.both === enMaster[key]?.both)) {
                                hasTranslated = true;
                                lngMaster[key].isTranslated = true;
                            }
                            lngMaster[key].both = both;
                            lngMaster[key].web = "";
                            lngMaster[key].tab = "";
                            lngTablet[key] = both;
                            lngWeb[key] = both;
                            lngMaster[key].isTranslated = true;
                            break;
                        case "web-tab":
                            // this language was same as english text before editing then increment the count 
                            if(existingLanguageId.language_code !== "en" && (!lngMaster[key]?.isTranslated) && (lngMaster[key]?.web === enMaster[key]?.web || lngMaster[key]?.tab === enMaster[key]?.tab)) {
                                hasTranslated = true;
                            }
                            lngMaster[key].both = "";
                            lngMaster[key].web = web;
                            lngMaster[key].tab = tab;
                            lngTablet[key] = tab;
                            lngWeb[key] = web;
                            lngMaster[key].isTranslated = true;
                            break;
                    }
                }

    
                const masterFilePath = path.join(__dirname, `/../Assets/Jsons/master/${existingLanguageId.language_code}.json`);
                const tabFilePath = path.join(__dirname, `/../Assets/Jsons/tab/${existingLanguageId.language_code}.json`); 
                const webFilePath = path.join(__dirname, `/../Assets/Jsons/web/${existingLanguageId.language_code}.json`);
    
                fs.writeFileSync(masterFilePath, JSON.stringify(lngMaster));
                fs.writeFileSync(webFilePath, JSON.stringify(lngWeb));
                fs.writeFileSync(tabFilePath, JSON.stringify(lngTablet));
                fileUpload.push(
                    publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${languageCode}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                    publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${languageCode}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                    publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${languageCode}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
                )
            }
            
            if (hasTranslated) {
                existingLanguageId.key_count += 1
                fileUpload.push(existingLanguageId.save())
            }
            await Promise.all(fileUpload);

            const dirMaster = path.join(__dirname + "/../Assets/Jsons/master");
            const dirWeb = path.join(__dirname + "/../Assets/Jsons/web");
            const dirTab = path.join(__dirname + "/../Assets/Jsons/tab");

            // delete all json file from the json folders
            const [masterFiles, WebFiles, tabFiles] = await Promise.all([fsPromises.readdir(dirMaster), fsPromises.readdir(dirWeb), fsPromises.readdir(dirTab)])
            if(Array.isArray(masterFiles)){
                for (const file of masterFiles) {
                    fs.unlinkSync(path.join(dirMaster, file));
                }
            }
            if(Array.isArray(WebFiles)) {
                for (const file of WebFiles) {
                    fs.unlinkSync(path.join(dirWeb, file));
                }
            }
            if(Array.isArray(tabFiles)) {
                for (const file of tabFiles) {
                    fs.unlinkSync(path.join(dirTab, file));
                }
            }
            return res.handler.success("updated_edited");
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async deleteKey(req, res) {
        try {
            const languages = await LanguageModel.getAllLanguages();
            const publicS3 = new S3Upload(BUCKET_TYPE.LOCALES);
            const langFilePromises = [];
            const key = req.query.key;
            let [enMasterJson, enTabJson, enWebJson] = await Promise.all([
                LanguageModel.getMasterFileContent("en", "master"),
                LanguageModel.getMasterFileContent("en", "tab"),
                LanguageModel.getMasterFileContent("en", "web"),
            ]);
            if(!enMasterJson[key]) {
                return res.handler.notFound("key_not_found");
            }
            // delete key in english json
            delete enMasterJson[key];
            delete enTabJson[key];
            delete enWebJson[key];

            const dirMaster = path.join(__dirname + "/../Assets/Jsons/master");
            const dirWeb = path.join(__dirname + "/../Assets/Jsons/web");
            const dirTab = path.join(__dirname + "/../Assets/Jsons/tab");
            let masterFilePath = dirMaster + "/en.json";
            let webFilePath = dirWeb + "/en.json";
            let tabFilePath = dirTab + "/en.json"
            fs.writeFileSync(masterFilePath, JSON.stringify(enMasterJson));
            fs.writeFileSync(webFilePath, JSON.stringify(enWebJson));
            fs.writeFileSync(tabFilePath, JSON.stringify(enTabJson));
            const fileUpload = [];
            fileUpload.push(
                publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${"en"}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${"en"}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${"en"}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
            )
            

            for (let i = 0; i < languages.length; i++) {
                const lang = languages[i];

                if(lang.language_code === "en") {
                    lang.key_count = Object.keys(enMasterJson).length;
                    await lang.save();
                } else {
                    let [masterJson, tabJson, webJson] = await Promise.all([
                        LanguageModel.getMasterFileContent(lang.language_code, "master"),
                        LanguageModel.getMasterFileContent(lang.language_code, "tab"),
                        LanguageModel.getMasterFileContent(lang.language_code, "web"),
                    ]);
                    // delete key in each languages
                    delete masterJson[key];
                    delete tabJson[key];
                    delete webJson[key];

                    // This will syncup the translated key count
                    lang.key_count = await LanguageModel.translationCountForLanguage(enMasterJson, masterJson);
                    await lang.save();
                    let masterFilePath = dirMaster + `/${lang.language_code}.json`;
                    let webFilePath = dirWeb + `/${lang.language_code}.json`;
                    let tabFilePath = dirTab + `/${lang.language_code}.json`;

                    fs.writeFileSync(masterFilePath, JSON.stringify(masterJson));
                    fs.writeFileSync(webFilePath, JSON.stringify(webJson));
                    fs.writeFileSync(tabFilePath, JSON.stringify(tabJson));

                    fileUpload.push(
                        publicS3.uploadFiles(FILE_PATH.MASTER_LOCAL, [`${lang.language_code}.json`], masterFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.WEB_LOCAL, [`${lang.language_code}.json`], webFilePath, {"CacheControl": "max-age=0"}),
                        publicS3.uploadFiles(FILE_PATH.TAB_LOCAL, [`${lang.language_code}.json`], tabFilePath, {"CacheControl": "max-age=0"}),
                    )
                    
                }
            }

            await Promise.all(fileUpload);
            // delete all json file from the json folders
            let [masterFiles, WebFiles, tabFiles] = await Promise.all([fsPromises.readdir(dirMaster), fsPromises.readdir(dirWeb), fsPromises.readdir(dirTab)])
            masterFiles = masterFiles.filter(f => f !== ".gitKeep");
            WebFiles = WebFiles.filter(f => f !== ".gitKeep");
            tabFiles = tabFiles.filter(f => f !== ".gitKeep");
            if(Array.isArray(masterFiles)){
                for (const file of masterFiles) {
                    fs.unlinkSync(path.join(dirMaster, file));
                }
            }
            if(Array.isArray(WebFiles)) {
                for (const file of WebFiles) {
                    fs.unlinkSync(path.join(dirWeb, file));
                }
            }
            if(Array.isArray(tabFiles)) {
                for (const file of tabFiles) {
                    fs.unlinkSync(path.join(dirTab, file));
                }
            }
            return res.handler.success();
        } catch(error) {
            return res.handler.serverError(error);
        }
    }
}

module.exports = LanguageController;