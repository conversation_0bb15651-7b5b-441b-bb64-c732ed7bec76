const IntegrationCredentialModel = new (require('../Models/IntegrationCredentialModel'))();
const HoldReasonModel = new (require('../Models/HoldReasonModel'))();

const EncryptionHandler = new (require("../Configs/encrypt"))();

const {
    STATUS_CODES,
    INTEGRATION_CHANNELS
} = require('../Configs/constants');

const {
    convertToSnakeCase
} = require('../Utils/helpers');

class TenantCredentialsController {

    createCredential = async (req, res) => {
        try {
            const body = convertToSnakeCase(req.body);

            const {
                tenant_id,
                name,
                configurations
            } = body;

            if (configurations.password) {
                const encryptedPassword = await EncryptionHandler.encrypt(configurations.password)
                const {
                    status = "",
                    data = "",
                    error,
                } = encryptedPassword

                if (status === STATUS_CODES.SERVER_ERROR) {
                    return res.handler.badRequest("unable_to_encrypt_password", undefined, error)
                }

                configurations.password = data
            }

            const credential = await IntegrationCredentialModel.createCredential(
                {
                    tenant_id,
                    name,
                    configurations,
                    unique_integration_credential: `${tenant_id}_${name}`,
                    created_by: req.headers.userDetails?._id,
                    updated_by: req.headers.userDetails?._id
                }
            );

            await credential.save();

            if (name === INTEGRATION_CHANNELS.MESSAGE_BIRD) {
                await HoldReasonModel.updateHoldReasons(
                    {
                        tenant_id,
                    },
                    {
                        integration_credential_id: credential._id,
                        updated_by: req.headers.userDetails?._id,
                    }
                );
            }

            return res.handler.created();
        }
        catch (error) {
            if (
                error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE &&
                "unique_integration_credential" in error.keyValue
            ) {
                return res.handler.conflict("credential_already_exists")
            }
            return res.handler.serverError(error);
        }
    };

    updateCredential = async (req, res) => {
        try {
            const body = convertToSnakeCase(req.body);

            const {
                tenant_id,
                name,
                configurations,
                is_active
            } = body;

            const credential = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id,
                    name
                },
            );

            if (!credential) {
                return res.handler.notFound("credential_not_found")
            }

            if (configurations.password) {
                const encryptedPassword = await EncryptionHandler.encrypt(configurations.password)
                const {
                    status = "",
                    data = "",
                    error,
                } = encryptedPassword

                if (status === STATUS_CODES.SERVER_ERROR) {
                    return res.handler.badRequest("unable_to_encrypt_password", undefined, error)
                }

                configurations.password = data
            }

            Object.keys(configurations).forEach(key => {
                credential.configurations[key] = configurations[key]
            })
            credential.markModified('configurations');

            credential.updated_by = req.headers.userDetails?._id;
            credential.is_active = is_active;

            await credential.save();

            if (name === INTEGRATION_CHANNELS.MESSAGE_BIRD) {
                await HoldReasonModel.updateHoldReasons(
                    {
                        tenant_id,
                    },
                    {
                        integration_credential_id: credential._id,
                        updated_by: req.headers.userDetails?._id,
                    }
                );
            }

            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    getCredentials = async (req, res) => {
        try {
            const {
                tenantId,
                names,
            } = req.query;

            const credentials = await IntegrationCredentialModel.getCredentials(
                {
                    tenant_id: tenantId,
                    name: {
                        $in: names
                    }
                },
                undefined,
                {
                    lean: true
                }
            );

            return res.handler.success(null, credentials);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };
}

module.exports = TenantCredentialsController;
