const CityService = require('../Services/CityService');
const DataSyncService = require('../Services/DataSyncService');
const RegionService = require('../Services/RegionService');

class DataSyncController {

    async getTenantAppSetting(req, res) {
        try {
            const response = await DataSyncService.getTenantAppSetting(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserRoleSettings(req, res) {
        try {
            const response = await DataSyncService.getUserRoleSettings(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUserRoles(req, res) {
        try {
            const response = await DataSyncService.getUserRoles(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getRegions(req, res) {
        try {
            const response = await RegionService.getDataSyncRegions(req.query);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCities(req, res) {
        try {
            const response = await CityService.getDataSyncCities(req.body);
            return res.handler.success(null, response);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }
}

module.exports = DataSyncController; 
