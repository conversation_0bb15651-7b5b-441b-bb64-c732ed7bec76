const TenantPortalModal = new (require("../Models/tenantPortal"))();
const UserAuthModal = new (require("../Models/auth"))();
const RoleModal = new (require("../Models/roles"))();

const {
    PRIMITIVE_ROLES
} = require("../Configs/constants");

class TenantCustomerController {
    async editCustomerCommonAddress(req, res) {
        try {
            const {
                shipping_address,
                shipping_country_id,
                shipping_city_id,
                shipping_region_id,
                gps_coordinates,
                shipping_country_code,
                shipping_mobile_number
            } = req.body;

            // only logged in customer change it's common address
            const { _id } = req.headers.userDetails;

            const tenantCustomer = await TenantPortalModal.findTenantCustomer({ _id });

            if (!tenantCustomer) {
                return res.handler.notFound("customer_not_found");
            }
            tenantCustomer.shipping_address = shipping_address;
            tenantCustomer.shipping_country_id = shipping_country_id;
            tenantCustomer.shipping_city_id = shipping_city_id;
            tenantCustomer.shipping_region_id = shipping_region_id;
            tenantCustomer.gps_coordinates = gps_coordinates;
            tenantCustomer.shipping_country_code = shipping_country_code;
            tenantCustomer.shipping_mobile_number = shipping_mobile_number;
            await tenantCustomer.save();

            return res.handler.success("customer_details_updated", tenantCustomer)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async searchCustomerByMobileNumber(req, res) {
        try {
            const body = req.query;
            const customerRoleId = await RoleModal.getRoleByFilter({ name: PRIMITIVE_ROLES.CUSTOMER }, { _id: 1 });

            const pipeline = [
                {
                    $match: {
                        tenant_id: body.tenantId,
                        role_id: customerRoleId._id
                    }
                },
                {
                    $project: {
                        customer_name: 1,
                        customer_legal_name: 1,
                        user_id: 1
                    }
                },
                {
                    $lookup: {
                        from: "tenant_customers",
                        localField: "user_id",
                        foreignField: "_id",
                        as: "customer",
                        pipeline: [
                            {
                                $project: {
                                    country_code: 1,
                                    mobile_number: 1
                                }
                            }
                        ]
                    }
                },
                {
                    $addFields: {
                        customer: { $first: "$customer" }
                    }
                },
                {
                    $match: {
                        "customer.country_code": body.countryCode,
                        "customer.mobile_number": Number(body.mobileNumber)
                    }
                }
            ]
            const data = await UserAuthModal.getUserRolesWithAggregation(pipeline);

            return res.handler.success("existing_customers", data);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = TenantCustomerController;