const axios = require("axios");

const HoldReasonModel = new (require('../Models/HoldReasonModel'))();
const IntegrationCredentialModel = new (require('../Models/IntegrationCredentialModel'))();
const TenantPortalModal = new (require("../Models/tenantPortal"))();
const MessageBird = new (require('../Configs/messageBird'))();

const {
    convertToSnakeCase,
    roundOf,
} = require('../Utils/helpers');

const {
    STATUS_CODES,
    INTEGRATION_CHANNELS,
    VALUES,
    BULK_CREATE_HOLD_REASONS,
    ENTITY_STATUS
} = require('../Configs/constants');

class HoldReasonController {

    createHoldReason = async (req, res) => {
        try {
            const body = convertToSnakeCase(req.body);

            let {
                tenant_id,
                title,
                secondary_language_title,
                hold_templates = [],
                release_templates = [],
                is_whatsapp_message_enabled,
                is_active
            } = body;

            const credentialsResponse = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id,
                    name: INTEGRATION_CHANNELS.MESSAGE_BIRD
                },
            )

            const configurations = credentialsResponse?.configurations || {};
            if (
                !configurations.access_key &&
                !configurations.base_url &&
                !configurations.workspace_id &&
                !configurations.channel_id
            ) {
                return res.handler.notFound('message_bird_credentials_not_found');
            }

            const holdReason = await HoldReasonModel.createHoldReason({
                tenant_id,
                title,
                secondary_language_title,
                hold_templates,
                release_templates,
                is_whatsapp_message_enabled,
                is_active,
                integration_credential_id: credentialsResponse._id,
                created_by: req.headers.userDetails._id,
                updated_by: req.headers.userDetails._id
            });

            await holdReason.save();
            return res.handler.created(holdReason);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    updateHoldReason = async (req, res) => {
        try {
            const body = convertToSnakeCase(req.body);

            let {
                id,
                tenant_id,
                title,
                secondary_language_title,
                hold_templates = [],
                release_templates = [],
                is_whatsapp_message_enabled,
                is_active
            } = body;

            const reason = await HoldReasonModel.getHoldReason(
                {
                    _id: id,
                    tenant_id
                }
            );

            if (!reason) {
                return res.handler.notFound("hold_reason_not_found")
            }

            reason.tenant_id = tenant_id;
            reason.title = title;
            reason.secondary_language_title = secondary_language_title;
            reason.hold_templates = hold_templates;
            reason.release_templates = release_templates;
            reason.is_whatsapp_message_enabled = is_whatsapp_message_enabled;
            reason.is_active = is_active;
            reason.updated_by = req.headers.userDetails._id;

            await reason.save();
            return res.handler.success("update_hold_reason", reason);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    getHoldReasons = async (req, res) => {
        try {
            const {
                tenantId,
                status = ENTITY_STATUS.ALL
            } = req.query;

            const holdReasonFilter = {
                tenant_id: tenantId
            }

            if (status === ENTITY_STATUS.ACTIVE) {
                holdReasonFilter.is_active = true
            }

            if (status === ENTITY_STATUS.INACTIVE) {
                holdReasonFilter.is_active = false
            }

            const reasons = await HoldReasonModel.getHoldReasons(
                holdReasonFilter,
                undefined,
                {
                    lean: true
                }
            );

            return res.handler.success(null, reasons);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    /*
     * GET PROJECT BY THE WORKSPACE WILL PROVIDE ALL THE TEMPLATES IN THE MESSAGE BIRD
     * TEMPLATES ARE KNOW AS A PROJECT IN THE MESSAGE BIRD
     */
    getTemplates = async (req, res) => {
        try {
            const {
                tenantId
            } = req.query;

            const credentialsResponse = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id: tenantId,
                    name: INTEGRATION_CHANNELS.MESSAGE_BIRD
                }
            )

            if (
                !credentialsResponse?.configurations?.access_key &&
                !credentialsResponse?.configurations?.base_url &&
                !credentialsResponse?.configurations?.workspace_id &&
                !credentialsResponse?.configurations?.channel_id
            ) {
                return res.handler.notFound('message_bird_credentials_not_found');
            }

            const templates = await MessageBird.getTemplates(
                credentialsResponse.configurations,
            );

            if (templates.status === STATUS_CODES.SUCCESS) {
                return res.handler.success(null,
                    {
                        list: templates.data || []
                    }
                );
            }
            else {
                let status = templates.status || STATUS_CODES.SERVER_ERROR;

                if (status === STATUS_CODES.UNAUTHORIZED) {
                    status = STATUS_CODES.FORBIDDEN;
                }

                return res.handler.custom(
                    status,
                    templates.message,
                );
            }

        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    getReasonTemplateWithDetails = async (req, res) => {
        try {
            const {
                tenantId,
                holdReasonId,
                type,
                orderId
            } = req.query;

            const filter = {
                "_id": orderId,
                "tenant_id": tenantId,
            };

            const projection = {
                customer_user_role_id: 1,
                order_number: 1,
                sales_user_role_id: 1,
                tenant_id: 1,
                total_amount: 1,
                total_tax: 1,
                unique_order_number: 1
            }

            const previewDetails = {
                order: {},
                user: {}
            };

            try {
                const orderUrl = VALUES.internalServiceBaseURL + "order";

                const orderData = await axios({
                    url: orderUrl,
                    params: {
                        filter,
                        projection
                    },
                })

                const order = orderData?.["data"]?.["data"];

                if (
                    order &&
                    order.sales_user_role_id &&
                    order.customer_user_role_id
                ) {
                    const customerApiParams = {
                        filter: {
                            _id: {
                                $in:
                                    [
                                        new mongoose.Types.ObjectId(order.sales_user_role_id),
                                        new mongoose.Types.ObjectId(order.customer_user_role_id)
                                    ]
                            },
                        },
                        projection: "-_id user_id role_id tenant_id collection_name customer_name customer_first_name customer_last_name customer_legal_name preferred_language",
                        options: {
                            lean: true,
                            populate: [
                                {
                                    path: "user_id",
                                    select: "_id first_name last_name email country_code mobile_number"
                                },
                                {
                                    path: "role_id",
                                    select: "_id name role_id"
                                },
                            ]
                        },
                    };

                    const [customers, salesPersonSettings] = await Promise.all([
                        TenantPortalModal.getTenantUsers(
                            customerApiParams.filter,
                            customerApiParams.projection,
                            customerApiParams.options
                        ),
                        TenantPortalModal.findUserRoleSetting(
                            {
                                _id: `${tenantId}_${order.sales_user_role_id}`
                            },
                            {
                                preferred_language: 1
                            }
                        ),
                    ])

                    previewDetails.order = order || {};

                    const decimalPoints = await TenantPortalModal.getAppSettingDecimalPoint(tenantId)
                    const orderAmount =
                        (previewDetails.order.total_amount || 0) +
                        (previewDetails.order.total_tax || 0)

                    previewDetails.order.order_amount = roundOf(orderAmount, decimalPoints, "string")

                    delete previewDetails.order.total_amount
                    delete previewDetails.order.total_tax

                    const userData = {};

                    if (customers?.length) {
                        customers.map((item) => {
                            if (item.collection_name === "users") {
                                userData['salesPerson'] = {};
                                userData['salesPerson'].first_name = item.user_id?.first_name;
                                userData['salesPerson'].last_name = item.user_id?.last_name;
                                userData['salesPerson'].name = `${item.user_id?.first_name || ""} ${item.user_id?.last_name || ""}`;
                                userData['salesPerson'].email = item.user_id?.email;
                                userData['salesPerson'].mobile_number = item.user_id?.mobile_number;
                                userData['salesPerson'].country_code = item.user_id?.country_code;
                                userData['salesPerson'].preferred_language = salesPersonSettings?.preferred_language || "en";
                            }

                            if (item.collection_name === "tenant_customers") {
                                userData['customer'] = {};
                                userData['customer'].first_name = item?.customer_first_name;
                                userData['customer'].last_name = item?.customer_last_name;
                                userData['customer'].name = `${item?.customer_first_name || ""} ${item?.customer_last_name || ""}`;
                                userData['customer'].mobile_number = item.user_id?.mobile_number;
                                userData['customer'].country_code = item.user_id?.country_code;
                                previewDetails["order"].legal_name = item?.customer_legal_name;
                                userData['customer'].preferred_language = item?.preferred_language || "en";
                            }
                        })
                    }
                    previewDetails.user = userData || {};
                }

            } catch (error) {
                logger.error(error)
            }

            const credentialsResponse = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id: tenantId,
                    name: INTEGRATION_CHANNELS.MESSAGE_BIRD
                },
            )

            const configurations = credentialsResponse?.configurations || {};
            if (
                !configurations.access_key &&
                !configurations.base_url &&
                !configurations.workspace_id &&
                !configurations.channel_id
            ) {
                return res.handler.notFound('message_bird_credentials_not_found');
            }

            // TODO: TYPE CHANGE TO THE ENUM
            const holdReason = await HoldReasonModel.getHoldReason(
                {
                    _id: holdReasonId,
                    tenant_id: tenantId
                },
                {
                    [type]: 1
                },
                {
                    lean: true
                }
            )

            if (!holdReason && !holdReason?.[type]?.length) {
                return res.handler.notFound("hold_reason_not_found")
            }

            for (let index = 0; index < holdReason[type]?.length; index++) {
                const template = holdReason[type][index];

                const project = await MessageBird.getProjectDetail(
                    template.template_id,
                    configurations,
                );

                template.project = project?.data || {};

                template.messageText =
                    project?.data?.platformContent[0]?.blocks?.find(
                        content =>
                            content.type === 'text' &&
                            content.role === 'body'
                    )?.text?.text || '';
            }

            return res.handler.success(null, {
                reasons: holdReason,
                preview: previewDetails,
            });
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    getTemplateDetail = async (req, res) => {
        try {
            const {
                tenantId,
                templateId,
            } = req.query;

            const credentialsResponse = await IntegrationCredentialModel.getCredential(
                {
                    tenant_id: tenantId,
                    name: INTEGRATION_CHANNELS.MESSAGE_BIRD
                },
            )

            const configurations = credentialsResponse?.configurations || {};

            if (
                !configurations.access_key &&
                !configurations.base_url &&
                !configurations.workspace_id &&
                !configurations.channel_id
            ) {
                return res.handler.notFound('message_bird_credentials_not_found');
            }

            const project = await MessageBird.getProjectDetail(templateId, configurations);

            if (project.status === STATUS_CODES.SUCCESS) {
                return res.handler.success(
                    undefined,
                    project.data
                );
            }
            else {
                let status = project.status || STATUS_CODES.SERVER_ERROR;

                if (status === STATUS_CODES.UNAUTHORIZED) {
                    status = STATUS_CODES.FORBIDDEN;
                }

                return res.handler.custom(
                    status,
                    project.message,
                );
            }
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    addHoldReasonInAllTenant = async (req, res) => {
        try {
            const tenants = await TenantPortalModal.tenantInfo(
                {},
                {
                    _id: 1
                },
                {
                    lean: true
                }
            );

            const holdReasons = [];
            for (let index = 0; index < tenants.length; index++) {
                const tenant = tenants[index];

                JSON.parse(JSON.stringify(BULK_CREATE_HOLD_REASONS)).forEach(holReason => {
                    holdReasons.push({
                        ...holReason,
                        tenant_id: tenant._id,
                        created_by: req.headers.userDetails?._id,
                        updated_by: req.headers.userDetails?._id
                    })
                })
            }

            await HoldReasonModel.createHoldReasons(holdReasons);
            return res.handler.success(holdReasons);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };
}

module.exports = HoldReasonController;
