const SAPServiceModel = new (require("../Models/SAPServiceModel"))();

const PDFManager = new (require("../Managers/PDFManager"))()

const TenantPortalModal = new (require("../Models/tenantPortal"))()

const {
    formatAmount,
} = require("../Utils/helpers")

const {
    SAP_SERVICE,
} = require("../Configs/constants")

module.exports = class {

    getAccountBalance = async (req, res) => {
        try {
            const balanceData = await SAPServiceModel.getBalance(
                req.query,
                req.headers.sapIntegrationCredentials,
            )

            if (!balanceData)
                return res.handler.success("sap_account_balance_not_found", {})

            res.handler.success(undefined, balanceData)
        }
        catch (error) {
            if (error.name === SAP_SERVICE.ERROR.API) {
                return res.handler.custom(
                    error.statuscode,
                    error.message,
                    undefined,
                    error
                )
            }
            else {
                return res.handler.serverError(error);
            }
        }
    }

    getAccountStatements = async (req, res) => {
        try {
            const data = await SAPServiceModel.getStatements(
                req.query,
                req.headers.sapIntegrationCredentials,
            )

            if (!data)
                return res.handler.success("sap_statements_not_found", [])

            res.handler.success(undefined, data)
        }
        catch (error) {
            if (error.name === SAP_SERVICE.ERROR.API) {
                return res.handler.custom(
                    error.statuscode,
                    error.message,
                    undefined,
                    error
                )
            }
            else {
                return res.handler.serverError(error);
            }
        }
    }

    generateAccountStatementReport = async (req, res) => {
        try {
            const payload = await this.generateStatementsPayloadForPdf(
                req.body,
                req.headers.sapIntegrationCredentials,
            )

            if (typeof payload === "string")
                return res.handler.notFound(payload)

            const pdfData = await PDFManager.getStatement(payload)

            res.handler.success(undefined, pdfData)
        }
        catch (error) {
            if (error.name === SAP_SERVICE.ERROR.API) {
                return res.handler.custom(
                    error.statuscode,
                    error.message,
                    undefined,
                    error
                )
            }
            else {
                return res.handler.serverError(error);
            }
        }
    }

    generateStatementsPayloadForPdf = async (
        query,
        sapIntegrationCredentials,
    ) => {
        const {
            tenantId,
            timezone,
            agingSecondaryTitle,
            statementSecondaryTitle,
            toDate
        } = query

        const [
            shippingLabelLogoUrl,
            decimalPoints,
            balance,
            statements
        ] = await Promise.all([
            TenantPortalModal.getShippingLabelLogoUrl(tenantId),
            TenantPortalModal.getAppSettingDecimalPoint(tenantId),
            SAPServiceModel.getBalance(
                {
                    ...query,
                    agingDate: toDate,
                },
                sapIntegrationCredentials,
            ),
            SAPServiceModel.getStatements(
                query,
                sapIntegrationCredentials,
            )
        ])

        if (!statements) {
            return "sap_statements_not_found"
        }

        if (!balance) {
            return "sap_account_balance_not_found"
        }

        statements.statements = statements.statements.reverse()
        statements.statements.unshift({
            document_type: "OB",
            post_date: "",
            remarks: "Opening Balance",
            debit_amount: 0,
            credit_amount: 0,
        })

        let openingBalance = statements.opening_balance

        statements.statements.forEach(statement => {
            if (statement.debit_amount > 0) {
                openingBalance = openingBalance + statement.debit_amount
            }
            else {
                openingBalance = openingBalance - statement.credit_amount
            }

            statement.balance = formatAmount(openingBalance, decimalPoints)
            statement.debit_amount = formatAmount(statement.debit_amount, decimalPoints)
            statement.credit_amount = formatAmount(statement.credit_amount, decimalPoints)
        });

        return {
            aging_secondary_title: agingSecondaryTitle,
            statement_secondary_title: statementSecondaryTitle,
            date: moment(toDate, "YYYYMMDD").format("DD/MM/YYYY"),
            customer_external_id: balance.customer_external_id,
            customer_legal_name: balance.customer_legal_name,
            balance_0_30_days: formatAmount(balance.balance_0_30_days, decimalPoints),
            balance_31_60_days: formatAmount(balance.balance_31_60_days, decimalPoints),
            balance_61_90_days: formatAmount(balance.balance_61_90_days, decimalPoints),
            balance_90_days_above: formatAmount(balance.balance_91_120_days + balance.balance_120_days_above, decimalPoints),
            balance_due: formatAmount(balance.balance_due, decimalPoints),
            logo: shippingLabelLogoUrl,
            transactions: statements.statements
        }
    }

}
