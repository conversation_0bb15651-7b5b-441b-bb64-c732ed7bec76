const HoldReasonTemplateOptionsModel = new (require('../Models/HoldReasonTemplateOptionsModel'))();

class HoldReasonTemplateOptionsController {
    getHoldReasonTemplateOptions = async (req, res) => {
        try {
            const reasons = await HoldReasonTemplateOptionsModel.getHoldReasonTemplateOption();
            return res.handler.success(null, reasons);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

}

module.exports = HoldReasonTemplateOptionsController;
