const { VALUES } = require('../Configs/constants');
const RoleModal = new (require('../Models/roles'));
class RoleController {
    addRole = async (req, res) => {
        try {
            let existingRole;

            switch (req.body.portalType) {
                case VALUES.portals.TENANT_PORTAL:
                    existingRole = await RoleModal.findTenantPortalRole(req.body, req.headers);
                
                case VALUES.portals.BRANCH_PORTAL:
                    existingRole = await RoleModal.findBranchPortalRole(req.body, req.headers);
    
                case VALUES.portals.SYSTEM_PORTAL:
                    existingRole = await RoleModal.findSystemPortalRole(req.body, req.headers);
            
            }

            if(existingRole?.length > 0 && existingRole[0].count > 0) {
                return res.handler.conflict("validation_exists_role_name");
            }
            const role = await RoleModal.addRole(req.body, req.headers);
            await role.save();
            return res.handler.success("created_role");
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    editRole = async (req, res) => {
        try {
            const role = await RoleModal.getRoleFromRoleID(req.body.roleId);
            if(!role) {
                return res.handler.notFound();
            }

            if(!role.is_editable) {
                return res.handler.conflict('validation_invalid_edit_role')
            }

            // let existingRole = await this.getExistingRoleFromName(req);
            let existingRole;
            switch (req.body.portalType) {
                case VALUES.portals.TENANT_PORTAL:
                    existingRole = await RoleModal.findTenantRoleNameExpect(req.body, req.headers);
                
                case VALUES.portals.BRANCH_PORTAL:
                    existingRole = await RoleModal.findBranchRoleNameExpect(req.body, req.headers);
    
                case VALUES.portals.SYSTEM_PORTAL:
                    existingRole = await RoleModal.findSystemRoleExpect(req.body, req.headers);
            
            }

            // if same name role for that portal exists,
            if(existingRole?.length > 0 && existingRole[0].count > 0) {
                return res.handler.conflict("validation_exists_role_name");
            }

            role.name = req.body.name;
            role.description = req.body.description;
            role.updated_by = req.headers.userDetails._id;
            await role.save();
            return res.handler.success("updated_role");
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async editRolePermission(req, res) {
        try {
            const role = await RoleModal.getRoleFromRoleID(req.body.roleId);
            if(!role) {
                return res.handler.notFound();
            }
            if(!role.is_editable) {
                return res.handler.conflict('validation_invalid_delete_role')
            }
            role.permission = req.body.permission
            role.updated_by = req.headers.userDetails._id;
            role.markModified("permission");
            await role.save();
            return res.handler.success("updated_role");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getPortalRoles(req, res) {
        try {

            let portalRoles;
            switch (req.query.portalType) {
                case VALUES.portals.SYSTEM_PORTAL:
                    portalRoles = await RoleModal.getSystemPortalRoles(req.query, req.headers);
                    break;
                case VALUES.portals.TENANT_PORTAL:
                    portalRoles = await RoleModal.getTenantAndBranchPortal(req.query, req.headers);
                    break;
                case VALUES.portals.BRANCH_PORTAL:
                    portalRoles = await RoleModal.getStandardBranchPortalRoles();
                    break;
                case VALUES.portals.SALES_APP:
                    portalRoles = await RoleModal.getSalesAppRoles();
            }

            return res.handler.success(null, portalRoles);
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async MarkRoleDeleted(req, res) {
        try {
            const role = await RoleModal.getRoleFromRoleID(req.query.roleId);
            if(!role) {
                return res.handler.notFound();
            }

            if(!role.is_deleteable) {
                return res.handler.conflict("validation_invalid_delete_role")
            }
            role.is_deleted = true;
            role.updated_by = req.headers.userDetails._id;
            await role.save();
            return res.handler.success();
        } catch(error) {
            return res.handler.serverError(error);
        }
    }

    async getPortalModules(req, res) {
        try {
            const modules = await RoleModal.getPortalModules(req.query.portalType);
            return res.handler.success(null, modules);
        } catch(error) {
            return res.handler.serverError(error);
        }
    }
    
    // async addModule(req, res) {
    //     try {
    //         await RoleModal.addModule(req.body);
    //         return res.handler.success();
    //     } catch(error) {
    //         return res.handler.serverError(error);
    //     }
    // }
}

module.exports = RoleController;