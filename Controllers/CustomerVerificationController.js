const UserAuthModal = new (require("../Models/auth"))()
const CustomerVerificationModal = new (require("../Models/CustomerVerificationModel"))()

const { VERIFY_CUSTOMER_ACTIONS, BY_PASS_OTP } = require("../Configs/constants")
const TenantPortalModal = new (require("../Models/tenantPortal"))()
const { generateOtp } = require("../Utils/helpers")

class CustomerVerificationController {

    async generateNewCustomerOtp(req, res) {
        const { countryCode, mobileNumber } = req.body

        /**
         *  If we have this user's verification info in db then,
         *  remove all the verifications from the db and then
         *  generate new otp.
         */
        await CustomerVerificationModal.deleteCustomerVerifications(req.headers.userroleid, mobileNumber)

        const newOtp = generateOtp()
        let userEmail

        let isEmailSent = false
        let isSmsSent = false

        //FIX:ME revisit flow for sent email
        // try {
        //     const customerDetails = await TenantPortalModal.findTenantCustomerByMobile(
        //         req.body.countryCode,
        //         req.body.mobileNumber
        //     )

        //     if (!customerDetails) {
        //         return res.handler.notFound("customer_not_found")
        //     }

        //     const customerUserRoleDetails = await TenantPortalModal.findUserProfileWithFilter(
        //         {
        //             user_id: customerDetails._id,
        //             tenant_id: req.headers.sessionDetails.tenant_id,
        //         },
        //         {
        //             customer_email: 1,
        //         },
        //         {
        //             lean: true
        //         }
        //     )

        //     userEmail = customerUserRoleDetails?.customer_email

        //     if (userEmail) {
        //         await UserAuthModal.sendEmailOTP(userEmail, newOtp)
        //         isEmailSent = true
        //     }
        // }
        // catch (emailError) {
        //     logger.error(emailError)
        // }

        try {
            await UserAuthModal.sendMobileOTP((countryCode + mobileNumber).replace("+", ""), newOtp)
            isSmsSent = true
        }
        catch (smsError) {
            logger.error(smsError)
        }

        if (!isEmailSent && !isSmsSent) {
            if (userEmail) {
                return res.handler.badRequest("unable_to_send_otp_sms_or_email")
            }
            else {
                return res.handler.badRequest("unable_to_send_otp_sms")
            }
        }

        const response = await CustomerVerificationModal.addCustomerVerification(req, newOtp)
        await response.save()

        return res.handler.success("process_sms_sent")
    }

    async verifyCustomerOtp(req, res) {
        const { mobileNumber, otp, customerUserRoleId } = req.body
        const customerVerification = await CustomerVerificationModal.findCustomerVerification(req.headers.userroleid, mobileNumber)

        if (!customerVerification?._id) {
            return res.handler.notFound("customer_verification_not_found")
        }

        if ([BY_PASS_OTP, customerVerification.otp].includes(otp)) {
            customerVerification.deleteOne()
            if (customerUserRoleId) {
                const customerVerificationUpdate = await TenantPortalModal.getOnlyUserRoleById(customerUserRoleId)
                customerVerificationUpdate.is_verified = true
                customerVerificationUpdate.save()
            }
            return res.handler.success("customer_verification_success")
        }
        else {
            return res.handler.validationError("validation_device_incorrect_otp")
        }
    }

    verifyCustomer = async (req, res) => {
        try {
            const { action } = req.body

            if (action === VERIFY_CUSTOMER_ACTIONS.SEND_OTP) {
                // Generate OTP to verify customer via mobile number.
                return await this.generateNewCustomerOtp(req, res)
            }
            else {
                // Verify customer via otp.
                return await this.verifyCustomerOtp(req, res)
            }
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }
}

module.exports = CustomerVerificationController
