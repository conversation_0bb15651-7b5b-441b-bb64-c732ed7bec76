const express = require('express')
const router = express.Router()

const AuthController = new (require("../Controllers/auth"))();
const AuthValidator = require('./../Middleware/validators/auth');
const Authentication = require('../Middleware/authentication').authentication
const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;

router.route("/testingEmail")
    .get(
        AuthController.testingEmail
    )

router.route("/generateMapFile")
    .get(
        AuthController.generateMapFile
    )

router.route('/signup')
    .post(
        AuthValidator.signup,
        Authentication,
        AuthController.Signup
    )

router.route('/sign-in')
    .post(
        AuthValidator.signIn,
        Authentication,
        AuthController.SignIn
    )

router.route('/app-sign-in')
    .post(
        AuthValidator.appSignIn,
        Authentication,
        AuthController.appSignIn.bind(AuthController)
    )
    
router.route("/app-resend-otp")
    .post(
        AuthValidator.appResendOtp,
        Authentication,
        AuthController.appResendOTP
    )

router.route('/verify-auth-otp')
    .post(
        AuthValidator.verifyAppAuthOtp,
        Authentication,
        AuthController.verifyAppAuthOtp.bind(AuthController)
    )

/**
 * 🚩🚩🚩🚩🚩🚩🚩🚩🚩🚩
 *
 * @caution If anyone changes the route of this api,
 * then check this route related condition elsewhere
 * in codebase and then change at affected places.
 */
router.route('/app-user-roles')
    .post(
        AuthValidator.appUserRoleList,
        Authentication,
        AuthController.appUserRoleList.bind(AuthController)
    )

router.route('/verify-user')
    .post(
        AuthValidator.verifyUserEmail,
        Authentication,
        AuthController.verifyUserOTP
    )

router.route('/resend-otp')
    .post(
        AuthValidator.resendOTP,
        Authentication,
        AuthController.resendOTP
    )

router.route('/forgot-password')
    .post(
        AuthValidator.forgotPassword,
        Authentication,
        AuthController.forgotPassword
    )

router.route('/verify-mobile-forgot-password-otp')
    .post(
        AuthValidator.verifyMobileForgotPasswordOTP,
        Authentication,
        AuthController.verifyMobileForgotPasswordOTP
    )

router.route('/change-password-with-token')
    .post(
        AuthValidator.forgotPasswordVerify,
        Authentication,
        AuthController.verifyForgotPassword
    )

router.route('/check-rest-password-link')
    .post(
        AuthValidator.checkRestPasswordLink,
        Authentication,
        AuthController.checkRestPasswordLink
    )

router.route('/user-role-accessed')
    .post(
        AuthValidator.roleAccessed,
        Authentication,
        DeviceValidator,
        AuthController.userRoleAccessed
    )

router.route('/user-roles')
    .get(
        AuthValidator.userRolesList,
        Authentication,
        DeviceValidator,
        AuthController.userRolesList
    )

router.route('/logout')
    .put(
        AuthValidator.logout,
        Authentication,
        DeviceValidator,
        AuthController.logout
    )

router.route('/token-validity')
    .get(
        AuthValidator.getTokenConfigInfo,
        Authentication,
        DeviceValidator,
        AuthController.getTokenConfigInfo
    )
    .put(
        AuthValidator.updateTokenConfigInfo,
        Authentication,
        DeviceValidator,
        AuthController.updateTokenConfigInfo
    )

router.route("/service-authenticator")
    .get(
        AuthValidator.serviceAuthenticator,
        Authentication,
        DeviceValidator,
        AuthController.serviceAuthenticator
    )

module.exports = router;
