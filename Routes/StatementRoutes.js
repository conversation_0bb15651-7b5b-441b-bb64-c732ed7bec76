const express = require('express')
const router = express.Router()

const StatementController = new (require("../Controllers/StatementController"))()
const StatementValidator = require("../Middleware/validators/StatementValidator")

const Authentication = require('../Middleware/authentication').authentication;

const {
    verifySAPAccess,
} = require('../Middleware/verifyPermission');

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route("/accountBalance")
    .get(
        StatementValidator.getAccountBalance,
        Authentication,
        verifySAPAccess({
            [PERMISSION_MODULES.SAP_SERVICE]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW
        }),
        StatementController.getAccountBalance
    )

router.route("/accountStatements")
    .get(
        StatementValidator.getAccountStatements,
        Authentication,
        verifySAPAccess({
            [PERMISSION_MODULES.SAP_SERVICE]: ACTIONS.VIEW
        }),
        StatementController.getAccountStatements
    )
    .post(
        StatementValidator.generateAccountStatementReport,
        Authentication,
        verifySAPAccess({
            [PERMISSION_MODULES.SAP_SERVICE]: ACTIONS.VIEW
        }),
        StatementController.generateAccountStatementReport
    )

module.exports = router
