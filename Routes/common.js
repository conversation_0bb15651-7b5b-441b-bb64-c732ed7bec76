const express = require('express');
const router = express.Router()

const CommonValidator = require("../Middleware/validators/common");
const Authentication = require('../Middleware/authentication').authentication
const CommonController = new (require("../Controllers/common/common"))();
const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

const {
    verifyPermission,
} = require('../Middleware/verifyPermission');

router.route('/country')
    .post(
        CommonValidator.addCountry,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_COUNTRIES]: ACTIONS.CREATE
        }),
        CommonController.addCountry
    )
    .put(
        CommonValidator.editCountry,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_COUNTRIES]: ACTIONS.EDIT
        }),
        CommonController.editCountry
    )
    .delete(
        CommonValidator.deleteCountry,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_COUNTRIES]: ACTIONS.DELETE
        }),
        CommonController.deleteCountry
    )

router.route('/get-countries')
    .get(
        CommonValidator.getCountries,
        Authentication,
        CommonController.getCountryList
    )

router.route('/country-status')
    .put(
        CommonValidator.changeCountryStatus,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_COUNTRIES]: ACTIONS.EDIT
        }),
        CommonController.changeCountryStatus
    )


router.route('/search-users')
    .get(
        CommonValidator.searchUsers,
        Authentication,
        DeviceValidator,
        CommonController.searchUsers
    )

router.route('/region')
    .post(
        CommonValidator.addRegion,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_REGIONS]: ACTIONS.CREATE
        }),
        CommonController.addRegion
    )
    .get(
        CommonValidator.getRegions,
        Authentication,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_REGIONS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.REGIONS]: ACTIONS.VIEW,
        }),
        CommonController.getRegions
    )
    .put(
        CommonValidator.editRegion,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_REGIONS]: ACTIONS.EDIT
        }),
        CommonController.editRegion
    )
    .delete(
        CommonValidator.deleteRegion,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_REGIONS]: ACTIONS.DELETE
        }),
        CommonController.deleteRegion
    )

router.route('/region-status')
    .put(
        CommonValidator.changeRegionStatus,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_REGIONS]: ACTIONS.EDIT
        }),
        CommonController.changeRegionStatus
    )

router.route('/city')
    .post(
        CommonValidator.addCity,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_CITIES]: ACTIONS.CREATE
        }),
        CommonController.addCity
    )
    .get(
        CommonValidator.getCities,
        Authentication,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_CITIES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.CITIES]: ACTIONS.VIEW,
        }),
        CommonController.getCities
    )
    .put(
        CommonValidator.editCity,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_CITIES]: ACTIONS.EDIT
        }),
        CommonController.editCity
    )

router.route("/automatic-add-city")
    .post(
        CommonValidator.automaticAddCity,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CITIES]: ACTIONS.EDIT,
            [PERMISSION_MODULES.REGIONS]: ACTIONS.EDIT,
        }),
        CommonController.automaticAddCity
    )

router.route('/cities-status')
    .put(
        CommonValidator.changeCitiesStatus,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_CITIES]: ACTIONS.EDIT
        }),
        CommonController.changeCitiesStatus
    )
    .delete(
        CommonValidator.deleteCities,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_CITIES]: ACTIONS.DELETE
        }),
        CommonController.deleteCities
    )

router.route('/language')
    .get(
        CommonValidator.getLanguages,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.LANGUAGES]: ACTIONS.VIEW,
        }),
        CommonController.getLanguages
    )

router.route('/change-user-password')
    .put(
        CommonValidator.changeUserPassword,
        Authentication,
        DeviceValidator,
        CommonController.changeUserPassword
    )


router.route('/user-profile-pic')
    .put(
        upload.any(),
        CommonValidator.changeUserProfilePic,
        Authentication,
        DeviceValidator,
        CommonController.changeUserProfilePic
    )

router.route('/getUploadSignature')
    .post(
        CommonValidator.getUploadSignatureValidator,
        Authentication,
        CommonController.getUploadSignature
    )

router.route('/addImage')
    .post(
        CommonValidator.addImageValidator,
        Authentication,
        CommonController.addImage
    )

router.route('/deleteImage')
    .put(
        CommonValidator.deleteImageValidator,
        Authentication,
        CommonController.deleteImage
    )

/* router.route('/script')
    .post(
        CommonController.script
    ) */

module.exports = router;
