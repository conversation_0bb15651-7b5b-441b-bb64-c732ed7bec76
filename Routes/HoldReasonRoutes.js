const express = require('express')
const router = express.Router();

const HoldReasonController = new (require("../Controllers/HoldReasonController"))();

const Authentication = require('../Middleware/authentication').authentication;
const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;

const HoldReasonValidator = require("../Middleware/validators/HoldReasonValidator");

const {
    verifyPermission
} = require('../Middleware/verifyPermission');

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route('/')
    .post(
        HoldReasonValidator.addHoldReason,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.CREATE
        }),
        HoldReasonController.createHoldReason
    )
    .put(
        HoldReasonValidator.editHoldReason,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.EDIT
        }),
        HoldReasonController.updateHoldReason
    )
    .get(
        HoldReasonValidator.getHoldReasons,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
        }),
        HoldReasonController.getHoldReasons
    )

router.route('/templates')
    .get(
        HoldReasonValidator.getTemplates,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
        }),
        HoldReasonController.getTemplates
    )

router.route('/template')
    .get(
        HoldReasonValidator.getTemplate,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
        }),
        HoldReasonController.getTemplateDetail
    )

router.route('/reasonTemplateWithDetails')
    .get(
        HoldReasonValidator.getReasonTemplateWithDetails,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
        }),
        HoldReasonController.getReasonTemplateWithDetails
    )

module.exports = router;
