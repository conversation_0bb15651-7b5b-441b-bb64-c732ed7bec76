const express = require('express')
const router = express.Router()

const RewardProgramController = new (require("../Controllers/RewardProgram/RewardProgramController"))()
const RewardProgramDashboardController = new (require("../Controllers/RewardProgram/RewardProgramDashboardController"))()
const RewardProgramMemberController = new (require("../Controllers/RewardProgram/RewardProgramMemberController"))()
const RewardProgramPointController = new (require("../Controllers/RewardProgram/RewardProgramPointController"))()
const RewardProgramProductController = new (require("../Controllers/RewardProgram/RewardProgramProductController"))()

const RewardProgramValidator = require("../Middleware/validators/RewardProgram/RewardProgramValidator")
const RewardProgramDashboardValidator = require("../Middleware/validators/RewardProgram/RewardProgramDashboardValidator")
const RewardProgramMemberValidator = require("../Middleware/validators/RewardProgram/RewardProgramMemberValidator")
const RewardProgramPointValidator = require("../Middleware/validators/RewardProgram/RewardProgramPointValidator")
const RewardProgramProductValidator = require("../Middleware/validators/RewardProgram/RewardProgramProductValidator")

const Authentication = require('../Middleware/authentication').authentication;

const InternServiceMiddleWare = require("../Middleware/InternalServiceMiddleware").InternalServiceMiddleware;

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

const {
    verifyRewardProgramAccess,
} = require('../Middleware/verifyPermission');

router.route("/")
    .post(
        RewardProgramValidator.addRewardProgram,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM]: ACTIONS.CREATE
        }),
        RewardProgramController.addRewardProgram
    )
    .put(
        RewardProgramValidator.updateRewardProgram,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM]: ACTIONS.EDIT
        }),
        RewardProgramController.updateRewardProgram
    )
    .get(
        RewardProgramValidator.listRewardProgram,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM]: ACTIONS.VIEW
        }),
        RewardProgramController.listRewardProgram
    )

router.route("/configuration")
    .put(
        RewardProgramValidator.updateRewardProgramConfiguration,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_REWARD_PROGRAM]: ACTIONS.EDIT
        }),
        RewardProgramController.updateRewardProgramConfiguration
    )
    .get(
        RewardProgramValidator.getRewardProgramConfiguration,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_REWARD_PROGRAM]: ACTIONS.VIEW
        }),
        RewardProgramController.getRewardProgramConfiguration
    )

router.route("/pointsSummary")
    .get(
        RewardProgramDashboardValidator.getDashboardStatistics,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM]: ACTIONS.VIEW
        }),
        RewardProgramDashboardController.getDashboardStatistics
    )

router.route("/pointsHistoricDistribution")
    .get(
        RewardProgramDashboardValidator.getDashboardHistoricDistributions,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM]: ACTIONS.VIEW
        }),
        RewardProgramDashboardController.getDashboardHistoricDistributions
    )

router.route("/members")
    .post(
        RewardProgramMemberValidator.enrollMembers,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.CREATE
        }),
        RewardProgramMemberController.enrollMembers
    )
    .get(
        RewardProgramMemberValidator.listMembers,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.VIEW
        }),
        RewardProgramMemberController.listMembers
    )
    .delete(
        RewardProgramMemberValidator.deactivateMembers,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.DELETE
        }),
        RewardProgramMemberController.deactivateMembers
    )

router.route("/member")
    .get(
        RewardProgramMemberValidator.memberDetails,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.VIEW
        }),
        RewardProgramMemberController.memberDetails
    )

router.route("/memberCount")
    .get(
        RewardProgramMemberValidator.countMembers,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.VIEW
        }),
        RewardProgramMemberController.countMembers
    )

router.route("/point")
    .post(
        RewardProgramPointValidator.addManualPoints,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_POINT]: ACTIONS.CREATE
        }),
        RewardProgramPointController.addManualPoints
    )
    .get(
        RewardProgramPointValidator.listPointsLogs,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_POINT]: ACTIONS.VIEW
        }),
        RewardProgramPointController.listPointsLogs
    )

router.route("/dailyAccess")
    .post(
        RewardProgramPointValidator.addDailyAccessPoints,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_POINT]: ACTIONS.CREATE
        }),
        RewardProgramPointController.addDailyAccessPoints
    )

router.route("/memberScan")
    .get(
        RewardProgramPointValidator.getQrCodeForMemberScan,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_POINT]: ACTIONS.CREATE
        }),
        RewardProgramPointController.getQrCodeForMemberScan
    )

//This route only used for faris's internal testing
router.route("/calculator/sap")
    .post(
        RewardProgramPointValidator.calculateSAPPoints,
        InternServiceMiddleWare,
        RewardProgramPointController.calculateSAPPoints
    )
    .get(
        RewardProgramPointValidator.checkSAPPoints,
        InternServiceMiddleWare,
        RewardProgramPointController.checkSAPPoints
    )

router.route("/product")
    .post(
        RewardProgramProductValidator.addRewardProduct,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: ACTIONS.CREATE
        }),
        RewardProgramProductController.addRewardProduct
    )
    .put(
        RewardProgramProductValidator.updateRewardProduct,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: ACTIONS.EDIT
        }),
        RewardProgramProductController.updateRewardProduct
    )
    .patch(
        RewardProgramProductValidator.updateRewardProducts,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: ACTIONS.EDIT
        }),
        RewardProgramProductController.updateRewardProducts
    )
    .get(
        RewardProgramProductValidator.listRewardProduct,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: ACTIONS.VIEW
        }),
        RewardProgramProductController.listRewardProduct
    )
    .delete(
        RewardProgramProductValidator.deleteRewardProduct,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: ACTIONS.DELETE
        }),
        RewardProgramProductController.deleteRewardProduct
    )

router.route("/productTags")
    .get(
        RewardProgramProductValidator.listRewardProductTags,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT]: ACTIONS.VIEW
        }),
        RewardProgramProductController.listRewardProductTags
    )

router.route("/productClaims")
    .post(
        RewardProgramProductValidator.claimRewardProduct,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT_CLAIM]: ACTIONS.CREATE
        }),
        RewardProgramProductController.claimRewardProduct
    )
    .put(
        RewardProgramProductValidator.updateRewardProductClaimsStatus,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT_CLAIM]: ACTIONS.EDIT
        }),
        RewardProgramProductController.updateRewardProductClaimsStatus
    )
    .get(
        RewardProgramProductValidator.listRewardProductClaims,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_PRODUCT_CLAIM]: ACTIONS.VIEW
        }),
        RewardProgramProductController.listRewardProductClaims
    )

module.exports = router
