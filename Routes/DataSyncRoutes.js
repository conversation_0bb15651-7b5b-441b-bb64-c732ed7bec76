const express = require("express")
const router = express.Router()

const Authentication = require("../Middleware/authentication").authentication
const DeviceValidator = require("../Middleware/validateUserSession").validateUserDevice
const DataSyncController = new (require("../Controllers/DataSyncController"))()
const DataSyncValidator = require("../Middleware/validators/DataSyncValidator")

const { verifySettingConfigPermission, verifyPermission } = require("../Middleware/verifyPermission")
const { PERMISSION_MODULES, ACTIONS } = require("../Configs/constants")

router.route("/tenantAppSetting")
    .get(
        DataSyncValidator.getTenantAppSetting,
        Authentication,
        DeviceValidator,
        verifySettingConfigPermission(
            ACTIONS.VIEW,
            {
                [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
                [PERMISSION_MODULES.INVENTORY]: ACTIONS.VIEW,
                [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
                [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW,
            }
        ),
        DataSyncController.getTenantAppSetting
    )

router.route("/userRoleSettings")
    .get(
        DataSyncValidator.getUserRoleSettings,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.USERS]: ACTIONS.VIEW
        }),
        DataSyncController.getUserRoleSettings
    )

router.route("/userRoles")
    .get(
        DataSyncValidator.getUserRoles,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.USERS]: ACTIONS.VIEW
        }),
        DataSyncController.getUserRoles
    )

router.route("/regions")
    .get(
        DataSyncValidator.getRegions,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_REGIONS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.REGIONS]: ACTIONS.VIEW,
        }),
        DataSyncController.getRegions
    )

router.route("/cities")
    .post(
        DataSyncValidator.getCities,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_CITIES]: ACTIONS.VIEW,
            [PERMISSION_MODULES.CITIES]: ACTIONS.VIEW,
        }),
        DataSyncController.getCities
    )

module.exports = router;
