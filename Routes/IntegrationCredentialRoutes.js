const express = require('express')
const router = express.Router();

const IntegrationCredentialController = new (require("../Controllers/IntegrationCredentialController"))();

const Authentication = require('../Middleware/authentication').authentication;
const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;

const IntegrationCredentialValidator = require("../Middleware/validators/IntegrationCredentialValidator");
const CommonValidator = require("../Middleware/validators/CommonValidator");

router.route('/')
    .post(
        IntegrationCredentialValidator.addCredentials,
        Authentication,
        DeviceValidator,
        IntegrationCredentialController.createCredential
    )
    .put(
        IntegrationCredentialValidator.editCredentials,
        Authentication,
        DeviceValidator,
        IntegrationCredentialController.updateCredential
    )
    .get(
        IntegrationCredentialValidator.getCredentials,
        Authentication,
        DeviceValidator,
        IntegrationCredentialController.getCredentials
    )

module.exports = router;
