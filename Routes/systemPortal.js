const express = require('express')
const router = express.Router();

const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;
const Authentication = require('../Middleware/authentication').authentication;
const SystemPortalValidator = require("../Middleware/validators/systemPortal");
const SystemPortalRoleCheckValidator = require("../Middleware/RoleValidators/systemPortalRoleValidators").systemPortalRoleCheckValidator;
const SystemPortalController = new (require("../Controllers/systemPortal"))();

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

const {
    verifyPermission,
} = require('../Middleware/verifyPermission');

router.route('/get-tenant-defaults')
    .get(
        SystemPortalValidator.tenantDefaults,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ],
            [PERMISSION_MODULES.USERS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.getTenantDefaults
    )

router.route('/tenant')
    .post(
        SystemPortalValidator.addTenants,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: ACTIONS.CREATE
        }),
        SystemPortalController.addTenant
    )
    .get(
        SystemPortalValidator.getTenant,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.TENANTS]: ACTIONS.VIEW,
        }),
        SystemPortalController.getTenant
    )
    .put(
        SystemPortalValidator.updateTenant,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: ACTIONS.EDIT
        }),
        SystemPortalController.updateTenant
    )
    .delete(
        SystemPortalValidator.deleteTenant,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: ACTIONS.DELETE
        }),
        SystemPortalController.deleteTenant
    )

router.route('/tenant-branch')
    .post(
        SystemPortalValidator.addBranch,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.addBranch
    )
    .put(
        SystemPortalValidator.editTenantBranch,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.editBranch
    )
    .delete(
        SystemPortalValidator.deleteTenantBranch,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.deleteBranch
    )

router.route('/get-tenants')
    .get(
        SystemPortalValidator.getTenants,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: ACTIONS.VIEW
        }),
        SystemPortalController.getTenants
    )

router.route('/search-tenant')
    .get(
        SystemPortalValidator.searchTenant,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: ACTIONS.VIEW
        }),
        SystemPortalController.searchTenant
    )

router.route('/linked-tenants')
    .put(
        SystemPortalValidator.addLinkedTenant,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.addLinkedTenant
    )
    .delete(
        SystemPortalValidator.removeLinkedTenant,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.removeLinkedTenant
    )

router.route('/change-tenant-owner-password')
    .put(
        SystemPortalValidator.changeTenantOwnerPassword,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.changeTenantOwnerPassword
    )

router.route('/system-users')
    .get(
        SystemPortalValidator.getSystemUsers,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_USERS]: ACTIONS.VIEW
        }),
        SystemPortalController.getSystemUsers
    )
    .put(
        SystemPortalValidator.changeSystemUserStatus,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_USERS]: ACTIONS.EDIT
        }),
        SystemPortalController.changeSystemUserStatus
    )

router.route('/user')
    .get(
        SystemPortalValidator.getUserDetailsWithRole,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        SystemPortalController.getUserDetailsWithRole
    )
    .post(
        upload.any(),
        SystemPortalValidator.addSystemUser,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_USERS]: ACTIONS.CREATE
        }),
        SystemPortalController.addSystemUser
    )
    .put(
        SystemPortalValidator.editSystemUser,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_USERS]: ACTIONS.VIEW
        }),
        SystemPortalController.editSystemUser
    )
    .delete(
        SystemPortalValidator.deleteSystemUser,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_USERS]: ACTIONS.DELETE
        }),
        SystemPortalController.deleteSystemUser
    )

router.route('/assigned-tenant')
    .delete(
        SystemPortalValidator.deleteAssignedTenant,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.deleteAssignedTenant
    )
    .post(
        SystemPortalValidator.addAssignedTenant,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.addAssignedTenant
    )
    .get(
        SystemPortalValidator.getAssignedTenants,
        Authentication,
        DeviceValidator,
        SystemPortalRoleCheckValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_TENANTS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ]
        }),
        SystemPortalController.getAssignedTenants
    )

router.route('/dashboard')
    .get(
        SystemPortalValidator.tenantDefaults,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_DASHBOARD]: ACTIONS.VIEW
        }),
        SystemPortalController.dashboard
    )

router.route('/dashboardSalesState')
    .get(
        SystemPortalValidator.getSalesState,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DASHBOARD]: ACTIONS.VIEW
        }),
        SystemPortalController.getSalesState
    )

module.exports = router;
