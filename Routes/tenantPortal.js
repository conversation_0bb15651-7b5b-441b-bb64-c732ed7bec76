const express = require('express')
const router = express.Router();

const StatementRoutes = require("./StatementRoutes")
const RewardProgramRoutes = require("./RewardProgramRoutes")

const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;
const TenantPortalValidator = require("../Middleware/validators/tenantPortal");
const NotificationValidator = require("../Middleware/validators/NotificationValidator");

const CustomerVerificationValidator = require('../Middleware/validators/CustomerVerificationValidator');

const TenantPortalController = new (require("../Controllers/tenantPortal"))();
const CustomerVerificationController = new (require('../Controllers/CustomerVerificationController'))();
const TenantCustomerController = new (require("../Controllers/TenantCustomerController"))();
const NotificationController = new (require("../Controllers/NotificationController"))();

const Authentication = require('../Middleware/authentication').authentication;
const {
    verifyPermission,
    verifyDataSheetPermission,
    verifySettingConfigPermission,
    verifyRewardProgramAccess
} = require('../Middleware/verifyPermission');

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.use(StatementRoutes);
router.use("/rewardProgram", RewardProgramRoutes);

router.route('/user')
    .post(
        upload.any(),
        TenantPortalValidator.addTenantBranchPortalUser,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.USERS]: ACTIONS.CREATE
        }),
        TenantPortalController.addTenantBranchPortalUser
    )
    .get(
        TenantPortalValidator.getTenantBranchPortalUser,
        Authentication,
        DeviceValidator,
        TenantPortalController.getTenantBranchPortalUser
    )
    .put(
        TenantPortalValidator.editTenantBranchUser,
        Authentication,
        DeviceValidator,
        TenantPortalController.updateTenantBranchPortalUser
    )
    /* .patch(
        TenantPortalValidator.updateUserRoleValidator,
        Authentication,
        DeviceValidator,
        TenantPortalController.updateUserRole
    ) */
    .delete(
        TenantPortalValidator.deleteTenantBranchPortalUser,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.USERS]: ACTIONS.DELETE
        }),
        TenantPortalController.deleteTenantBranchPortalUser
    )

router.route('/customer')
    .post(
        TenantPortalValidator.addCustomer,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.CREATE
        }),
        TenantPortalController.addCustomer
    )
    .get(
        TenantPortalValidator.getCustomer,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.TRACKING]: ACTIONS.VIEW,
        }),
        TenantPortalController.getCustomer
    )
    .put(
        TenantPortalValidator.editCustomer,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.editCustomer
    )
    .delete(
        TenantPortalValidator.deleteCustomer,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.DELETE
        }),
        TenantPortalController.deleteCustomer
    )

router.route('/customerAppAccess')
    .put(
        TenantPortalValidator.updateCustomerAppAccess,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.updateCustomerAppAccess
    )

router.route("/customer/checkExternalId")
    .get(
        TenantPortalValidator.checkExistingExternalId,
        Authentication,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW
        }),
        TenantPortalController.checkExistingExternalId
    )

router.route('/customer-device-access')
    .post(
        TenantPortalValidator.addCustomerDeviceAccess,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.addCustomerDeviceAccess
    )
    .delete(
        TenantPortalValidator.deleteCustomerDeviceAccess,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.deleteCustomerDeviceAccess
    )

router.route('/users')
    .get(
        TenantPortalValidator.listTenantAndBranchUser,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.USERS]: ACTIONS.VIEW
        }),
        TenantPortalController.listTenantAndBranchUser
    )
    .put(
        TenantPortalValidator.changeTenantUserRoleStatus,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.USERS]: ACTIONS.EDIT
        }),
        TenantPortalController.changeTenantUserRoleStatus
    )

router.route('/supervisors')
    .get(
        TenantPortalValidator.getTenantSupervisors,
        Authentication,
        DeviceValidator,
        TenantPortalController.getTenantSupervisors
    )

router.route('/salesperson')
    .get(
        TenantPortalValidator.getTenantSalesperson,
        Authentication,
        DeviceValidator,
        TenantPortalController.getTenantSalesperson
    )

router.route("/usersDetails")
    .post(
        TenantPortalValidator.usersDetails,
        Authentication,
        TenantPortalController.usersDetails
    )

router.route('/branches')
    .get(
        TenantPortalValidator.tenantBranches,
        Authentication,
        DeviceValidator,
        TenantPortalController.tenantNonDeletedBranches
    )

router.route("/customers")
    .get(
        TenantPortalValidator.getCustomers,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW
        }),
        TenantPortalController.getCustomers
    )
    .put(
        TenantPortalValidator.updateCustomersAccesses,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.updateCustomersAccesses
    )
    .post(
        TenantPortalValidator.updateCustomers,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.updateCustomers
    )

router.route("/searchCustomerByMobileNumber")
    .get(
        TenantPortalValidator.searchCustomerByMobileNumber,
        Authentication,
        DeviceValidator,
        TenantCustomerController.searchCustomerByMobileNumber
    )

router.route("/customerCommonAddress")
    .put(
        TenantPortalValidator.addUpdateCustomerShippingAddress,
        Authentication,
        DeviceValidator,
        TenantCustomerController.editCustomerCommonAddress
    )

router.route("/configurations")
    .get(
        TenantPortalValidator.getConfigurations,
        Authentication,
        DeviceValidator,
        verifySettingConfigPermission(
            ACTIONS.VIEW,
            {
                [PERMISSION_MODULES.ORDERS]: ACTIONS.VIEW,
                [PERMISSION_MODULES.INVENTORY]: ACTIONS.VIEW,
                [PERMISSION_MODULES.PRODUCTS]: ACTIONS.VIEW,
                [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.VIEW,
            }
        ),
        TenantPortalController.getConfigurations
    )
    .post(
        TenantPortalValidator.updateShippingLabelValidator,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_SHIPPING_LABEL]: ACTIONS.EDIT
        }),
        TenantPortalController.updateShippingLabel
    )
    .put(
        TenantPortalValidator.updateAppSetting,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.EDIT
        }),
        TenantPortalController.updateAppSetting
    )

router.route("/verifyCustomer")
    .post(
        CustomerVerificationValidator.verifyCustomerValidator,
        Authentication,
        DeviceValidator,
        CustomerVerificationController.verifyCustomer
    )

router.route('/allowNewValidation')  // Dec 30 users and customer limit validation
    .get(
        TenantPortalValidator.allowNewValidation,
        Authentication,
        DeviceValidator,
        TenantPortalController.allowNewValidation
    )

router.route('/userSetting')
    .get(
        TenantPortalValidator.getUserSettings,
        Authentication,
        DeviceValidator,
        TenantPortalController.getUserSettings
    )

router.route('/updateUserSetting')
    .post(
        TenantPortalValidator.updateUserSetting,
        Authentication,
        DeviceValidator,
        TenantPortalController.updateUserSetting
    )

router.route('/dataSheet')
    .post(
        TenantPortalValidator.dataSheet,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET]: ACTIONS.VIEW
        }),
        verifyDataSheetPermission(ACTIONS.VIEW),
        TenantPortalController.dataSheet
    )
    .get(
        TenantPortalValidator.listDataSheet,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET]: ACTIONS.VIEW
        }),
        TenantPortalController.getDataSheetList
    )
    .delete(
        TenantPortalValidator.deleteDataSheetValidator,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET]: ACTIONS.DELETE
        }),
        TenantPortalController.deleteDataSheet
    )

router.route('/columnField')
    .get(
        TenantPortalValidator.columnField,
        Authentication,
        DeviceValidator,
        TenantPortalController.columnField
    )

router.route('/dataSheet/import/getUploadSignature')
    .post(
        TenantPortalValidator.uploadSignatureValidator,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT,
            ]
        }),
        verifyDataSheetPermission(),
        TenantPortalController.getUploadSignature
    )

router.route('/dataSheet/import/updateUploadFile')
    .post(
        TenantPortalValidator.updateUploadFile,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT,
            ]
        }),
        verifyDataSheetPermission(),
        TenantPortalController.updateUploadFile.bind(TenantPortalController)
    )

router.route('/dataSheet/import/validation')
    .post(
        TenantPortalValidator.importValidation,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.DATA_SHEET]: ACTIONS.EDIT
        }),
        TenantPortalController.importValidation.bind(TenantPortalController)
    )

router.route('/eachOtherLinkedTenants')
    .get(
        TenantPortalValidator.tenantsForImageMatch,
        Authentication,
        DeviceValidator,
        TenantPortalController.tenantsForImageMatch
    )

router.route('/allowPayment')
    .put(
        TenantPortalValidator.allowPayment,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.PAYMENTS]: ACTIONS.EDIT,
            [PERMISSION_MODULES.CUSTOMERS]: ACTIONS.EDIT
        }),
        TenantPortalController.allowPayment
    )

router.route('/customerPayment')
    .get(
        TenantPortalValidator.getCustomersForPayment,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.PAYMENTS]: ACTIONS.VIEW,
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TRACKING]: ACTIONS.VIEW
        }),
        TenantPortalController.getCustomersForPayment
    )

router.route('/customerRewardProgram')
    .get(
        TenantPortalValidator.getCustomersForRewardProgram,
        Authentication,
        DeviceValidator,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.CREATE
        }),
        TenantPortalController.getCustomersForRewardProgram
    )

router.route("/paymentTerms")
    .get(
        TenantPortalValidator.listPaymentTerms,
        Authentication,
        verifyRewardProgramAccess({
            [PERMISSION_MODULES.REWARD_PROGRAM_MEMBER]: ACTIONS.CREATE,
            [PERMISSION_MODULES.CUSTOMERS]: [
                ACTIONS.CREATE,
                ACTIONS.EDIT
            ],
        }),
        TenantPortalController.listPaymentTerms
    )

router.route("/scanQrCode")
    .post(
        TenantPortalValidator.scanQrCode,
        Authentication,
        verifyPermission({
            [PERMISSION_MODULES.REWARD_PROGRAM_POINT]: ACTIONS.CREATE,
            [PERMISSION_MODULES.TRACKING]: ACTIONS.EDIT,
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_TRACKING]: ACTIONS.EDIT,
        }),
        TenantPortalController.scanQrCode
    )

router.route('/notifications')
    .get(
        NotificationValidator.listNotifications,
        Authentication,
        DeviceValidator,
        NotificationController.listNotifications
    )
    .patch(
        NotificationValidator.countUnreadNotifications,
        Authentication,
        DeviceValidator,
        NotificationController.countUnreadNotifications
    )

router.route('/schedule-notification')
    .post(
        NotificationValidator.scheduleNotification,
        Authentication,
        NotificationController.scheduleNotification
    )
    .put(
        NotificationValidator.updateScheduledNotification,
        Authentication,
        verifyPermission({
            [PERMISSION_MODULES.DEALS]: ACTIONS.EDIT
        }),
        NotificationController.updateScheduledNotification
    )

module.exports = router;
