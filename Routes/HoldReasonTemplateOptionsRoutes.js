const express = require('express')
const router = express.Router();

const HoldReasonTemplateOptionsController = new (require("../Controllers/HoldReasonTemplateOptionsController"))();

const Authentication = require('../Middleware/authentication').authentication;
const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;

const HoldReasonTemplateOptionsValidator = require("../Middleware/validators/HoldReasonTemplateOptionsValidator");

const {
    verifyPermission
} = require('../Middleware/verifyPermission');

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

router.route('/')
    .get(
        HoldReasonTemplateOptionsValidator.getHoldReasonTemplateOptionsValidator,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SETTINGS_CONFIGURATIONS_APP_SETTING]: ACTIONS.VIEW,
        }),
        HoldReasonTemplateOptionsController.getHoldReasonTemplateOptions
    )
module.exports = router;
