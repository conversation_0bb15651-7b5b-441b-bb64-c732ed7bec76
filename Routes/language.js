const express = require('express');
const router = express.Router()

const LanguageValidator = require("../Middleware/validators/language");
const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;
const Authentication = require('../Middleware/authentication').authentication;

const LanguageController = new (require("../Controllers/language"));

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

const {
    verifyPermission,
} = require('../Middleware/verifyPermission');

router.route("/")
    .post(
        upload.any(),
        LanguageValidator.addLanguage,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGES]: ACTIONS.CREATE
        }),
        LanguageController.addLanguage
    )
    .put(
        upload.any(),
        LanguageValidator.editLanguage,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGES]: ACTIONS.EDIT
        }),
        LanguageController.editLanguage
    )
    // .get(
    //     Authentication,
    //     LanguageController.uploadInitEnFile
    // )

router.route("/sample-csv")
    .get(
        LanguageValidator.sampleCsv,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGE_KEYS]: ACTIONS.VIEW
        }),
        LanguageController.sampleCsv
    )

router.route("/key")
    .post(
        LanguageValidator.addKey,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGE_KEYS]: ACTIONS.CREATE
        }),
        LanguageController.addKey
    )
    .put(
        LanguageValidator.editLanguageKey,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGE_KEYS]: ACTIONS.EDIT
        }),
        LanguageController.editLanguageKey
    )
    .delete(
        LanguageValidator.deleteKey,
        Authentication,
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_LANGUAGE_KEYS]: ACTIONS.DELETE
        }),
        LanguageController.deleteKey
    )

module.exports = router;