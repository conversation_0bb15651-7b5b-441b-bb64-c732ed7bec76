const express = require('express')
const router = express.Router()

const InternalServiceValidator = require("../Middleware/validators/InternalServiceValidator")
const NotificationValidator = require("../Middleware/validators/NotificationValidator");

const InternalServiceController = new (require("../Controllers/InternalServiceController"))();
const NotificationController = new (require("../Controllers/NotificationController"))();
const RewardProgramProductController = new (require("../Controllers/RewardProgram/RewardProgramProductController"))();
const RewardProgramPointController = new (require("../Controllers/RewardProgram/RewardProgramPointController"))()

const InternalServiceMiddleware = require("../Middleware/InternalServiceMiddleware").InternalServiceMiddleware;

router.route("/tenant")
    .get(
        InternalServiceController.getTenantInfo
    )
    .put(
        InternalServiceController.updateTenantInfo
    )

router.route("/tenants")
    .get(
        InternalServiceController.getTenantsInfo
    )

router.route("/countries")
    .get(
        InternalServiceController.getCountries
    )

router.route("/salespersons")
    .get(
        InternalServiceController.getSalespersons
    )

router.route("/tenantAppSettings")
    .get(
        InternalServiceValidator.getByIdValidator,
        InternalServiceController.getTenantAppSettings
    )

router.route("/tenantsAppSettings")
    .get(
        InternalServiceController.getTenantsAppSettings
    )

router.route("/userRoleSettingsCount")
    .get(
        InternalServiceController.getUserRoleSettingsCount
    )

router.route("/userRoleSettings")
    .get(
        InternalServiceController.getUserRoleSettings
    )

router.route("/currentPrefix")
    .get(
        InternalServiceValidator.currentPrefix,
        InternalServiceMiddleware,
        InternalServiceController.findCurrentPrefix
    )

router.route("/sendOrderEmails")
    .post(
        InternalServiceValidator.sendOrderEmails,
        InternalServiceMiddleware,
        InternalServiceController.sendOrderEmails
    )

router.route("/customer")
    .put(
        InternalServiceValidator.updateBulkDataValidator,
        InternalServiceMiddleware,
        InternalServiceController.updateBulkCustomers
    )
    .post(
        InternalServiceController.getCustomer
    )

router.route("/customers")
    .get(
        InternalServiceController.getCustomers
    )

router.route("/customers/checkPreApproved")
    .get(
        InternalServiceController.checkCustomersPreApprovedStatus
    )

router.route("/users")
    .get(
        InternalServiceController.getUsers
    )

router.route("/customersCount")
    .get(
        InternalServiceController.getCustomersCount
    )

router.route("/tenantBranches")
    .get(
        InternalServiceValidator.tenantBranches,
        InternalServiceController.getTenantBranch
    )
    .patch(
        InternalServiceController.getTenantBranches
    )

router.route("/shippingLabel")
    .get(
        InternalServiceController.getShippingLabel
    )

router.route("/checkOrderUsers")
    .get(
        InternalServiceController.checkOrderUsers
    )

router.route("/dataSheet")
    .put(
        InternalServiceValidator.updateBulkDataValidator,
        InternalServiceMiddleware,
        InternalServiceController.updateDataSheetFiles
    )

router.route("/salesPersonsBySupervisorId")
    .get(
        InternalServiceValidator.salesPersonsBySupervisorId,
        InternalServiceMiddleware,
        InternalServiceController.salesPersonsBySupervisorId
    )

router.route("/holdReasonTemplate")
    .get(InternalServiceController.getHoldReasonTemplate)

router.route("/integrationCredential")
    .get(InternalServiceController.getIntegrationCredential)

router.route("/integrationCredentials")
    .get(InternalServiceController.getIntegrationCredentials)

router.route("/notification")
    .post(
        NotificationValidator.createNotifications,
        InternalServiceMiddleware,
        NotificationController.createNotifications
    )

router.route("/rewardProgramProducts")
    .get(
        RewardProgramProductController.findRewardProducts
    )

router.route("/recentlyRestockedItems")
    .get(
        InternalServiceController.getRecentlyRestockedItems
    )

router.route("/fetchPoints")
    .post(
        RewardProgramPointController.fetchPointsThroughSAP,
    )
router.route("/verifyPoints")
    .post(
        RewardProgramPointController.verifyPointsThroughSAP,
    )

module.exports = router
