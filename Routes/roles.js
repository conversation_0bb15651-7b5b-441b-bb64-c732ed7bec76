const express = require('express')
const router = express.Router();

const DeviceValidator = require('../Middleware/validateUserSession').validateUserDevice;
const Authentication = require('../Middleware/authentication').authentication;

const RoleValidator = require('../Middleware/validators/roles');

const RoleController = new (require('../Controllers/roles'))();

const {
    PERMISSION_MODULES,
    ACTIONS
} = require('../Configs/constants');

const {
    verifyPermission,
} = require('../Middleware/verifyPermission');

//TODO: if we start maintaining status (is_active) of the role, then change the user_roles collection accordingly
router.route('/')
    .post(
        RoleValidator.addRole, 
        Authentication, 
        DeviceValidator,
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_ROLES]: ACTIONS.CREATE
        }),
        RoleController.addRole
    )
    .put(
        RoleValidator.editRole, 
        Authentication, 
        DeviceValidator, 
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_ROLES]: ACTIONS.EDIT
        }),
        RoleController.editRole
    )
    .get(
        RoleValidator.getPortalRoles, 
        Authentication, 
        DeviceValidator, 
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_ROLES]: ACTIONS.VIEW
        }),
        RoleController.getPortalRoles
    )
    .delete(
        RoleValidator.MarkRoleDeleted, 
        Authentication, 
        DeviceValidator, 
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_ROLES]: ACTIONS.DELETE
        }),
        RoleController.MarkRoleDeleted
    )

router.route('/edit-role-permission')
    .put(
        RoleValidator.editRolePermission, 
        Authentication, 
        DeviceValidator, 
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_ROLES]: ACTIONS.EDIT
        }),
        RoleController.editRolePermission
    )

router.route('/modules')
    .get(
        RoleValidator.getPortalModules, 
        Authentication, 
        DeviceValidator, 
        verifyPermission({
            [PERMISSION_MODULES.SYSTEM_ROLES]: ACTIONS.VIEW
        }),
        RoleController.getPortalModules
    )

// router.route('/add-module')
//     .post(
//         RoleController.addModule
//     )

module.exports = router;
