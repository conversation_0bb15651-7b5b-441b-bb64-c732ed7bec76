// Add compound index for tenant_id and user_role_id
// This index optimizes the query pattern used in NotificationModel #getMatchCondition method
db.notifications.createIndex(
    {
        "tenant_id": 1,
        "user_role_id": 1
    },
    {
        "name": "tenant_id_user_role_id_asc_idx",
        "background": true,
    }
);

db.notifications.createIndex(
    {
        "user_role_id": 1,
        "is_read": 1
    },
    {
        "name": "user_role_id_is_read_asc_idx",
        "background": true,
    }
);

// Verify the index was created
print("Indexes on notifications collection:");
db.notifications.getIndexes().forEach(function (index) {
    printjson(index);
});

print("\nIndex 'tenant_id_user_role_id_compound_index' has been created successfully!"); 
