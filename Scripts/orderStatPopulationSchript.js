var acceptedOrders = db.tenant_orders.find({ $and: [{ order_status: { $ne: "PENDING" } }, { order_status: { $ne: "CANCELLED" } }] })

acceptedOrders.forEach(aOrder => {
    var salesPersonId = aOrder.sales_user_role_id;
    var customerRoleId = aOrder.customer_user_role_id;

    var salesPersonStats = {
        _id: salesPersonId,
        tenant_id: aOrder.tenant_id,
        $inc: { total_sales: aOrder.total_amount, total_tax: aOrder.total_tax, accepted_order_count: 1 },
    };
    var customerStats = {
        _id: customerRoleId,
        tenant_id: aOrder.tenant_id,
        $inc: { total_sales: aOrder.total_amount, total_tax: aOrder.total_tax, accepted_order_count: 1 },
    };

    db.order_sales_statistics.updateOne({ _id: salesPersonId }, { $set: { _id: salesPersonId, tenant_id: aOrder.tenant_id }, $inc: { total_sales: aOrder.total_amount, total_tax: aOrder.total_tax, accepted_order_count: 1 } }, { upsert: true });
    db.order_sales_statistics.updateOne({ _id: customerRoleId }, { $set: { _id: customerRoleId, tenant_id: aOrder.tenant_id }, $inc: { total_sales: aOrder.total_amount, total_tax: aOrder.total_tax, accepted_order_count: 1 } }, { upsert: true })
});

// =================================================================================

// update tenant_customers with unique_mobile_number

var customers = db.tenant_customers.find({});

customers.forEach(c => {
    db.tenant_customers.updateOne({ _id: c._id }, { $set: { unique_mobile_number: c.country_code + c.mobile_number } })
});


// =================================================================================


const OwnerRole = db.roles.findOne({ name: "Tenant Owner" });

const AdminRole = db.roles.findOne({ name: "Admin" });
const BManagerRole = db.roles.findOne({ name: "Branch Manager" });
const SalesPersonRole = db.roles.findOne({ name: "Sales Person" });
const SupervisorRole = db.roles.findOne({ name: "Supervisor" });

const customerRole = db.roles.findOne({ name: "Customer" });

const users = db.users.find({});
users.forEach((u) => {
    let existingProfile = db.user_roles.findOne({ is_deleted: false, tenant_id: { $ne: null }, user_id: u._id }, { _id: 1, tenant_id: 1, user_id: 1, });
    if (existingProfile) {
        let tenantDetails = db.tenants.findOne({ _id: existingProfile.tenant_id }, { _id: 1, is_deleted: 1 });
        if (!tenantDetails.is_deleted) {
            db.user_roles.updateMany(
                {
                    $and: [
                        { tenant_id: { $ne: null } }, { tenant_id: { $ne: existingProfile.tenant_id } }
                    ],
                    is_deleted: false,
                    user_id: existingProfile.user_id
                },
                {
                    $set: { is_deleted: true }
                }
            );
        } else {
            db.user_roles.updateMany({ tenant_id: tenantDetails._id }, { $set: { is_deleted: true } });
        }
    }

});

// =================================================================================

const drafts = db.drafts.find({});

drafts.forEach(d => {
    db.drafts.updateOne({ _id: d._id }, { $set: { draft_owner_user_role_id: d.sales_user_role_id } })
});

// =================================================================================

var orders = db.tenant_orders.find({ order_creator_user_role_id: null });

orders.forEach((o) => {
    let updateObject = { order_creator_user_role_id: "", order_creator_name: "" };
    if (o.order_app_type === "SALES_APP") {
        updateObject.order_creator_user_role_id = o.sales_user_role_id;
        updateObject.order_creator_name = o.sales_person_name;
    } else if (o.order_app_type === "CUSTOMER_APP") {
        updateObject.order_creator_user_role_id = o.customer_user_role_id;
        updateObject.order_creator_name = o.customer_legal_name;
    }
    db.tenant_orders.updateOne({ _id: o._id }, { $set: updateObject })
});