
module.exports = async function () {
    try {
        const RoleModel = new (require("../Models/roles"))()
        const AuthModel = new (require("../Models/auth"))()
        const TenantPortalModel = new (require("../Models/tenantPortal"))()

        const { toLeanOption } = require("../Utils/helpers");

        // Get customer role
        const customerRole = await RoleModel.getCustomerRole("_id", toLeanOption)

        // Get all customers
        const customers = await AuthModel.findUserRoles(
            {
                role_id: new mongoose.Types.ObjectId(customerRole._id || customerRole),
                is_deleted: false, // Only active customers
            },
            {
                _id: 1,
                tenant_id: 1,
                price_list_id: 1,
                preferred_language: 1,
                customer_name: 1,
                customer_first_name: 1,
                customer_last_name: 1
            }
        )
        logger.info(`Found ${customers.length} customers to update`);

        if (customers.length === 0) {
            logger.info('No customers found to update');
            return;
        }

        // Prepare all update operations data
        const updateOperations = customers.map(userRole => {
            return {
                filter: { _id: `${userRole.tenant_id}_${userRole._id}` },
                updateData: {
                    $set: {
                        _id: `${userRole.tenant_id}_${userRole._id}`,
                        tenant_id: userRole.tenant_id,
                        user_role_id: userRole._id,
                        default_master_price_id: userRole.price_list_id,
                        out_of_stock: {
                            visible: false,
                            searchable: false,
                        },
                        price_change: false,
                        preferred_language: userRole.preferred_language,
                    }
                },
                options: { upsert: true, new: true }
            };
        });

        // Execute all updates in parallel
        logger.info('Starting parallel updates...');
        const results = await Promise.allSettled(
            updateOperations.map(operation =>
                TenantPortalModel.updateUserRoleSetting(
                    operation.filter,
                    operation.updateData,
                    operation.options
                )
            )
        );

        // Analyze results in single loop
        let successfulUpdates = 0;
        let failedUpdates = 0;
        const successList = [];
        const failureList = [];

        results.forEach((result, index) => {
            const customer = customers[index];
            const customerName = customer.customer_name ||
                `${customer.customer_first_name || ''} ${customer.customer_last_name || ''}`.trim() ||
                `Customer ID: ${customer._id}`;

            if (result.status === 'fulfilled') {
                successfulUpdates++;
                successList.push({
                    _id: customer._id,
                    customerName
                });
            }
            else {
                failedUpdates++;
                failureList.push({
                    _id: customer._id,
                    customerName,
                    error: result.reason
                });
                logger.error(`Update failed for ${customerName} (ID: ${customer._id}):`, result.reason);
            }
        });
        logger.info(`Successfully updated ${successfulUpdates} customer settings`);

        if (successfulUpdates > 0) {
            logger.info('Successful updates:', successList.map(s => `${s.customerName} (${s._id})`).join(', '));
        }

        if (failedUpdates > 0) {
            logger.warn(`${failedUpdates} updates failed`);
        }

        logger.info("Final Result", {
            totalCustomers: customers.length,
            successfulUpdates,
            failedUpdates,
            successList,
            failureList
        })
    }
    catch (err) {
        console.error('Error in add PreferredLanguage Into Settings:', err);
    }
};
