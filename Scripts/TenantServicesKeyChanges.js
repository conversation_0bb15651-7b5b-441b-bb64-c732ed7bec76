const paymentService = {
    key: "payments",
    name: "Payments",
    description: "Ability to maintain payment",
    is_custom: false,
    is_active: true,
    is_deleted: false,
    default_value: {
        view: false,
        edit: false,
        delete: false,
        create: false
    },
    translate_key: "Payments"
}

db.tenant_services.insertOne(paymentService)

const keyMapping = {
    "Deals": "deals",
    "Collections": "collections",
    "Quotations": "quotations",
    "Customer App": "customer_app",
    "Push Notifications": "push_notifications",
    "Tracking": "tracking",
    "Payments": "payments"
};

let tenantServiceBulkWrite = [];

db.tenant_services.find().forEach(service => {
    const serviceKey = service.key;

    tenantServiceBulkWrite.push({
        "updateOne": {
            "filter": { "_id": service._id },
            "update": {
                "$set": {
                    "key": keyMapping[serviceKey] || serviceKey
                }
            }
        }
    });
});

const tenantServiceResult = db.tenant_services.bulkWrite(tenantServiceBulkWrite);
print("Bulk update completed. Modified tenant services: " + tenantServiceResult.modifiedCount);

let tenantsBulkWrite = [];

db.tenants.find().forEach(tenant => {
    const updatedServices = tenant.services.map(service => {
        const serviceKey = service.key;
        service.key = keyMapping[serviceKey] || serviceKey;
        return service
    });

    if(!updatedServices.some(service => service.key === paymentService.key)) {
        updatedServices.push({
            key: paymentService.key,
            permission: paymentService.default_value
        })
    }

    tenantsBulkWrite.push({
        "updateOne": {
            "filter": { "_id": tenant._id },
            "update": {
                "$set": {
                    "services": updatedServices
                }
            }
        }
    });
});

const tenantResult = db.tenants.bulkWrite(tenantsBulkWrite);
print("Bulk update completed. Modified tenants: " + tenantResult.modifiedCount);
