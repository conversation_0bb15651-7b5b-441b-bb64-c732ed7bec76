module.exports = async function () {
    const user_role_settings = require("../Database/Schemas/user_role_settings");
    try {
        const docs = await user_role_settings.find({ user_role_id: { $exists: false } });

        if (!docs.length) {
            logger.info('No documents with missing user_role_id found.');
            return
        }

        const bulkOps = docs.map(doc => {
            const parts = doc._id.split('_');
            if (parts.length === 2 && mongoose.Types.ObjectId.isValid(parts[1])) {
                return {
                    updateOne: {
                        filter: { _id: doc._id },
                        update: { $set: { user_role_id: new mongoose.Types.ObjectId(parts[1]) } }
                    }
                };
            }
            else {
                console.warn(`Invalid _id format: ${doc._id}`);
                return null;
            }
        }).filter(Boolean);

        if (bulkOps.length === 0) {
            logger.info('No valid _id format to extract user_role_id.');
            return
        }

        const result = await user_role_settings.bulkWrite(bulkOps);
        logger.info(`Updated ${result.modifiedCount} documents.`);
    }
    catch (err) {
        console.error('Error updating user_role_id:', err);
    }
};
