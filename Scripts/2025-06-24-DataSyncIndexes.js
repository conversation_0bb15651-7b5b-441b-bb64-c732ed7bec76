/**
 * Data Sync Optimized Indexes Script
 * 
 * This script creates optimized database indexes for collections used in data sync operations.
 * The indexes are designed based on the query patterns in DataSyncService, RegionService, and CityService.
 * 
 * USAGE:
 * 1. Run this script in MongoDB shell: mongo your_database Scripts/DataSyncIndexes.js
 * 2. Or execute individual commands in MongoDB Compass or Studio 3T
 * 
 * Query Patterns Analyzed:
 * 1. tenant_app_settings: Filter by tenant_id, sort by updated_at
 * 2. user_role_settings: Filter by tenant_id, sort by created_at/updated_at with cursor pagination
 * 3. user_roles: Filter by tenant_id, role_id, sort by created_at/updated_at with cursor pagination
 *    - For ADDED records: Also filter by is_deleted=false, is_active=true
 *    - For UPDATED records: No is_deleted/is_active filters
 * 4. regions: Filter by country_id, sort by created_at/updated_at with cursor pagination
 *    - For ADDED records: Also filter by is_deleted=false, is_active=true
 *    - For UPDATED records: No is_deleted/is_active filters
 * 5. cities: Filter by region_id, sort by created_at/updated_at with cursor pagination
 *    - For ADDED records: Also filter by is_deleted=false, is_active=true
 *    - For UPDATED records: No is_deleted/is_active filters
 * 
 * NOTE: This is a MongoDB script - the 'db' variable is a global MongoDB shell variable
 * and will be undefined in Node.js context, which is expected behavior.
 */

// ==================== TENANT APP SETTINGS INDEXES ====================

// Primary index for tenant app settings sync
// Query: { tenant_id: tenantId, updated_at: { $gt: lastSyncedAt } }
db.tenant_app_settings.createIndex(
    {
        tenant_id: 1,
        updated_at: 1
    },
    {
        name: "tenant_app_settings_sync_idx",
        background: true
    }
);

// ==================== USER ROLE SETTINGS INDEXES ====================

// Index for updated records sync with cursor pagination
// Query: { tenant_id: tenantId, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.user_role_settings.createIndex(
    {
        tenant_id: 1,
        updated_at: 1,
        _id: 1
    },
    {
        name: "user_role_settings_updated_sync_idx",
        background: true
    }
);

// ==================== USER ROLES INDEXES ====================

// Primary index for user roles sync with cursor pagination (created records)
// Query: { tenant_id: tenantId, is_deleted: false, is_active: true, role_id: { $in: roleIds }, $or: [{ created_at: { $gt: lastSyncedDate } }, { created_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.user_roles.createIndex(
    {
        tenant_id: 1,
        is_deleted: 1,
        is_active: 1,
        updated_at: 1,
        _id: 1,
        role_id: 1,
    },
    {
        name: "user_roles_updated_init_sync_idx",
        background: true
    }
);

// Index for user roles sync with cursor pagination (updated records)
// Query: { tenant_id: tenantId, role_id: { $in: roleIds }, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.user_roles.createIndex(
    {
        tenant_id: 1,
        updated_at: 1,
        _id: 1,
        role_id: 1,
    },
    {
        name: "user_roles_updated_sync_idx",
        background: true
    }
);

// Supporting index for role-based queries
// Query: { name: { $in: roles }, is_deleted: false, is_active: true }
db.roles.createIndex(
    {
        name: 1,
        is_deleted: 1,
        is_active: 1
    },
    {
        name: "roles_name_active_idx",
        background: true
    }
);

// ==================== REGIONS INDEXES ====================

// Primary index for regions sync with cursor pagination (created records)
// Query: { country_id: countryId, is_deleted: false, is_active: true, $or: [{ created_at: { $gt: lastSyncedDate } }, { created_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.regions.createIndex(
    {
        country_id: 1,
        is_deleted: 1,
        is_active: 1,
        updated_at: 1,
        _id: 1
    },
    {
        name: "regions_updated_init_sync_idx",
        background: true
    }
);

// Index for regions sync with cursor pagination (updated records)
// Query: { country_id: countryId, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.regions.createIndex(
    {
        country_id: 1,
        updated_at: 1,
        _id: 1
    },
    {
        name: "regions_updated_sync_idx",
        background: true
    }
);

// ==================== CITIES INDEXES ====================

// Primary index for cities sync with cursor pagination (created records)
// Query: { region_id: { $in: regionIds }, is_deleted: false, is_active: true, $or: [{ created_at: { $gt: lastSyncedDate } }, { created_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.cities.createIndex(
    {
        region_id: 1,
        is_deleted: 1,
        is_active: 1,
        updated_at: 1,
        _id: 1
    },
    {
        name: "cities_updated_init_sync_idx",
        background: true
    }
);

// Index for cities sync with cursor pagination (updated records)
// Query: { region_id: { $in: regionIds }, $or: [{ updated_at: { $gt: lastSyncedDate } }, { updated_at: lastSyncedDate, _id: { $gt: cursor } }] }
db.cities.createIndex(
    {
        region_id: 1,
        updated_at: 1,
        _id: 1
    },
    {
        name: "cities_updated_sync_idx",
        background: true
    }
);
