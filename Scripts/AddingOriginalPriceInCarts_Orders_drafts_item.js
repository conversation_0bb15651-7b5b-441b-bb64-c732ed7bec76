original_price

const cartItems = db.order_cart_items.find({ original_price: null });

cartItems.forEach(c => {
    db.order_cart_items.updateOne({ _id: c._id }, { $set: { original_price: c.base_price + (c.tax || 0) } });
});
const orderItems = db.tenant_order_items.find({ original_price: null });

orderItems.forEach(c => {
    db.tenant_order_items.updateOne({ _id: c._id }, { $set: { original_price: c.base_price + (c.tax || 0) } });
});
const draftItems = db.draft_items.find({ original_price: null });

draftItems.forEach(c => {
    db.draft_items.updateOne({ _id: c._id }, { $set: { original_price: c.base_price + (c.tax || 0) } });
});


// =================================================================================


const types = db["variant_types_2.0"].find({});

types.forEach(t => {
    const product = db["products_2.0"].findOne({ _id: ObjectId(t.product_id) });
    if (!product) {
        db["variant_types_2.0"].deleteOne({ _id: t._id });
    } else {
        db["variant_types_2.0"].updateOne({ _id: t._id }, { $set: { product_id: product._id, tenant_id: product.tenant_id } })
    }
})


// =================================================================================

const parentProducts = db["products_2.0"].find({ type: "PARENT", _id: { $ne: ObjectId("6435027187d2d4694ff89dc3") } });

parentProducts.forEach(pp => {
    const productMasterPriceMap = {};
    const variantProducts = db["products_2.0"].find({ parent_id: pp._id });
    variantProducts.forEach(vp => {
        const varPriceMap = vp.price_mappings;
        for (let i = 0; i < varPriceMap?.length; i++) {
            const element = varPriceMap[i];
            element["product_variant_id"] = vp._id;
            if ((!productMasterPriceMap[element.master_price_id]) || ((element.price > 0) && (productMasterPriceMap[element.master_price_id].price > element.price))) {
                productMasterPriceMap[element.master_price_id] = element;
            }
        }
    });

    const mainProductPriceMapping = []
    for (const key in productMasterPriceMap) {
        mainProductPriceMapping.push(productMasterPriceMap[key]);
    }

    db["products_2.0"].updateOne({ _id: pp._id }, { $set: { price_mappings: mainProductPriceMapping } })
});


// =================================================================================


const settings = db.user_role_settings.find();

settings.forEach(s => {
    const tenantId = Number(s._id.split("_")[0]);
    db.user_role_settings.updateMany({ _id: s._id }, { $set: { tenant_id: tenantId } })
});