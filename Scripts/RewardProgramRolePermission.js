db.roles.updateOne(
    {
        name: "System Owner"
    },
    {
        $set: {
            "permission.reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.settings_configurations_reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_member": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_point": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product_claim": {
                create: true,
                edit: true,
                view: true,
                delete: true
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Super Admin"
    },
    {
        $set: {
            "permission.reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.settings_configurations_reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_member": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_point": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product_claim": {
                create: true,
                edit: true,
                view: true,
                delete: true
            }

        }
    }
)

db.roles.updateOne(
    {
        name: "Account Manager"
    },
    {
        $set: {
            "permission.reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.settings_configurations_reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_member": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_point": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product_claim": {
                create: true,
                edit: true,
                view: true,
                delete: true
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Tenant Owner"
    },
    {
        $set: {
            "permission.reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.settings_configurations_reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_member": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_point": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product_claim": {
                create: true,
                edit: true,
                view: true,
                delete: true
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Admin"
    },
    {
        $set: {
            "permission.reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.settings_configurations_reward_program": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_member": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_point": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.reward_program_product_claim": {
                create: true,
                edit: true,
                view: true,
                delete: true
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Branch Manager"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_point": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: false,
                edit: false,
                view: false,
                delete: false
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Sales Person"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_point": {
                create: true,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: false,
                edit: false,
                view: false,
                delete: false
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Supervisor"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_point": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: false,
                edit: false,
                view: false,
                delete: false
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Customer"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.reward_program_point": {
                create: true,
                edit: false,
                view: true,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: true,
                edit: false,
                view: false,
                delete: false
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Warehouse Clerk"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_point": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: false,
                edit: false,
                view: false,
                delete: false
            }
        }
    }
)

db.roles.updateOne(
    {
        name: "Accountant"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_point": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: false,
                edit: false,
                view: false,
                delete: false
            }
        },
    }
)

db.roles.updateOne(
    {
        name: "Contributor"
    },
    {
        $set: {
            "permission.reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.settings_configurations_reward_program": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_member": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_point": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.reward_program_product_claim": {
                create: false,
                edit: false,
                view: false,
                delete: false
            }
        }
    }
)
