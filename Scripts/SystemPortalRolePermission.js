db.roles.updateMany(
    { "permission.settings": { $exists: true } },
    { $unset: { "permission.settings": "" } }
);

db.roles.updateMany(
    { "permission.localization": { $exists: true } },
    { $unset: { "permission.localization": "" } }
);

db.roles.updateOne(
    {
        name: "System Owner"
    },
    {
        $set: {
            "permission.system_users": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_regions": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_cities": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_countries": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_settings_other": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_languages": {
                create: true,
                edit: true,
                view: true,
                delete: false
            },
            "permission.system_language_keys": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_tenants": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_roles": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Super Admin"
    },
    {
        $set: {
            "permission.system_users": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_regions": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_cities": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_countries": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_settings_other": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_languages": {
                create: true,
                edit: true,
                view: true,
                delete: false
            },
            "permission.system_language_keys": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_tenants": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
            "permission.system_roles": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Account Manager"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: true,
                view: true,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Tenant Owner"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Admin"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Branch Manager"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Sales Person"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: true,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Supervisor"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Customer"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: true,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Warehouse Clerk"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Accountant"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateMany(
    {
        name: "Contributor"
    },
    {
        $set: {
            "permission.system_users": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_regions": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_cities": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_countries": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_dashboard": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_settings_other": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_languages": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_language_keys": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_tenants": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
            "permission.system_roles": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
             "permission.languages": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.tenants": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.regions": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.cities": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
            "permission.countries": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)
