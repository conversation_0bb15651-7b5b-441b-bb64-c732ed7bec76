db.column_fields.updateOne(
    {
        module: "CUSTOMER"
    },
    {
        $set: {
            fields: [
                "External id",
                "Customer Name",
                "Legal Name",
                "Sales Person",
                "Price List",
                "First Name",
                "Last Name",
                "Mobile Number",
                "Email",
                "Customer App Access",
                "Catalog Mode",
                "Preferred Language",
                "Shipping Mobile Number",
                "GPS Coordinate Lat",
                "GPS Coordinate Long",
                "Street Address",
                "Region",
                "City",
                "Active"
            ]
        }
    }
)

db.column_fields.updateOne(
    {
        module: "CUSTOMER_CREATE"
    },
    {
        $set: {
            fields: [
                "External id",
                "Customer Name",
                "Legal Name",
                "Sales Person",
                "Price List",
                "First Name",
                "Last Name",
                "Mobile Number",
                "Email",
                "Customer App Access",
                "Catalog Mode",
                "Preferred Language",
                "Shipping Mobile Number",
                "GPS Coordinate Lat",
                "GPS Coordinate Long"
            ]
        }
    }
)
