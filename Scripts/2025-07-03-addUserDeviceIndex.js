// Add compound index for device_token and user_id
// This index optimizes the query pattern used in validateUserSession middleware
db.user_devices.createIndex(
    {
        "device_token": 1,
        "user_id": 1
    },
    {
        "name": "device_token_user_id_compound_index",
        "background": true,
    }
);

db.user_role_devices.createIndex(
    {
        "user_role_id": 1,
    },
    {
        "name": "user_role_id_asc_idx",
        "background": true,
    }
);

// Verify the index was created
print("Indexes on user_devices collection:");
db.user_devices.getIndexes().forEach(function (index) {
    printjson(index);
});

print("\nIndex 'device_token_user_id_compound_index' has been created successfully!"); 
