/** This will update old shipping label settings */

const SystemPortalController = new (require("../systemPortal"))

const {
    tenants: TenantSchema,
    tenant_branch: TenantBranchSchema,
    shipping_labels: ShippingLabelSchema
} = require('../../Database/Schemas');


router.route('/scripts')
    .post(CommonController.scripts)

async scripts(req, res) {
    try {
        const tenants = await TenantSchema.find({}, "_id legal_name created_by street_name region city")

        for await (let tenant of tenants) {

            const tenantBranches = await TenantBranchSchema.findOne({
                tenant_id: tenant._id,
                name: "Main"
            }, "name")

            if (tenantBranches) {
                const headers = {
                    "userDetails": {
                        "_id": tenant.created_by
                    }
                }

                const ShippingLabel = await ShippingLabelSchema.findOne({ tenant_id: tenant._id })
                if (ShippingLabel) {
                    ShippingLabel.deleteOne()
                }

                await SystemPortalController.addTenantShippingLabel(tenant, [tenantBranches], headers)
            }
        }

        return res.handler.success()
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}
