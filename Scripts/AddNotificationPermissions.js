const roles = db.roles.find(
    {
        name: "Tenant Owner"
    },
    {
        _id: 1,
        name: 1
    }
)

const roleIds = []

roles.forEach(role => {
    roleIds.push(role._id)
});

db.user_roles.updateMany(
    {
        role_id: {
            $in: roleIds,
        },
        $or: [
            { notifications: { $size: 0 } },
            { notifications: { $exists: false } },
        ]
    },
    {
        $set: {
            "notifications": [
                {
                    "allow_push_notification": true,
                    "allow_sms_notification": true,
                    "allow_email_notification": true,
                    "notification_type": "NEW_ORDER"
                },
                {
                    "allow_push_notification": true,
                    "allow_sms_notification": true,
                    "allow_email_notification": true,
                    "notification_type": "ORDER_PREPARING"
                },
                {
                    "allow_push_notification": true,
                    "allow_sms_notification": true,
                    "allow_email_notification": true,
                    "notification_type": "ORDER_SHIPPED"
                },
                {
                    "allow_push_notification": true,
                    "allow_sms_notification": true,
                    "allow_email_notification": true,
                    "notification_type": "ORDER_DELIVERED"
                }
            ],
        }
    }
)
