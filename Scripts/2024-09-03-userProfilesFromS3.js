script = async (req, res) => {
    try {
        const s3Upload = new S3Upload(BUCKET_TYPE.PUBLIC)
        const list = await s3Upload.listAllObjectsFromS3Bucket(FILE_PATH.SYSTEM_USER_PROFILE)

        return res.handler.success(
            null,
            {
                count: list.length,
                Records: list,
            }
        )
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}
