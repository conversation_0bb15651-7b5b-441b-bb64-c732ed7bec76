const TenantPortalModal = new (require("../../Models/tenantPortal"));
const UserRoleSchema = require("../../Database/Schemas/user_roles")

router.route('/scripts')
    .post(CommonController.scripts)

async scripts(req, res) {
    try {
        const customers = await TenantPortalModal.findUserProfilesWithFilter(
            {
                collection_name: 'tenant_customers',
                external_id: {
                    $exists: true,
                    $gt: ""
                },
                is_deleted: false,
            },
            "_id tenant_id external_id unique_external_id customer_id customer_first_name customer_last_name",
        )

        const duplicateExternalIds = {}
        const successExternalIds = {}
        const erroredExternalIds = {}

        for (let i = 0; i < customers.length; i++) {
            const customer = customers[i]
            const { _id, tenant_id, external_id, customer_id, } = customer

            try {
                const updatedResult = await UserRoleSchema.findOneAndUpdate(
                    {
                        _id,
                    },
                    {
                        unique_external_id: tenant_id + "_" + external_id
                    },
                    {
                        new: true,
                        runValidators: true,
                    }
                )

                if (!([customer_id] in successExternalIds)) {
                    successExternalIds[customer_id] = customer
                }
            }
            catch (error) {
                if (error.code === 11000) {
                    if (!([customer_id] in duplicateExternalIds)) {
                        duplicateExternalIds[customer_id] = customer
                    }
                }
                else {
                    if (!([customer_id] in erroredExternalIds)) {
                        erroredExternalIds[customer_id] = { ...customer, error: error.message }
                    }
                }
            }
        }

        return res.handler.success(null,
            {
                successExternalIds,
                duplicateExternalIds,
                erroredExternalIds,
            }
        )
    }
    catch (error) {
        return res.handler.serverError(error)
    }
}
