db.roles.updateOne(
	{
		name: "System Owner"
	},
	{
		$set: {
			permission: {
				datasheet: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_product: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_price: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_inventory: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_customer: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				images: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				products: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				inventory: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				categories: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				dashboard: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				users: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				user_notification: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_account_info: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_shipping_label: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_app_setting: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tax_setting: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tax: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tracking: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_attribute: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_attribute_set: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_price_list: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_brand: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_uom: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				collections: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				deals: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				customers: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				payments: {
					create: false,
					edit: false,
					view: true,
					delete: false
				},
				tracking: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				tenants: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				localization: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				system_users: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Super Admin"
	},
	{
		$set: {
			permission: {
				datasheet: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_product: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_price: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_inventory: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_customer: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				images: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				products: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				inventory: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				categories: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				dashboard: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				users: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				user_notification: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_account_info: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_shipping_label: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_app_setting: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tax_setting: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tax: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tracking: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_attribute: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_attribute_set: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_price_list: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_brand: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_uom: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				collections: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				deals: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				customers: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				payments: {
					create: false,
					edit: false,
					view: false,
					delete: false
				},
				tracking: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				tenants: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				localization: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				system_users: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Account Manager"
	},
	{
		$set: {
			permission: {
				datasheet: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_product: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_price: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_inventory: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_customer: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				images: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				products: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				inventory: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				categories: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				dashboard: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				users: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				user_notification: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_account_info: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_shipping_label: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_app_setting: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tax_setting: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tax: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_tracking: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_attribute: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_attribute_set: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_price_list: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_brand: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				settings_master_data_uom: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				collections: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				deals: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				customers: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				payments: {
					create: false,
					edit: false,
					view: false,
					delete: false
				},
				tracking: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				tenants: {
					view: true,
					edit: true,
					delete: false,
					create: false
				},
				settings: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				localization: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				system_users: {
					create: false,
					edit: false,
					view: false,
					delete: false
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Tenant Owner"
	},
	{
		$set: {
			permission: {
				datasheet: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				images: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				products: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				categories: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				dashboard: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				users: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				collections: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				deals: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				customers: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				payments: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				tracking: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_customer: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_price: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_product: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_account_info: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_app_setting: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_shipping_label: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_tax: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_tax_setting: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_tracking: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_attribute: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_brand: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_price_list: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_uom: {
					view: true,
					edit: true,
					delete: true,
					create: true
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Warehouse Clerk"
	},
	{
		$set: {
			permission: {
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				products: {
					create: false,
					edit: false,
					view: true,
					delete: false
				},
				inventory: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Customer"
	},
	{
		$set: {
			permission: {
				products: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				inventory: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				categories: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				orders: {
					create: true,
					edit: false,
					view: true,
					delete: false
				},
				dashboard: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				users: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				deals: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				collections: {
					view: true,
					edit: true,
					delete: false,
					create: false
				},
				customers: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				payments: {
					view: true,
					edit: true,
					delete: false,
					create: true
				},
				tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			}
		}
	})

db.roles.updateOne(
	{
		name: "Sales Person"
	},
	{
		$set: {
			permission: {
				products: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				inventory: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				categories: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: false
				},
				dashboard: {
					view: true,
					edit: true,
					delete: false,
					create: true
				},
				users: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				customers: {
					view: true,
					edit: true,
					delete: false,
					create: true
				},
				payments: {
					view: true,
					edit: true,
					delete: false,
					create: true
				},
				tracking: {
					view: true,
					edit: true,
					delete: false,
					create: true
				},
				deals: {
					view: true,
					edit: true,
					delete: false,
					create: false
				},
				collections: {
					view: true,
					edit: true,
					delete: false,
					create: false
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Admin"
	},
	{
		$set: {
			permission: {
				datasheet: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				images: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				products: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				categories: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				dashboard: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				users: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				deals: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				collections: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				customers: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				payments: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				tracking: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_customer: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_price: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_product: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_account_info: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_app_setting: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_shipping_label: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_tax: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_tax_setting: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_tracking: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_attribute: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_brand: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_price_list: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data_uom: {
					view: true,
					edit: true,
					delete: true,
					create: true
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Supervisor"
	},
	{
		$set: {
			permission: {
				datasheet: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				images: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				products: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				categories: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				orders: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				dashboard: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				users: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_master_data: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				deals: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				collections: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				customers: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				payments: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				tracking: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				datasheet_customer: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				datasheet_inventory: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				datasheet_price: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				datasheet_product: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Accountant"
	},
	{
		$set: {
			permission: {
				customers: {
					view: true,
					edit: true,
					delete: false,
					create: false
				},
				payments: {
					create: false,
					edit: true,
					view: true,
					delete: true
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: false,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Branch Manager"
	},
	{
		$set: {
			permission: {
				products: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				inventory: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				categories: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				orders: {
					create: false,
					edit: false,
					view: true,
					delete: false
				},
				dashboard: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				users: {
					view: true,
					edit: true,
					delete: false,
					create: true
				},
				region: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				language: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				customers: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				payments: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				datasheet: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				deals: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				collections: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				images: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				datasheet_customer: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_inventory: {
					view: true,
					edit: false,
					delete: true,
					create: true
				},
				datasheet_price: {
					view: true,
					edit: false,
					delete: true,
					create: true
				},
				datasheet_product: {
					view: true,
					edit: false,
					delete: true,
					create: true
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)

db.roles.updateOne(
	{
		name: "Contributor"
	},
	{
		$set: {
			permission: {
				orders: {
					create: false,
					edit: false,
					view: true,
					delete: false
				},
				products: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				categories: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				images: {
					create: true,
					edit: true,
					view: true,
					delete: true
				},
				datasheet_customer: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				datasheet_inventory: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_price: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				datasheet_product: {
					view: true,
					edit: true,
					delete: true,
					create: true
				},
				settings_configurations_account_info: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_app_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_shipping_label: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tax_setting: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_configurations_tracking: {
					view: false,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_attribute_set: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_brand: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_price_list: {
					view: true,
					edit: false,
					delete: false,
					create: false
				},
				settings_master_data_uom: {
					view: true,
					edit: false,
					delete: false,
					create: false
				}
			},
		}
	}
)
