db.roles.updateOne(
    {
        name: "System Owner"
    },
    {
        $set: {
            "permission.sap_service": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Super Admin"
    },
    {
        $set: {
            "permission.sap_service": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },

        }
    }
)

db.roles.updateOne(
    {
        name: "Account Manager"
    },
    {
        $set: {
            "permission.sap_service": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Tenant Owner"
    },
    {
        $set: {
            "permission.sap_service": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Admin"
    },
    {
        $set: {
            "permission.sap_service": {
                create: true,
                edit: true,
                view: true,
                delete: true
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Branch Manager"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Sales Person"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Supervisor"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Customer"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: true,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Warehouse Clerk"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
        }
    }
)

db.roles.updateOne(
    {
        name: "Accountant"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
        },
    }
)

db.roles.updateOne(
    {
        name: "Contributor"
    },
    {
        $set: {
            "permission.sap_service": {
                create: false,
                edit: false,
                view: false,
                delete: false
            },
        }
    }
)
