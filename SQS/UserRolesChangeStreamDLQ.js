const AWS = require('aws-sdk');
const { Consumer } = require('sqs-consumer');

const { sendEmail } = require('../Configs/mailer');

const {
    EMAIL_ID,
} = require("../Configs/constants")

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const initializeUserRolesChangeStreamDLQ = () => {
    const handleDLQ = async (event) => {
        const payload = {
            from: EMAIL_ID.NOTIFICATION,
            to: EMAIL_ID.FARIS,
            cc: [
                EMAIL_ID.BRIJESH,
                EMAIL_ID.DEEP,
            ],
            subject: "Failed: User role change stream",
            data: {
                html: `
                Unable to handle change stream for user role.
                <br/><br/>

                <b> Environment: </b> ${process.env.ENVIRONMENT}
                <br/><br/>

                <b> Tenant Id: </b> ${event.fullDocument.tenant_id}
                <br/><br/>

                <b> Reason: </b> 
                Queues can't change stream event successfully.
                <br/>
                `
            },
        }

        await sendEmail(
            payload.to,
            payload.subject,
            payload.data,
            payload.cc,
            payload.from
        )
    }

    const consumer = Consumer.create({
        queueUrl: process.env.USER_ROLES_CHANGE_STREAM_DLQ_URL,
        sqs,
        handleMessageBatch: async (messages) => {
            if (messages.length) {
                const tasks = messages.map(message => {
                    const event = JSON.parse(message.Body)
                    return handleDLQ(event)
                })

                const results = await Promise.allSettled(tasks)
                results.forEach(result => {
                    if (result.reason) {
                        logger.error(result.reason)
                    }
                })
            }
        },
        batchSize: 5,
    })

    consumer.on('error', (err) => {
        logger.error(err)
    });

    consumer.on('processing_error', (err) => {
        logger.error(err)
    });

    consumer.on('timeout_error', (err) => {
        logger.error(err)
    });

    consumer.start();
}

module.exports = {
    initializeUserRolesChangeStreamDLQ,
};
