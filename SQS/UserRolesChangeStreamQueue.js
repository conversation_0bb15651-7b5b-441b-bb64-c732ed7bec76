const AWS = require('aws-sdk');
const { Consumer } = require('sqs-consumer');

const UserRoleModel = new(require("../Models/UserRoleModel"));

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const initializeUserRolesChangeStreamReceiver = () => {
    const consumer = Consumer.create({
        queueUrl: process.env.USER_ROLES_CHANGE_STREAM_SQS_URL,
        sqs,
        handleMessageBatch: async (messages) => {
            if (messages.length) {                                
                const tasks = messages.map(message => {
                    const event = JSON.parse(message.Body)
                    return UserRoleModel.performChangeStream(event)
                })

                const results = await Promise.allSettled(tasks)
                results.forEach(result => {
                    if (result.reason) {
                        logger.error(result.reason)
                    }
                })
            }
        },
        batchSize: 10,
    })

    consumer.on('error', (err) => {
        logger.error(err)
    });

    consumer.on('processing_error', (err) => {
        logger.error(err)
    });

    consumer.on('timeout_error', (err) => {
        logger.error(err)
    });

    consumer.start();
}

module.exports = {
    initializeUserRolesChangeStreamReceiver,
};
