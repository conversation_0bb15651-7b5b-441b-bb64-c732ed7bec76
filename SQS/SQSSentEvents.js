const AWS = require("aws-sdk");

const sqs = new AWS.SQS(
    {
        accessKeyId: process.env.AWS_ACCESS_KEY,
        secretAccessKey: process.env.AWS_SECRET_KEY,
        region: process.env.AWS_REGION,
    }
);

const sentEffectiveRewardMemberToSQS = (event) => {
    return sqs.sendMessage(
        {
            QueueUrl: process.env.EFFECTIVE_REWARD_MEMBER_QUEUE_URL,
            MessageBody: JSON.stringify(event),
        }
    ).promise()
}

const sentUserRolesChangeStreamToSQS = (event) => {        
    return sqs.sendMessage(
        {
            QueueUrl: process.env.USER_ROLES_CHANGE_STREAM_SQS_URL,
            MessageBody: JSON.stringify(event),
            MessageGroupId: event.fullDocument.tenant_id.toString(),
        }
    ).promise()
}

module.exports = {
    sentEffectiveRewardMemberToSQS,
    sentUserRolesChangeStreamToSQS
};
