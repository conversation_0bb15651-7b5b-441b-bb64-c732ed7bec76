# Hawak DataSync API Documentation for React Native with Realm Integration

This documentation provides comprehensive DataSync API integration details for React Native developers using Realm database with `realm` and `@realm/react` packages.

## Base Configuration

```typescript
const BASE_URL = "https://your-api-domain.com"; // Replace with your actual API domain
```

## Authentication Headers

All dataSync endpoints require the following headers:

```typescript
interface AuthHeaders {
  "Content-Type": "application/json";
  authorization: string; // JWT access token
  userroleid: string; // User role ID
  devicetoken: string; // Device token
  devicetype: "IOS" | "ANDROID"; // Device type
  deviceaccesstype: string; // Device access type
}
```

## Data Synchronization APIs

### 1. Get Tenant App Settings

**API URL:** `GET /dataSync/tenantAppSetting`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetTenantAppSettingRequest {
  tenantId: string;
  type: string; // Must be "APP_SETTING"
}
```

**API Response Type:**

```typescript
interface GetTenantAppSettingResponse {
  message: string;
  data: {
    _id: string;
    tenant_id: number;
    quantity_label: number;
    consider_new_item: number;
    price_change: boolean;
    hide_out_of_stock_product: boolean;
    reduce_inventory: boolean;
    customer_app_access: boolean;
    catalog_mode: boolean;
    customer_auto_catalog_mode: {
      enabled: boolean;
      duration: number;
    };
    preferred_language: string;
    decimal_points: number;
    payment_voucher_whatsapp_notification: boolean;
    created_at: string;
    updated_at: string;
  };
}
```

**Realm Object Schema:**

```typescript
class TenantAppSetting extends Realm.Object<TenantAppSetting> {
  _id!: Realm.BSON.ObjectId;
  settingId!: string;
  tenantId!: number;
  quantityLabel!: number;
  considerNewItem!: number;
  priceChange!: boolean;
  hideOutOfStockProduct!: boolean;
  reduceInventory!: boolean;
  customerAppAccess!: boolean;
  catalogMode!: boolean;
  customerAutoCatalogModeEnabled!: boolean;
  customerAutoCatalogModeDuration!: number;
  preferredLanguage!: string;
  decimalPoints!: number;
  paymentVoucherWhatsappNotification!: boolean;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "TenantAppSetting",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      settingId: "string",
      tenantId: "int",
      quantityLabel: "int",
      considerNewItem: "int",
      priceChange: "bool",
      hideOutOfStockProduct: "bool",
      reduceInventory: "bool",
      customerAppAccess: "bool",
      catalogMode: "bool",
      customerAutoCatalogModeEnabled: "bool",
      customerAutoCatalogModeDuration: "int",
      preferredLanguage: "string",
      decimalPoints: "int",
      paymentVoucherWhatsappNotification: "bool",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 2. Get User Role Settings

**API URL:** `GET /dataSync/userRoleSettings`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetUserRoleSettingsRequest {
  tenantId: string;
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // For pagination
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetUserRoleSettingsResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      tenant_id: number;
      user_role_id: string;
      default_master_price_id?: string;
      out_of_stock: {
        visible: boolean;
        searchable: boolean;
      };
      price_change: boolean;
      preferred_language?: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class UserRoleSetting extends Realm.Object<UserRoleSetting> {
  _id!: Realm.BSON.ObjectId;
  settingId!: string;
  tenantId!: number;
  userRoleId!: string;
  defaultMasterPriceId?: string;
  outOfStockVisible!: boolean;
  outOfStockSearchable!: boolean;
  priceChange!: boolean;
  preferredLanguage?: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "UserRoleSetting",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      settingId: "string",
      tenantId: "int",
      userRoleId: "string",
      defaultMasterPriceId: "string?",
      outOfStockVisible: "bool",
      outOfStockSearchable: "bool",
      priceChange: "bool",
      preferredLanguage: "string?",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 3. Get User Roles (Data Sync)

**API URL:** `GET /dataSync/userRoles`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetUserRolesDataSyncRequest {
  tenantId: string;
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // MongoDB ObjectId for pagination
  isInitialSync?: boolean; // true for first sync
  roles: string[]; // Array of role names
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetUserRolesDataSyncResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      collection_name: string;
      customer_app_access?: boolean;
      customer_email?: string;
      customer_first_name?: string;
      customer_last_name?: string;
      customer_legal_name?: string;
      external_id?: string;
      branch_id?: string;
      is_active: boolean;
      is_deleted: boolean;
      allow_price_change?: boolean;
      preferred_language?: string;
      role_id: string;
      tenant_id: number;
      user_id: {
        _id: string;
        first_name: string;
        last_name: string;
        email: string;
        mobile_number: number;
        country_code: string;
      };
      shipping_address?: string;
      shipping_city_id?: string;
      shipping_country_code?: string;
      shipping_region_id?: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class DataSyncUserRole extends Realm.Object<DataSyncUserRole> {
  _id!: Realm.BSON.ObjectId;
  userRoleId!: string;
  collectionName!: string;
  customerAppAccess?: boolean;
  customerEmail?: string;
  customerFirstName?: string;
  customerLastName?: string;
  customerLegalName?: string;
  externalId?: string;
  branchId?: string;
  isActive!: boolean;
  isDeleted!: boolean;
  allowPriceChange?: boolean;
  preferredLanguage?: string;
  roleId!: string;
  tenantId!: number;
  userId!: string;
  userFirstName!: string;
  userLastName!: string;
  userEmail!: string;
  userMobileNumber!: number;
  userCountryCode!: string;
  shippingAddress?: string;
  shippingCityId?: string;
  shippingCountryCode?: string;
  shippingRegionId?: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "DataSyncUserRole",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      userRoleId: "string",
      collectionName: "string",
      customerAppAccess: "bool?",
      customerEmail: "string?",
      customerFirstName: "string?",
      customerLastName: "string?",
      customerLegalName: "string?",
      externalId: "string?",
      branchId: "string?",
      isActive: "bool",
      isDeleted: "bool",
      allowPriceChange: "bool?",
      preferredLanguage: "string?",
      roleId: "string",
      tenantId: "int",
      userId: "string",
      userFirstName: "string",
      userLastName: "string",
      userEmail: "string",
      userMobileNumber: "int",
      userCountryCode: "string",
      shippingAddress: "string?",
      shippingCityId: "string?",
      shippingCountryCode: "string?",
      shippingRegionId: "string?",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 4. Get Regions

**API URL:** `GET /dataSync/regions`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetRegionsRequest {
  countryId: string; // MongoDB ObjectId
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // For pagination
  isInitialSync?: boolean; // true for first sync
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetRegionsResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      country_id: string;
      name: string;
      secondary_language_name: string;
      code?: string;
      is_active: boolean;
      is_deleted: boolean;
      secondary_language_code?: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class Region extends Realm.Object<Region> {
  _id!: Realm.BSON.ObjectId;
  regionId!: string;
  countryId!: string;
  name!: string;
  secondaryLanguageName!: string;
  code?: string;
  isActive!: boolean;
  isDeleted!: boolean;
  secondaryLanguageCode?: string;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "Region",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      regionId: "string",
      countryId: "string",
      name: "string",
      secondaryLanguageName: "string",
      code: "string?",
      isActive: "bool",
      isDeleted: "bool",
      secondaryLanguageCode: "string?",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

### 5. Get Cities

**API URL:** `POST /dataSync/cities`

**API Headers:**

```typescript
{
  'Content-Type': 'application/json',
  'authorization': string,
  'userroleid': string,
  'devicetoken': string,
  'devicetype': 'IOS' | 'ANDROID',
  'deviceaccesstype': string
}
```

**API Request Type:**

```typescript
interface GetCitiesRequest {
  regionIds: string[]; // Array of MongoDB ObjectIds
  lastSyncedAt?: string; // ISO date string
  cursor?: string; // For pagination
  isInitialSync?: boolean; // true for first sync
  perPage?: number; // Default: 50
}
```

**API Response Type:**

```typescript
interface GetCitiesResponse {
  message: string;
  data: {
    count: number;
    list: Array<{
      _id: string;
      country_id: string;
      region_id: string;
      name: string;
      secondary_language_name: string;
      is_active: boolean;
      is_deleted: boolean;
      created_at: string;
      updated_at: string;
    }>;
  };
}
```

**Realm Object Schema:**

```typescript
class City extends Realm.Object<City> {
  _id!: Realm.BSON.ObjectId;
  cityId!: string;
  countryId!: string;
  regionId!: string;
  name!: string;
  secondaryLanguageName!: string;
  isActive!: boolean;
  isDeleted!: boolean;
  createdAt!: Date;
  updatedAt!: Date;

  static schema: Realm.ObjectSchema = {
    name: "City",
    primaryKey: "_id",
    properties: {
      _id: "objectId",
      cityId: "string",
      countryId: "string",
      regionId: "string",
      name: "string",
      secondaryLanguageName: "string",
      isActive: "bool",
      isDeleted: "bool",
      createdAt: "date",
      updatedAt: "date",
    },
  };
}
```

## Implementation Examples

### Realm Configuration

```typescript
import Realm from "realm";
import {
  TenantAppSetting,
  UserRoleSetting,
  DataSyncUserRole,
  Region,
  City,
} from "./schemas";

const realmConfig: Realm.Configuration = {
  schema: [TenantAppSetting, UserRoleSetting, DataSyncUserRole, Region, City],
  schemaVersion: 1,
  migration: (oldRealm, newRealm) => {
    // Handle schema migrations here
  },
};

export const getRealm = async (): Promise<Realm> => {
  return await Realm.open(realmConfig);
};
```

### API Service Implementation

```typescript
import axios, { AxiosInstance } from "axios";
import { Platform } from "react-native";

class DataSyncApiService {
  private api: AxiosInstance;
  private baseURL = "https://your-api-domain.com";

  constructor() {
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
    });
  }

  setAuthHeaders(token: string, userRoleId: string, deviceToken: string) {
    this.api.defaults.headers.common = {
      "Content-Type": "application/json",
      authorization: token,
      userroleid: userRoleId,
      devicetoken: deviceToken,
      devicetype: Platform.OS === "ios" ? "IOS" : "ANDROID",
      deviceaccesstype: "MOBILE_APP",
    };
  }

  async getTenantAppSetting(
    params: GetTenantAppSettingRequest
  ): Promise<GetTenantAppSettingResponse> {
    const response = await this.api.get("/dataSync/tenantAppSetting", {
      params,
    });
    return response.data;
  }

  async getUserRoleSettings(
    params: GetUserRoleSettingsRequest
  ): Promise<GetUserRoleSettingsResponse> {
    const response = await this.api.get("/dataSync/userRoleSettings", {
      params,
    });
    return response.data;
  }

  async getUserRoles(
    params: GetUserRolesDataSyncRequest
  ): Promise<GetUserRolesDataSyncResponse> {
    const response = await this.api.get("/dataSync/userRoles", { params });
    return response.data;
  }

  async getRegions(params: GetRegionsRequest): Promise<GetRegionsResponse> {
    const response = await this.api.get("/dataSync/regions", { params });
    return response.data;
  }

  async getCities(data: GetCitiesRequest): Promise<GetCitiesResponse> {
    const response = await this.api.post("/dataSync/cities", data);
    return response.data;
  }
}

export const dataSyncApiService = new DataSyncApiService();
```

### Data Sync Service with Realm Integration

```typescript
import { getRealm } from "./realmConfig";
import { dataSyncApiService } from "./dataSyncApiService";

class DataSyncService {
  private realm: Realm | null = null;

  async initializeRealm() {
    this.realm = await getRealm();
  }

  async syncTenantAppSettings(tenantId: string) {
    try {
      const response = await dataSyncApiService.getTenantAppSetting({
        tenantId,
        type: "APP_SETTING",
      });

      if (response.data && this.realm) {
        this.realm.write(() => {
          this.realm!.create(
            "TenantAppSetting",
            {
              _id: new Realm.BSON.ObjectId(),
              settingId: response.data._id,
              tenantId: response.data.tenant_id,
              quantityLabel: response.data.quantity_label,
              considerNewItem: response.data.consider_new_item,
              priceChange: response.data.price_change,
              hideOutOfStockProduct: response.data.hide_out_of_stock_product,
              reduceInventory: response.data.reduce_inventory,
              customerAppAccess: response.data.customer_app_access,
              catalogMode: response.data.catalog_mode,
              customerAutoCatalogModeEnabled:
                response.data.customer_auto_catalog_mode.enabled,
              customerAutoCatalogModeDuration:
                response.data.customer_auto_catalog_mode.duration,
              preferredLanguage: response.data.preferred_language,
              decimalPoints: response.data.decimal_points,
              paymentVoucherWhatsappNotification:
                response.data.payment_voucher_whatsapp_notification,
              createdAt: new Date(response.data.created_at),
              updatedAt: new Date(response.data.updated_at),
            },
            Realm.UpdateMode.Modified
          );
        });
      }
    } catch (error) {
      console.error("Error syncing tenant app settings:", error);
      throw error;
    }
  }

  async syncUserRoles(
    tenantId: string,
    roles: string[],
    isInitialSync = false
  ) {
    try {
      const response = await dataSyncApiService.getUserRoles({
        tenantId,
        roles,
        isInitialSync,
        perPage: 100,
      });

      if (response.data.list && this.realm) {
        this.realm.write(() => {
          response.data.list.forEach((userRole) => {
            this.realm!.create(
              "DataSyncUserRole",
              {
                _id: new Realm.BSON.ObjectId(),
                userRoleId: userRole._id,
                collectionName: userRole.collection_name,
                customerAppAccess: userRole.customer_app_access,
                customerEmail: userRole.customer_email,
                customerFirstName: userRole.customer_first_name,
                customerLastName: userRole.customer_last_name,
                customerLegalName: userRole.customer_legal_name,
                externalId: userRole.external_id,
                branchId: userRole.branch_id,
                isActive: userRole.is_active,
                isDeleted: userRole.is_deleted,
                allowPriceChange: userRole.allow_price_change,
                preferredLanguage: userRole.preferred_language,
                roleId: userRole.role_id,
                tenantId: userRole.tenant_id,
                userId: userRole.user_id._id,
                userFirstName: userRole.user_id.first_name,
                userLastName: userRole.user_id.last_name,
                userEmail: userRole.user_id.email,
                userMobileNumber: userRole.user_id.mobile_number,
                userCountryCode: userRole.user_id.country_code,
                shippingAddress: userRole.shipping_address,
                shippingCityId: userRole.shipping_city_id,
                shippingCountryCode: userRole.shipping_country_code,
                shippingRegionId: userRole.shipping_region_id,
                createdAt: new Date(userRole.created_at),
                updatedAt: new Date(userRole.updated_at),
              },
              Realm.UpdateMode.Modified
            );
          });
        });
      }
    } catch (error) {
      console.error("Error syncing user roles:", error);
      throw error;
    }
  }

  async syncRegions(countryId: string, isInitialSync = false) {
    try {
      const response = await dataSyncApiService.getRegions({
        countryId,
        isInitialSync,
        perPage: 100,
      });

      if (response.data.list && this.realm) {
        this.realm.write(() => {
          response.data.list.forEach((region) => {
            this.realm!.create(
              "Region",
              {
                _id: new Realm.BSON.ObjectId(),
                regionId: region._id,
                countryId: region.country_id,
                name: region.name,
                secondaryLanguageName: region.secondary_language_name,
                code: region.code,
                isActive: region.is_active,
                isDeleted: region.is_deleted,
                secondaryLanguageCode: region.secondary_language_code,
                createdAt: new Date(region.created_at),
                updatedAt: new Date(region.updated_at),
              },
              Realm.UpdateMode.Modified
            );
          });
        });
      }
    } catch (error) {
      console.error("Error syncing regions:", error);
      throw error;
    }
  }

  async syncCities(regionIds: string[], isInitialSync = false) {
    try {
      const response = await dataSyncApiService.getCities({
        regionIds,
        isInitialSync,
        perPage: 100,
      });

      if (response.data.list && this.realm) {
        this.realm.write(() => {
          response.data.list.forEach((city) => {
            this.realm!.create(
              "City",
              {
                _id: new Realm.BSON.ObjectId(),
                cityId: city._id,
                countryId: city.country_id,
                regionId: city.region_id,
                name: city.name,
                secondaryLanguageName: city.secondary_language_name,
                isActive: city.is_active,
                isDeleted: city.is_deleted,
                createdAt: new Date(city.created_at),
                updatedAt: new Date(city.updated_at),
              },
              Realm.UpdateMode.Modified
            );
          });
        });
      }
    } catch (error) {
      console.error("Error syncing cities:", error);
      throw error;
    }
  }
}

export const dataSyncService = new DataSyncService();
```

## Usage Example

```typescript
import { dataSyncService, dataSyncApiService } from "./services";

// Initialize the service
async function initializeDataSync() {
  // Set authentication headers
  dataSyncApiService.setAuthHeaders(
    "your-jwt-token",
    "user-role-id",
    "device-token"
  );

  // Initialize Realm
  await dataSyncService.initializeRealm();

  // Perform initial sync
  await dataSyncService.syncTenantAppSettings("your-tenant-id");
  await dataSyncService.syncUserRoles(
    "your-tenant-id",
    ["SALESPERSON", "CUSTOMER"],
    true
  );
  await dataSyncService.syncRegions("country-id", true);
  await dataSyncService.syncCities(["region-id-1", "region-id-2"], true);
}

// Call this function when your app starts
initializeDataSync().catch(console.error);
```
