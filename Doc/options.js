module.exports = {
	"summary": "CORS support",
	"description": "Default response for CORS method",
	"responses": {
		"200": {
			"description": "200 response",
			"content": {},
			"headers": {
				"Access-Control-Allow-Origin": {
					"schema": {
						"type": "string"
					}
				},
				"Access-Control-Allow-Methods": {
					"schema": {
						"type": "string"
					}
				},
				"Access-Control-Allow-Headers": {
					"schema": {
						"type": "string"
					}
				},
				"refreshed-access-token": {
					"schema": {
						"type": "string"
					}
				}
			}
		}
	},
	"x-amazon-apigateway-integration": {
		"responses": {
			"default": {
				"statusCode": "200",
				"responseParameters": {
					"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT,PATCH'",
					"method.response.header.Access-Control-Allow-Headers": "'userroleid,Expires,cache-control,refreshToken,devicetype,devicetoken,deviceaccesstype,version,build,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'",
					"method.response.header.Access-Control-Allow-Origin": "'*'"
				},
				"responseTemplates": {
					"application/json": ''
				}
			}
		},
		"requestTemplates": {
			"application/json": "{\"statusCode\": 200}"
		},
		"passthroughBehavior": "when_no_match",
		"type": "mock"
	}
};

// module.exports = {
// 	"consumes": [
// 		"application/json"
// 	],
// 	"produces": [
// 		"application/json"
// 	],
// 	"responses": {
// 		"200": {
// 			"description": "200 response",
// 			"schema": {
// 				"$ref": "#/definitions/Empty"
// 			},
// 			"headers": {
// 				"Access-Control-Allow-Origin": {
// 					"type": "string"
// 				},
// 				"Access-Control-Allow-Methods": {
// 					"type": "string"
// 				},
// 				"Access-Control-Allow-Headers": {
// 					"type": "string"
// 				}
// 			}
// 		}
// 	},
// 	"x-amazon-apigateway-integration": {
// 		"responses": {
// 			"default": {
// 				"statusCode": "200",
// 				"responseParameters": {
// 					"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'",
// 					"method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'",
// 					"method.response.header.Access-Control-Allow-Origin": "'*'"
// 				}
// 			}
// 		},
// 		"requestTemplates": {
// 			"application/json": "{\"statusCode\": 200}"
// 		},
// 		"passthroughBehavior": "when_no_match",
// 		"type": "mock"
// 	}
// };