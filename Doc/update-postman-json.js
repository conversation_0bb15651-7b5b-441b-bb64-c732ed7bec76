const path = require('path');
const fs = require('fs');
const headers = [
    {
        "key": "Authorization",
        "value": "{{Authorization}}",
        "type": "text"
    },
    {
        "key": "devicetoken",
        "value": "{{devicetoken}}",
        "type": "text"
    },
    {
        "key": "refreshToken",
        "value": "{{refreshToken}}",
        "type": "text"
    },
    {
        "key": "userroleid",
        "value": "{{userRoleId}}",
        "type": "text"
    },
    {
        "key": "deviceaccesstype",
        "value": "{{devicetype}}",
        "type": "text"
    }
];

const hawakPostManJson = require(__dirname + '/development' + '/hawak-postman.json');

const updatedPostmanJsonFileOutPut = __dirname + `/development/updated-hawak-postman-apis.json`;



const items = hawakPostManJson.item;

function updateAPIHeaders(items) {
    if (Array.isArray(items)) {
        items.forEach(it => {
            if (Array.isArray(it.item)) {
                updateAPIHeaders(it.item)
            } else if (it.request?.header) {
                it.request.header = headers;
            }
        });
    } else if (it.request?.headers) {
        it.request.header = headers;
    }
}

updateAPIHeaders(items);
fs.writeFileSync(updatedPostmanJsonFileOutPut, JSON.stringify(hawakPostManJson))