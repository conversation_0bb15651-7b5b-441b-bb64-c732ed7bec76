const fs = require('fs');
const path = require('path');
const postmanToOpenApi = require('postman-to-openapi');
const apiGatewayIntegration = require('./apigateway-integration');
const optionsReq = require('./options');

const { VALUES } = require('../Configs/constants');

const environment = VALUES.ENVIRONMENT

const postmanCollection = path.resolve(__dirname + `/${environment}` + '/hawak-postman.json');
const swaggerOutputFile = path.resolve(__dirname + `/${environment}` + '/openapi.json');
const apiGatewayOutputFile = path.resolve(__dirname + `/${environment}` + '/apigateway-rest-apis.json');

const options = {
    defaultTag: 'General',
    servers: [
        {
            url: process.env.BASE_URL,
            description: `${environment} environment server`
        }
    ],
    outputFormat: 'json',
    info: {
        title: process.env.AWS_API_GATEWAY_SERVICE_NAME,
        description: `${environment} environment server`,
        version: new Date().toISOString()
    },
}

postmanToOpenApi(postmanCollection, swaggerOutputFile, options)
    .then(result => {
        logger.info(`OpenAPI success`);
        try {
            let collection = JSON.parse(result);
            const allAPIs = Object.keys(collection.paths);

            // collection.host = process.env.AWS_API_GATEWAY_HOST;
            // collection.basePath = `/${environment}`;
            // collection.schemes = ['https', 'http'];
            // delete collection.tags;

            allAPIs.forEach((api, index) => {
                const allMethods = Object.keys(collection.paths[api])
                allMethods.map((method) => {
                    // Add api gateway configuration into all methods
                    collection.paths[api][method] = {
                        ...collection.paths[api][method],
                        ...apiGatewayIntegration(api, method)
                    };
                });

                // Add options request in all api for api gateway
                collection.paths[api]['options'] = optionsReq;

                if (index === (allAPIs.length - 1)) {
                    const updateData = JSON.stringify(collection, null, 4);
                    // Create or update json for api gateway
                    fs.writeFileSync(apiGatewayOutputFile, updateData);
                    logger.info(`ApiGateway json created`);
                }
            });
        }
        catch (error) {
            logger.error(error, {
                errorMessage: `Convert to API gateway json failed -> ${error.message}`
            });
        }
    })
    .catch(err => {
        logger.error(err, {
            errorMessage: `OpenAPI error -> ${err.message}`
        });
    })
