const axios = require("axios")

const {
    arrayBufferToBase64,
} = require("../Utils/helpers")

const {
    REPORTS,
    VALUES
} = require("../Configs/constants");

module.exports = class {

    constructor() {
        this.baseUrl = VALUES.jsReportBaseURL
        this.token = Buffer.from(`${VALUES.jsReportUsername}:${VALUES.jsReportPassword}`, "utf-8").toString("base64")
    }

    #getPDFData = async (payload) => {
        const response = await axios({
            url: this.baseUrl,
            responseType: 'arraybuffer',
            method: 'post',
            headers: {
                "Authorization": `Basic ${this.token}`,
                "Accept": "application/pdf",
            },
            data: payload
        })

        return response.data
    }

    #getPDFInBase64 = async (payload) => {
        const pdf = await this.#getPDFData(payload)
        return arrayBufferToBase64(pdf)
    }

    getStatement = async (pdfData) => {
        const payload = {
            template: {
                name: REPORTS.TEMPLATE.ACCOUNT_STATEMENT
            },
            data: pdfData
        }

        return await this.#getPDFInBase64(payload)
    }

}
