const NotificationModel = new (require("../Models/Notification/NotificationModel"))();
const UserRoleDeviceModel = new (require("../Models/UserRoleDeviceModel"))();

const {
    LANGUAGE,
    PROMISE_STATES
} = require("../Configs/constants")

const { getFirebaseAdmin } = require("../Configs/firebase")

class NotificationManager {

    sendNotification = async (
        tokens = [],
        title,
        message,
        notificationLanguage = LANGUAGE.EN,
        payloadData
    ) => {
        const result = {
            successTokens: [],
            failedTokens: [],
            invalidTokens: []
        }

        if (!tokens.length) { return result }

        const admin = getFirebaseAdmin()
        if (!admin) { return result }

        Object.keys(payloadData).forEach(key => {
            if (typeof payloadData[key] === "number") {
                payloadData[key] = String(payloadData[key])
            }
            else if (typeof payloadData[key] !== "string") {
                delete payloadData[key]
            }
        })

        const {
            type
        } = payloadData

        let notificationTitle = title,
            notificationBody = message;

        if (notificationLanguage === LANGUAGE.AR) {
            notificationTitle = payloadData.secondaryLanguageTitle;
            notificationBody = payloadData.secondaryLanguageMessage;
        }

        const batchResponse = await admin.messaging().sendEachForMulticast({
            data: {
                title,
                message,
                ...payloadData,
            },
            notification: {
                title: notificationTitle,
                body: notificationBody,
            },
            tokens,
            fcmOptions: {
                analyticsLabel: type
            },
            android: {
                priority: "high"
            },
            apns: {
                headers: {
                    "apns-priority": "5"
                },
            },
        })

        for (let i = 0; i < batchResponse.responses.length; i++) {
            const response = batchResponse.responses[i]
            const token = tokens[i]

            if (response.success) {
                result.successTokens.push(token)
            }
            else {
                logger.error(response.error)

                if (
                    [
                        "messaging/invalid-registration-token",
                        "messaging/registration-token-not-registered"
                    ].includes(response.error.code)
                ) {
                    result.invalidTokens.push(token)
                }
                else {
                    result.failedTokens.push(token)
                }
            }
        }

        return result
    }

    sendPushNotificationWithUserRoleIds = async (
        notificationInfos = [],
        type,
        data,
        threadId,
        session
    ) => {
        const summary = {
            success: [],
            failed: []
        }

        if (!notificationInfos.length) {
            return summary
        }

        const userRoleIds = notificationInfos.map(notificationInfo => {
            return new mongoose.Types.ObjectId(notificationInfo.userRoleId)
        })

        const [
            tokenData,
            unreadCountData
        ] = await Promise.all([
            UserRoleDeviceModel.getFcmTokensByUserRoleIds(
                userRoleIds,
                session
            ),
            NotificationModel.getUnreadNotificationsCountByUserRoleIds(
                userRoleIds,
                session
            )
        ])

        const pushNotificationResult = await Promise.allSettled(
            notificationInfos.map(notificationInfo => {
                const {
                    userRoleId,
                    title,
                    message,
                    secondaryLanguageTitle,
                    secondaryLanguageMessage,
                    notificationLanguage
                } = notificationInfo

                const payloadData = {
                    type,
                    badge: String(unreadCountData[userRoleId.toString()] ?? 0),
                    secondaryLanguageTitle,
                    secondaryLanguageMessage,
                    "thread-id": threadId,
                    ...data,
                }

                return this.sendNotification(
                    tokenData[userRoleId.toString()],
                    title,
                    message,
                    notificationLanguage,
                    payloadData
                )
            })
        )

        const deleteTokenList = []

        for (let i = 0; i < pushNotificationResult.length; i++) {
            const result = pushNotificationResult[i];
            const userRoleId = notificationInfos[i].userRoleId;

            if (result.status === PROMISE_STATES.REJECTED) {
                logger.error(result.reason)
                summary.failed.push(userRoleId)
            }
            else {
                const {
                    successTokens,
                    failedTokens,
                    invalidTokens
                } = result.value

                if (successTokens.length) {
                    summary.success.push(userRoleId)
                }
                else if (failedTokens.length) {
                    summary.failed.push(userRoleId)
                }

                if (invalidTokens.length) {
                    deleteTokenList.push(...invalidTokens)
                }
            }
        }

        if (deleteTokenList.length) {
            await UserRoleDeviceModel.deleteDevicesByFcmTokens(deleteTokenList, session)
        }
        return summary
    }
}

module.exports = NotificationManager
