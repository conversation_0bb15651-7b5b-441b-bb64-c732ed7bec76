{"swagger": "2.0", "info": {"description": "staging user service for hawak.io", "version": "2022-08-09T13:23:50Z", "title": "staging_user_service"}, "host": "jxdyvef8c6.execute-api.me-south-1.amazonaws.com", "basePath": "/staging", "schemes": ["https", "http"], "paths": {"/auth": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/change-password-with-token": {"post": {"produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/change-password-with-token", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods,devicetoken,Authorization'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/forgot-password": {"post": {"produces": ["application/json"], "parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/forgot-password", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/resend-otp": {"post": {"produces": ["application/json"], "parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/resend-otp/", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/sign-in": {"post": {"produces": ["application/json"], "parameters": [{"name": "devicetype", "in": "header", "required": true, "type": "string"}, {"name": "devicetoken", "in": "header", "required": false, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/sign-in", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetype,devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/verify-mobile-forgot-password-otp": {"post": {"produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/verify-mobile-forgot-password-otp", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/verify-user": {"post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/verify-user", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/auth/user-role-accessed": {"post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/auth/user-role-accessed", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/common/country": {"post": {"produces": ["application/json"], "parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/common/country", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"produces": ["application/json"], "parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/country", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "delete": {"produces": ["application/json"], "parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/common/country", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/get-countries": {"get": {"produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/common/get-countries", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}}, "/common/country-status": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/country-status", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/search-users": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/common/search-users", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/region": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "countryId", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/common/region", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/common/region", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/region", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/common/region", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/region-status": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/region-status", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/city": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "countryId", "in": "query", "required": true, "type": "string"}, {"name": "regionId", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/common/city", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/common/city", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/city", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/cities-status": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/common/cities-status", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/cities-status", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "common/language": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "countryId", "in": "query", "required": true, "type": "string"}, {"name": "regionId", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/common/language", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/change-user-password": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/change-user-password", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/common/user-profile-pic": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/common/user-profile-pic", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/role": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}, {"name": "portalType", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/role", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/role", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/role", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}, {"name": "roleId", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/role", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/role/edit-role-permission": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/role/edit-role-permission", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/role/modules": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}, {"name": "portalType", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/role/modules", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/get-tenant-defaults": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/get-tenant-defaults", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/tenant": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}, {"name": "tenantId", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/system-portal/tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/system-portal/tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/system-portal/tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/get-tenants": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/get-tenants", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/tenant-branch": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/system-portal/tenant-branch", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/system-portal/tenant-branch", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/system-portal/tenant-branch", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/search-tenant": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/search-tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/linked-tenants": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/system-portal/linked-tenants", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/system-portal/linked-tenants", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/change-tenant-owner-password": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/system-portal/change-tenant-owner-password", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/system-users": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}, {"name": "portalType", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/system-users", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}, {"name": "portalType", "in": "query", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/system-portal/system-users", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/user": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/user", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/system-portal/user", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "put": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "PUT", "uri": "https://staging-api.hawak.io/system-portal/user", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/system-portal/user", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}, "/system-portal/assigned-tenant": {"options": {"consumes": ["application/json"], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}, "headers": {"Access-Control-Allow-Origin": {"type": "string"}, "Access-Control-Allow-Methods": {"type": "string"}, "Access-Control-Allow-Headers": {"type": "string"}}}}, "x-amazon-apigateway-integration": {"responses": {"default": {"statusCode": "200", "responseParameters": {"method.response.header.Access-Control-Allow-Methods": "'POST,GET,DELETE,OPTIONS,PUT'", "method.response.header.Access-Control-Allow-Headers": "'devicetoken,Authorization,Content-Type,x-location-id,x-tenant-id,x-company-id,x-access-token,X-Requested-With,Access-Control-Allow-Origin,Access-Control-Allow-Headers.Access-Control-Allow-Methods'", "method.response.header.Access-Control-Allow-Origin": "'*'"}}}, "requestTemplates": {"application/json": "{\"statusCode\": 200}"}, "passthroughBehavior": "when_no_match", "type": "mock"}}, "delete": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "DELETE", "uri": "https://staging-api.hawak.io/system-portal/assigned-tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "post": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "POST", "uri": "https://staging-api.hawak.io/system-portal/assigned-tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}, "get": {"parameters": [{"name": "devicetoken", "in": "header", "required": true, "type": "string"}, {"name": "Authorization", "in": "header", "required": true, "type": "string"}], "produces": ["application/json"], "responses": {"200": {"description": "200 response", "schema": {"$ref": "#/definitions/Empty"}}}, "x-amazon-apigateway-integration": {"httpMethod": "GET", "uri": "https://staging-api.hawak.io/system-portal/assigned-tenant", "responses": {"default": {"statusCode": "200"}}, "passthroughBehavior": "when_no_match", "type": "http_proxy"}}}}, "definitions": {"Empty": {"type": "object", "title": "Empty Schema"}}}